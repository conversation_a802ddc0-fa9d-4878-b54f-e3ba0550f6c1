<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yy.hd</groupId>
    <artifactId>hd_api</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>common/hd_common_room_api</module>
        <module>common/hd_common_game_api</module>
        <module>common/hd_common_game_match_api</module>

        <module>client/hd_client_api</module>
        <module>client/hd_client_base_api</module>
        <module>client/hd_client_game_api</module>
        <module>client/hd_client_room_api</module>
        <module>client/hd_client_game_match_api</module>
        <module>client/hd_client_zhuiya_api</module>
        <module>client/hd_client_sports_api</module>

        <module>server/hd_server_api</module>
        <module>server/hd_server_base_api</module>
        <module>server/hd_server_game_api</module>
        <module>server/hd_server_game_match_api</module>
        <module>server/hd_server_room_api</module>
        <module>server/hd_server_seat_api</module>
        <module>server/hd_server_zhuiya_api</module>
        <module>server/hd_server_broadcast_api</module>

        <module>external/hd_external_api</module>
        <module>external/hd_external_cul_api</module>
        <module>external/hd_external_cul_event_api</module>
        <module>external/hd_external_chanif_api</module>
        <module>external/hd_external_secuserinfo_api</module>
        <module>external/hd_external_webdb_api</module>
        <module>external/hd_external_common_config_api</module>

        <module>fts/hd_external_fts_api</module>
        <module>fts/hd_external_fts_base_api</module>
        <module>fts/hd_external_fts_anticheat_api</module>
        <module>fts/hd_external_fts_compere_tier_api</module>
        <module>fts/hd_external_fts_component_api</module>
        <module>fts/hd_external_fts_currency_api</module>
        <module>fts/hd_external_fts_fragment_api</module>
        <module>fts/hd_external_fts_game_center_api</module>
        <module>fts/hd_external_fts_grade_api</module>
        <module>fts/hd_external_fts_lbs_api</module>
        <module>fts/hd_external_fts_moduleswitch_api</module>
        <module>fts/hd_external_fts_noble_api</module>
        <module>fts/hd_external_fts_privilege_api</module>
        <module>fts/hd_external_fts_userinfo_api</module>
        <module>fts/hd_external_fts_video_fight_mix_api</module>
        <module>fts/hd_external_fts_game_match_api</module>
        <module>fts/hd_external_fts_room_manager_api</module>

        <module>turnover/hd_external_turnover_base_api</module>
        <module>turnover/hd_external_turnover_props_api</module>
        <module>turnover/hd_external_turnover_service_api</module>
        <module>turnover/hd_external_turnover_channel_account_api</module>
        <module>turnover/hd_external_turnover_product_api</module>
        <module>turnover/hd_external_turnover_contract_api</module>
        <module>turnover/hd_external_turnover_bag_api</module>
        <module>turnover/hd_external_turnover_family_api</module>
        <module>turnover/hd_external_turnover_finance_api</module>
        <module>turnover/hd_external_turnover_finance_common_api</module>
        <module>turnover/hd_external_turnover_finance_revenue_v2_api</module>
        <module>turnover/hd_external_turnover_finance_props_stat_v2_api</module>
        <module>turnover/hd_external_turnover_finance_function_user_white_api</module>
        <module>turnover/hd_external_turnover_finance_currency_real_support_api</module>
        <module>turnover/hd_external_turnover_finance_data_center_statistics_api</module>
        <module>turnover/hd_external_turnover_finance_props_v2_api</module>
        <module>turnover/hd_external_turnover_finance_activity_config_api</module>
        <module>turnover/hd_external_turnover_finance_product_v2_api</module>
        <module>turnover/hd_external_turnover_finance_dating_seal_stat_api</module>
        <module>turnover/hd_external_turnover_finance_dating_garden_stat_api</module>
        <module>turnover/hd_external_turnover_finance_dating_stat_api</module>
        <module>turnover/hd_external_turnover_finance_udb_risk_gift_send_api</module>
        <module>turnover/hd_external_turnover_finance_revenue_daren_v2_api</module>
        <module>turnover/hd_external_turnover_im_api</module>
        <module>turnover/hd_external_turnover_api</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>21</java.version>
        <protobuf.version>3.25.5</protobuf.version>
        <thrift.version>0.11.0</thrift.version>
        <dubbo.yrpc.version>1.0.5.76-RELEASE</dubbo.yrpc.version>
        <commons-protopack.version>2.6.0</commons-protopack.version>
        <lombok.version>1.18.30</lombok.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yy.ent.commons</groupId>
            <artifactId>commons-protopack</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yy.ent.commons</groupId>
                <artifactId>commons-protopack</artifactId>
                <version>${commons-protopack.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.yrpc.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>clients-daemon</artifactId>
                        <groupId>com.yy.ent.clients</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-web</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo-rpc-redis</artifactId>
                        <groupId>org.apache.dubbo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo-serialization-ankathrift</artifactId>
                        <groupId>org.apache.dubbo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo-serialization-hessian2</artifactId>
                        <groupId>org.apache.dubbo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo-rpc-ankathrift</artifactId>
                        <groupId>org.apache.dubbo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo-registry-zookeeper</artifactId>
                        <groupId>org.apache.dubbo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo-configcenter-zookeeper</artifactId>
                        <groupId>org.apache.dubbo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo-registry-consul</artifactId>
                        <groupId>org.apache.dubbo</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.thrift</groupId>
                <artifactId>libthrift</artifactId>
                <version>${thrift.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
                <optional>true</optional>
            </dependency>

            <!-- hd_api all sub module start -->
            <!-- Common modules -->
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_common_room_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_common_game_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_common_game_match_api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Client modules -->
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_client_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_client_base_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_client_game_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_client_room_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_client_game_match_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_client_zhuiya_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_client_sports_api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Server modules -->
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_server_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_server_base_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_server_game_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_server_game_match_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_server_room_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_server_seat_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_server_zhuiya_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_server_broadcast_api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- External modules -->
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_cul_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_cul_event_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_chanif_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_secuserinfo_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_webdb_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_common_config_api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- FTS modules -->
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_base_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_anticheat_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_compere_tier_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_component_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_currency_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_fragment_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_game_center_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_grade_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_lbs_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_moduleswitch_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_noble_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_privilege_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_userinfo_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_video_fight_mix_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_game_match_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_fts_room_manager_api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Turnover modules -->
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_base_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_props_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_service_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_channel_account_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_product_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_contract_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_bag_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_family_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_common_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_revenue_v2_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_props_stat_v2_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_function_user_white_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_currency_real_support_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_data_center_statistics_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_props_v2_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_activity_config_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_product_v2_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_dating_seal_stat_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_dating_garden_stat_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_dating_stat_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_udb_risk_gift_send_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_finance_revenue_daren_v2_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_im_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>hd_external_turnover_api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- hd_api all sub module end -->
        </dependencies>
    </dependencyManagement>

    <build>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.0</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>0.6.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- required: os-maven-plugin -->
                    <protocArtifact>com.google.protobuf:protoc:${protobuf.version}:exe:${os.detected.classifier}</protocArtifact>
                    <checkStaleness>true</checkStaleness>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.thrift.tools</groupId>
                <artifactId>maven-thrift-plugin</artifactId>
                <version>0.1.11</version>
                <configuration>
                    <generator>java</generator>
                </configuration>
                <executions>
                    <execution>
                        <id>thrift-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>yyent-release</id>
            <name>yyent-release Repository</name>
            <url>https://nexus.yy.com/music/content/repositories/yyent-release/</url>
        </repository>
        <snapshotRepository>
            <id>yyent</id>
            <name>yyent Repository</name>
            <url>https://nexus.yy.com/music/content/repositories/yyent/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>public</id>
            <name>YYEnt Public Repositories</name>
            <url>https://nexus.yy.com/music/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>YYEnt Public Repositories</name>
            <url>https://nexus.yy.com/music/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>