namespace java com.yy.hd.api.thrift.turnover.channel.account

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"

typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TCurrencyType TCurrencyType
typedef turnover_common.TAppId TAppId
typedef turnover_common.TAccountOperateType TAccountOperateType

struct TUserChannelAccount {
  1: i64 id;
  2: i64 uid;
  3: i64 sid;
  4: i32 appid;
  5: TCurrencyType currencyType;
  6: i64 amount;
  7: i64 totalAmount;
  8: i64 version;
  9: string expand;
}

struct TUserChannelAccountHistory {
  1: i64 id;
  2: i64 accountId;
  3: i64 uid;
  4: i64 sid;
  5: TCurrencyType currencyType;
  6: i64 amountOrig;
  7: i64 amountChange;
  8: TAccountOperateType optType;
  9: i64 optTime;
  10: string description;
  11: string userIp;
  12: i32 appid;
  13: string expand;
}
struct TUserChannelAccountHistoryPage {
  1: list<TUserChannelAccountHistory> records;
  2: i32 total;
}

struct TUserChannelAccountPage {
  1: list<TUserChannelAccount> records;
  2: i32 total;
}

// s2sname: to_service/to_service_pre
service TUserChannelAccountService {
  i64 decrUserChannelAccountWithOperateTypeAndDescription(1: i64 uid, 2: i32 appid, 3: i64 sid, 4: TCurrencyType currencyType, 5: i64 deltaAmount, 6: TAccountOperateType operateType, 7: string description, 8: string userIp, 9: string expand) throws (1: TServiceException ex1);
  TUserChannelAccount getUserChannelAccountByUidAndSid(1: i64 uid, 2: TAppId appid, 3: i64 sid, 4: TCurrencyType currencyType);
  TUserChannelAccountHistoryPage getUserChannelAccountHistoryPage(1: i64 uid, 2: TAppId appid, 3: TCurrencyType currencyType, 4: TAccountOperateType operateType, 5: i64 sid, 6: i32 start, 7: i32 limit, 8: i64 startTime, 9: i64 endTime, 10: string expand);
  TUserChannelAccountPage getUserChannelAccountPage(1: i64 uid, 2: TAppId appid, 3: TCurrencyType currencyType, 4: i32 start, 5: i32 limit);
  i64 incrUserChannelAccountWithOperateTypeAndDescription(1: i64 uid, 2: i32 appid, 3: i64 sid, 4: TCurrencyType currencyType, 5: i64 deltaAmount, 6: TAccountOperateType operateType, 7: string description, 8: string userIp, 9: string expand) throws (1: TServiceException ex1);

/**
   * 查询用户频道账户
   * @param appid 业务ID
   * @param sid 频道id
   * @param uid 用户id
   * @param currencyType 货币类型
   * @throws ServiceException
   */
  TUserChannelAccountPage queryUserChannelAccountPage(1: TAppId appid, 2: i64 sid, 3: i64 uid, 4: TCurrencyType currencyType, 5: i32 page, 6: i32 pageSize) throws (1: TServiceException ex1);

  /**
   * 频道代充剩余额度
   * @param appid 业务ID
   * @param sid 频道id
   * @param placeholderContext 占位符，yrpc bug 请求发不出去，要连续的参数才行，这里设置为 null 就行
   * @param currencyType 货币类型
   * @throws ServiceException
   */
  i64 queryChannelReplaceChargeRemain(1: TAppId appid, 2: i64 sid, 3:string placeholderContext, 4: TCurrencyType currencyType) throws (1: TServiceException ex1);

}
