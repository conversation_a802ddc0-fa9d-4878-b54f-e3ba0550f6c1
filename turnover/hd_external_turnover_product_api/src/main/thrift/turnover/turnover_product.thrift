namespace java com.yy.hd.api.thrift.turnover.product

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"

typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TAppId TAppId

struct TCurrencyBean {
  1: i32 currencyType;      // 货币类型
  2: i64 amount;            // 货币数量
  3: i32 effectiveDays;     // 有效期天数
  4: i64 effectiveEndTime;  // 有效期截止时间
}

struct TReverseConsumeProductRequest {
  1: i64 uid;               // 用户uid
  2: i64 amount;            // 金额，紫水晶价格
  3: TAppId appid;          // 业务类型
  4: string seqId;          // 流水号，产品订单id
  5: string description;    // 冲正返回描述，显示在后台流水中
}

struct TConsumeProductResult {
  /**
     * 0、失败
     * 1、成功(扣费则表示扣费成功，冲正则表示冲正成功)
     *
     * -23、余额不足
     * -400、参数错误
     * -500、服务端错误
     *
     */
  1: i32 code;
  2: string message;
  3: i64 orderId;
  4: i64 uid;
  5: string seqId;
  6: i64 amount;
  7: list<TCurrencyBean> realConsumeCurrencys;    // 实际消耗的货币信息
  8: string expand;
}

struct TConsumeProductRequest {
  1: i64 uid;               // 用户uid
  2: i64 anchorUid;         // 当前主持uid
  3: i64 sid;               // 当前顶级频道
  4: i64 ssid;              // 当前子频道
  5: i32 productId;         // 产品id，有业务方定义，没有则传0
  6: i32 productType;       // 产品类型，由营收分配
  7: i64 amount;            // 金额，紫水晶价格
  8: TAppId appid;          // 业务类型
  9: i32 usedChannel;       // 渠道：0、pc端
  10: i64 targetUid;        // 没有则传0
  11: string seqId;         // 流水号，产品订单id
  12: string description;   // 商品描述，显示在后台流水中
  13: string expand;        // 目前用于透传，原样返回
  14: i32 decreaseRest;
  15: string userIp;
  16: list<TCurrencyBean> extraConsumeCurrencys;  // 此次消费额外扣取指定货币账户指定余额
}

struct TProductConsumeData {
  1: i64 uid;               // 用户uid
  2: i32 productType;       // 产品类型
  3: i64 productId;         // 产品id
  4: i64 amount;            // 金额，紫水晶价格
  5: string description;    // 描述

  /**
     * 扣费状态
     * 1：成功
     * 2：Y币二次确认等待
     * 3：失败
     * 4：冲正返还
     */
  6: i32 status;
  7: string seqId;          // 流水号，产品订单id
}

service TProductService {

  /**
   * 查询扣费信息
   *
   * @param uid 用户uid
   * @param appid 业务类型
   * @param seqId 流水号
   * @return
   */
  TProductConsumeData getProductConsumeData(1: i64 uid, 2: TAppId appid, 3: string seqId);

  /**
   * 返回，冲正，只返还紫水晶 或 队友金
   *
   * @param reverseConsumeProductRequest 请求参数
   * @return
   */
  TConsumeProductResult reverseConsumeProduct(1: TReverseConsumeProductRequest reverseConsumeProductRequest);

  /**
   * 扣费
   *
   * @param consumeProductRequest 请求参数
   * @return 如果返回成功，则表示扣费成功
   *
   * 如果调用我们接口超时，业务方可以选择重试。如果订单号存在并已经成功扣费，则返回原订单的成功状态
   */
  TConsumeProductResult consumeProductNew(1: TConsumeProductRequest consumeProductRequest);

  /**
   * 查询Y币余额 单位元
   */
  double queryDuowanb(1: i64 uid, 2: i32 appid)

}
