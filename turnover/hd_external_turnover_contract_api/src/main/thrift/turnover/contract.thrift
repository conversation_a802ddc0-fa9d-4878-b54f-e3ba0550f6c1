namespace java com.yy.hd.api.thrift.turnover.contract

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"

typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TCurrencyType TCurrencyType
typedef turnover_common.TAppId TAppId

/**
 * 参数说明：
 * liveUid：主播
 * sid：签约频道
 * owUid：ow
 * signTime：签约时间
 */
struct TContract {
    1: i64 liveUid,
    2: string groupName,
    3: i64 sid,
    4: i64 owUid,
    5: i32 weight,          // 分成百分比(weight=20表示公会拿2成,主持拿8成)
    6: i64 signTime
    7: i32 appid;
	8: i32 companySign;		// 是否是企业签约
	9: i64 finishTime;		// 签约截止时间
	10: i32 months;			// 签约期限月份数
	11: i32 superAnchorSign;	// 是否是超级主持
	12: i32 templateId;		// 可忽略
	13: i32 settleMode; //0对私结算, 1对公结算
}

struct TRoomContract {
  1: i64 id;
  2: i32 appid;
  3: i64 sid;  // 房管签约sid
  4: i64 ssid; // 房管经营厅ssid
  5: i64 owUid;
  6: i64 roomMgrUid;
  7: i32 weight;
  8: i32 settleMode; // 0：对私，1：对公
  9: string expand; // {"isPersonalRoom":1} expand里有这个字段且=1的就是个播厅, 没有或者0就是公共厅
  10: i64 createTime;
  11: i64 updateTime;
  12: i64 signTime;
  13: i64 finishTime;
  14: i64 playSid; // 房管经营厅sid
}


// 签约相关service，注意：服务端口是 6907
service TContractService {
    void ping2();

    /**
     * 查询签约记录：当TContract.liveUid!=0 && TContract.liveUid==uid时，该主持有签约
     *
     * @param uid 主持uid
     * @param appid 业务id，固定填 TAppId.Dating
     * @return
     */
    TContract queryContractByAnchor(1: i64 uid, 2: TAppId appid);

    /**
     * 批量查询主播签约信息
     *
     * @param uid,appid
     * @return map<uid, TContract>
     */
    map<i64, TContract> batchQueryContractByAnchors(1: list<i64> uids, 2: TAppId appid);

    //根据uid查房管签约, 传空返回全量
    map<i64, TRoomContract> queryRoomContractByUid(1: TAppId appid, 2:list<i64> uidList) throws (1: TServiceException ex1);

    //根据ssid查房管签约, 传空返回全量
    map<i64, TRoomContract> queryRoomContractBySsid(1: TAppId appid, 2:list<i64> ssidList) throws (1: TServiceException ex1);

    // 按频道查房管厅列表
    list<TRoomContract> queryRoomContractBySid(1: TAppId appid, 2: i64 sid) throws (1: TServiceException ex1);

    // 按OW查询房管厅列表
    list<TRoomContract> queryRoomContractByOw(1: TAppId appid, 2: i64 owUid) throws (1: TServiceException ex1);

    // 查所有房管厅列表
    list<TRoomContract> queryRoomContractByAppId(1: TAppId appid) throws (1: TServiceException ex1);
}
