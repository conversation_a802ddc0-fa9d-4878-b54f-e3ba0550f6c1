namespace java com.yy.hd.api.thrift.turnover.family

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"

typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TCurrencyType TCurrencyType
typedef turnover_common.TAppId TAppId

struct TFamily {
  /**
   * 家族ID
   */
  1: i64 id;

  /**
   * 家族名称
   */
  2: string name;

  /**
   * 会长UID
   */
  3: i64 uid;

  /**
   * 入驻日期
   */
  4: i64 signTime;

  /**
   * 状态, 0:申请中; 1:生效中; 2:已失效
   */
  5: i32 status;

  /**
   * 业务id
   */
  6: i32 appid;

  /**
   * 扩展字段
   */
  7: string expand;

  /**
   * 结算模式，0 对私结算  1对公结算
   */
  8: i32 settleMode;
}

struct TFamilyContract {
  /**
   * 业务id
   */
  1: i32 appid;

  /**
   * 家族id
   */
  2: i64 familyId;

  /**
   * 主持uid
   */
  3: i64 liveUid;

  /**
   * 分成比例
   */
  4: i32 weight;

  /**
   * 生效日期
   */
  5: i64 signTime;

  /**
   * 失效日期
   */
  6: i64 finishTime;

  /**
   * 扩展json字段 {"delete":1} 表示解约中
   */
  7: string expand;

  /**
   * 经纪人uid
   */
  8: i64 agentUid;
  /**
   * 主持角色，0普通主持，1大神
   */
  9: i32 roleType;

  /**
   * 结算模式，0对私，1对公
   */
  10: i32 settleMode;

  /**
   * 邀请房主uid
   */
  11: i64 inviteRoomer;
}

struct TFamilyContractPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TFamilyContract> contents;
}

// s2sname: to_contract/to_contract_pre
service TFamilyService extends turnover_common.TPingService {

  /**
   * 删除家族
   *
   * @param id 家族ID
   */
  i32 deleteFamily(1: i64 id, 2: TAppId appid) throws (1: TServiceException ex1);

  /**
   * 设置家族对公状态
   * @param id 家族ID
   * @param appid appid
   * @param settleMode 结算方式：0对私 1对公
   * @param force 1：代表强制对私(取消对公资格，且旗下有对公主持时，该值非1会拦截)"
   */
  i32 updateFamilySettleMode(1: i64 id, 2: TAppId appid, 3: i32 settleMode, 4: i32 force) throws (1: TServiceException ex1);

  /**
   * 查询家族签约信息
   * @param familyId 家族id
   * @param page 1为第一页，默认1
   * @param pagesize 每页数量，默认50
   */
  TFamilyContractPageResult queryContractByFamilyId(1: i64 familyId, 2: i32 page, 3: i32 pageSize, 4: TAppId appid) throws (1: TServiceException ex1);

  /**
   * 查询家族
   * @param ids 家族ID
   */
  map<i64, TFamily> queryFamily(1: list<i64> ids, 2: TAppId appid, 3: string name) throws (1: TServiceException ex1);

  /**
   * 查询家族签约信息
   * @param liveUid 主持uid
   */
  TFamilyContract queryContractByLiveUid(1: i64 liveUid, 2: TAppId appid) throws (1: TServiceException ex1);

}
