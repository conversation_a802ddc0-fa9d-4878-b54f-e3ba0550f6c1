namespace java com.yy.hd.api.thrift.turnover.bag

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"

typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TCurrencyType TCurrencyType
typedef turnover_common.TAppId TAppId
typedef turnover_common.UsedChannelType UsedChannelType
typedef turnover_common.TPropsType TPropsType
typedef turnover_common.TAccountOperateType TAccountOperateType

struct TCanBuyGiftBag {
  /**
   * true：能，false：不能
   */
  1: bool canBug;

  /**
   * 透传udb字段 0: <7 days, 1: <15 days, 2: <30 days, 3: <90 days, 4: <180 days, 5: <360 days, 6: >=360 days
   */
  2: i32 seq;

  /**
   * 透传udb字段 seq=0时，代表用户注册第x天，seq≠0 days字段无效
   */
  3: i32 days;

  /**
   * 扩展字段，预留
   */
  4: string expand;
}

struct TGiftBagProp {
  1: TProps giftBag;
  2: list<TGiftBag> config;
  3: map<i32, TProps> acquireProps;
}

struct TProps {
  1: i32 id;
  2: TPropsMeta meta;
  3: list<TPropsPricing> pricing;
  4: list<TPropsChannel> channels;
}

struct TPropsMeta {
  1: i32 id;
  2: string name;
  3: string shortName;
  4: TPropsType type;
  5: TAppId appId;
  6: string expand;
  7: string afterConsume;
  8: string afterUse;
  9: string beforeConsume;
  10: string beforeUse;
  11: i32 visible;
  12: i64 endTime;
  13: i64 startTime;
  14: i64 createTime;
}

struct TGiftBag {
  1: i32 giftPropId;
  2: i32 propId;
  3: i32 pricingId;
  4: i32 propCnt;
  5: double acquireProbability;
  6: i32 exclusive;
  7: i32 type;
  8: i32 currencyType;
  9: i32 currencyAmount;
  10: string name;
  11: string url;
  12: string tips;
  13: i32 weight;
  14: i32 status;
  15: string expand;
}

struct TPropsPricing {
  1: i32 id;
  2: i32 propsId;
  3: TCurrencyType currencyType;
  4: double currencyAmount;
  5: i32 month;
  6: string priceHints;
  7: i32 visible;
  8: string userTypeLimit;
  9: i32 usedChannel;
  10: i64 effectStartTime;
  11: i64 effectEndTime;
  12: i32 weight;
  13: double revenueRate;
  14: i32 revenueType;
  15: i32 appid;
}

struct TPropsChannel {
  1: i32 id;
  2: i32 propId;
  3: UsedChannelType channelType;
  4: string description;
  5: string priority;
  6: string imgs;
  7: i32 visible;
  8: i32 usable;
  9: string tips;
  10: string packagePriority;
  11: i32 useType;
  12: string userTitle;
  13: string userTitleName;
}

struct TUserAccount {
  1: i64 uid;
  2: TCurrencyType currencyType;
  3: i64 amount;
  4: i64 freezed;
  5: i32 appid;
}

struct TExchangeCurrencyResult {
  1: i64 uid;
  2: TAppId appid;
  3: TCurrencyType srcCurrencyType;
  4: TCurrencyType destCurrencyType;
  5: i32 result;
  6: TUserAccount srcAccount;
  7: TUserAccount destAccount;
  8: i64 exchangeAmount;
  9: TChannelAccount srcChannelAccount;
}

struct TChannelAccount {
  1: i64 uid;
  2: TCurrencyType currencyType;
  3: i64 amount;
  4: i64 freezed;
  5: i32 appid;
  6: i64 sid;
}

struct TUserChannelAccount {
  1: i64 id;
  2: i64 uid;
  3: i64 sid;
  4: i32 appid;
  5: TCurrencyType currencyType;
  6: i64 amount;
  7: i64 totalAmount;
  8: i64 version;
  9: string expand;
}

struct TUserChannelAccountHistory {
  1: i64 id;
  2: i64 accountId;
  3: i64 uid;
  4: i64 sid;
  5: TCurrencyType currencyType;
  6: i64 amountOrig;
  7: i64 amountChange;
  8: TAccountOperateType optType;
  9: i64 optTime;
  10: string description;
  11: string userIp;
  12: i32 appid;
  13: string expand;
}
struct TUserChannelAccountHistoryPage {
  1: list<TUserChannelAccountHistory> records;
  2: i32 total;
}

struct TUserChannelAccountPage {
  1: list<TUserChannelAccount> records;
  2: i32 total;
}

/** 是否有购买低价礼物资格 */
struct TCanBuyLowPriceProp {
  /**
   * true：能，false：不能
   */
  1: bool canBuy;

  /**
   * 扩展字段，预留
   */
  2: string expand;
}

struct TCanBuyGiftBagYyLive {
  /**
   * true：能，false：不能
   */
  1: bool canBuy;

  /**
   * url
   */
  2: string url;

  /**
   * 扩展字段，预留
   */
  3: string expand;
}

struct TYyInfo
{
    1: required i64 uid;
    2: required i64 yyId;
    3: string yyName;
    4: i32 usedChannel; // 送礼者渠道号，客户端传0；送礼人信息传入即可
}

struct TPropsMetaAndSinglePricing {
  1: i32 id;
  2: TPropsMeta meta;			// 道具元信息
  3: TPropsPricing pricing;		// 道具价格信息
}

struct TAddPropsAndUseInfo {
  1: TYyInfo recvYyInfo; // 收礼人信息 交友会设置为主持人uid，可能不是实际的收礼uid
  2: i32 propId; // 礼物id
  3: i32 count; // 数量
  4: string expand; // 送礼相关的服务端标记，例如displayType
  5: string seqId; // 单条送礼的seqId
  6: i64 targetUid; // 送礼对象uid
}

service TGiftBagService {
  /**
   * 用户是否有资格购买礼包
   * @param appid 业务id
   * @param uid 用户id
   * @param giftPropIds 礼包id集合
   * @param channels 渠道集合 133：追玩语音房IOS，134：追玩语音房安卓
   */
  TCanBuyGiftBag canBuyGiftBag(1: TAppId appid, 2: i64 uid, 3: list<i32> giftPropIds, 4: list<i32> channels) throws (1: TServiceException ex1);
  TGiftBagProp getGiftBag(1: i32 giftPropId);
  list<TGiftBag> getGiftBagConfig(1: i32 giftPropId);
  bool hasBuyChargeGiftBags(1: i32 appid, 2: i64 uid, 3: list<i32> giftPropIds);
  bool hasBuyGiftBag(1: i64 uid, 2: i32 giftPropId, 3: TAppId appid);
  bool hasBuyGiftBags(1: i64 uid, 2: list<i32> giftPropIds, 3: TAppId appid);
  map<i32, bool> hasBuyGiftBagsMap(1: i64 uid, 2: list<i32> giftPropIds, 3: TAppId appid);
  list<TGiftBagProp> getGiftBags(1: list<i32> giftPropIds);
  /**
   * 修改礼包名称和有效期
   * 1-成功
  */
  i32 updateGiftbagMeta(1: TAppId appid, 2: i32 giftPropId, 3: string name, 4: i64 startTime, 5: i64 endTime) throws (1: TServiceException ex1);
  /**
   * 用户是否有资格购买低价礼物
   * @param appid 业务id
   * @param uid 用户id
   * @param propId 礼物id
   * @param usedChannel 渠道 和 canBuyGiftBag 方法中定义的一样
   */
  TCanBuyLowPriceProp canBuyLowPriceProp(1: TAppId appid, 2: i64 uid, 3: i32 propId, 4: i32 usedChannel) throws (1: TServiceException ex1);
  /**
   * 根据玩法返回能否送礼给主持
   * @param grabLoveType 玩法
   * @param sid sid
   * @param ssid ssid
   */
  bool anchorCanRecvProps(1: i32 grabLoveType, 2: i64 sid, 3: i64 ssid);

  /**
   * 用户是否有资格购买手Y礼包
   * @param appid 业务id 传2，当前不是交友模板的情况下，不用调用本接口（营收侧非2的会报错）
   * @param uid 用户id
   * @param channels 渠道 29-ios手y，30-安卓手y
   */
  TCanBuyGiftBagYyLive canBuyGiftBagYyLive(1: TAppId appid, 2: i64 uid, 3: i32 usedChannel) throws (1: TServiceException ex1);
}

service TTurnoverService {
	   /**
		 * 为用户发放金币
		 *
		 * @param uid 用户的uid
		 * @param activityId 活动的id号 (填656)
		 * @param currencyType 货币类型 (金币填62)
		 * @param amount 发放金币的数量 (每1000个金币=1元)
		 * @param appid 业务类型 (追玩填 ZhuiYa 30)
		 * @param seqId 由调用方传入的流水号，用来区别唯一的一次调用，多次调用传同一个seqId只会写一次记录
		 * @param expand 扩展json字段，可以填 {"usedChannel":xxx}
		 */
	i32 issueCurrencyNew(1: i64 uid, 2: i64 activityId, 3: TCurrencyType currencyType, 4: i64 amount, 5: TAppId appid, 6: string seqId, 7: string expand) throws (1: TServiceException ex1);



		/**
		 * 获取用户账户余额
		 * @param uid 用户的uid
		 * @param appid 业务方标识 (填 ZhuiYa,30)
		 * @param currencyType 货币类型 (填 Zhuiwan_Gold,62)
		 * @return userAccount 用户账户余额( "amount":金币数 )
		 */
	TUserAccount getUserAccountByUidAndType(1: i64 uid, 2: TAppId appid, 3: TCurrencyType currencyType);



		/**
		 * 清空用户账户
		 *
		 * @param 同上
		 * @return 1.成功  0.失败(帐户不存在或余额已为0)  (其他错误抛异常)
		 */
	i32 clearUserAccountByUidAndType(1: i64 uid, 2: TAppId appid, 3: TCurrencyType currencyType) throws (1: TServiceException ex1);

	/**
      * 货币转换(金币转提现货币,1:1转)
      * @param uid 用户的uid
      * @param appid 业务方标识 (填 ZhuiYa,30)
      * @param amount 需要转的金币数 (1000金币=1元)
      * @param srcCurrencyType 源货币类型 (填 Zhuiwan_Gold,62)
      * @param destCurrencyType 目标货币类型 (填 Zhuiwan_SettleGold,63)
      * @param userIp IP地址
      * @param usedChannel 渠道id(知道就填实际值, 不知道填0)
      * @param configId 填0
      * @param expand (可不填)
      * @return TExchangeCurrencyResult (result=1成功,其他失败)
      */
    TExchangeCurrencyResult exchangeUserCurrency(1: i64 uid, 2: TAppId appid, 3: i64 amount, 4: TCurrencyType srcCurrencyType, 5: TCurrencyType destCurrencyType, 6: string userIp, 7: UsedChannelType usedChannel, 8: i64 configId, 9: string expand) throws (1: TServiceException ex1);

    /**
     * 发放活动道具接口
     *
     * @param uid 用户
     * @param propId 道具id
     * @param count 数量
     * @param addSeqId 由调用方传入的流水号，用来区别唯一的一次调用，多次调用传同一个seqId只会写一次记录
     * @param appid 业务id，交友业务为 TAppId.Dating
     * @param addType 发放的类型：1-激活码，2-活动发放，3-商城购买
     * @param expand 扩展字段
     * @param sid 发放道具时所在的顶级频道
     * @param ssid 发放道具时所在的子频道
     * @param activityId 活动id号
     * @return  * 返回码：
     * 				0 失败
     * 				1 成功
     * 				2 超出限额
     * @throws ServiceException
     * 			   错误码：
     *             <ul>
     *             <li>-1: 参数错误</li>
     *             <li>-22: 账户不存在</li>
     * 			   <li>-405: 订单序号已存在</li>
     * 			   <li>-500: 服务端出错</li>
     *             <li>-600: 该活动不存在</li>
     *             </ul>
     */
    i32 addPropsActivity(1:  i64 uid, 2:  i32 propId, 3:  i32 count, 4:  string addSeqId,
    		5:  TAppId appid, 6:  string expand, 7:  i64 sid, 8:  i64 ssid, 9:  i32 addType, 10:  i64 activityId) throws (1: TServiceException ex1);

   /**
   * 发放道具并送出使用接口
   *
   * @param usedYyInfo 送礼人信息
   * @param propId 道具id
   * @param count 数量
   * @param addSeqId 由调用方传入的流水号，用来区别唯一的一次调用，多次调用传同一个seqId只会写一次记录
   * @param appid 业务id
   * @param addType 填2即可
   * @param expand 扩展字段
   * @param sid 发放道具时所在的顶级频道
   * @param ssid 发放道具时所在的子频道
   * @param activityId 活动id号
   * @param recvYyInfo 主持人/分成者信息
   * @param isBroadcastPropsUsedMessage 是否发送广播，填true即可
   * @param clientExpand 客户端透传的expand字段
   * @return  * 返回码：
   * 				0 失败
   * 				1 成功
   * 				2 超出限额
   * @throws ServiceException
   * 			   错误码：
   *             <ul>
   *             <li>-1: 参数错误</li>
   *             <li>-22: 账户不存在</li>
   * 			   <li>-405: 订单序号已存在</li>
   * 			   <li>-500: 服务端出错</li>
   *             <li>-600: 该活动不存在</li>
   *             </ul>
   */
  i32 addPropsActivityAndUsedPropsByYyInfo(1: TYyInfo usedYyInfo, 2: i32 propId, 3: i32 count, 4: string addSeqId, 5: TAppId appid, 6: string expand, 7: i64 sid, 8: i64 ssid, 9: i32 addType, 10: i64 activityId, 11: TYyInfo recvYyInfo, 12: bool isBroadcastPropsUsedMessage, 13: string clientExpand) throws (1: TServiceException ex1);

  /**
   *
   * 批量发放道具并使用道具(桃花签全麦专用)
   * @param listJson 由[{"recvYyInfo":"", "propId":20000, "count":1, "clientExpand":"", "seqId",""}] 组成的json
   * @return 0.失败  1.成功
   */
  i32 batchAddPropsActivityAndUsedPropsByYyInfo(1: string listJson, 2: TYyInfo usedYyInfo, 3: string addSeqId, 4: TAppId appid, 5: string expand, 6: i64 sid, 7: i64 ssid, 8: i32 addType, 9: i64 activityId, 10: bool isBroadcastPropsUsedMessage, 11: string clientExpand) throws (1: TServiceException ex1);


  /**
   * @param seqId 请求流水号，目前该接口设计为可以重试
   * @param usedYyInfo 送礼人信息
   * @param addPropsInfos 送礼相关信息
   * @param clientExpandMap;//送礼人送礼给收礼人，客户端生成的expand,key为收礼人uid
   * @param expand 客户端送多人传的一个通用的expand
   */
   i32 batchAddPropsActivityAndUsedPropsByYyInfoV2(1: TAppId appid, 2: string seqId, 3: TYyInfo usedYyInfo, 4: list<TAddPropsAndUseInfo> addPropsInfos, 5: map<i64,string> clientExpandMap, 6: i64 sid, 7: i64 ssid, 8: i64 activityId, 9: string expand);

    /**
     * 获取业务所有礼物及对应的单个价格接口
     *
     * @param appid 业务id，固定填 TAppId.Dating
     * @param scode 固定填 "datingHaveRevenueNotYYlive"
     * @return  礼物列表
     */
    list<TPropsMetaAndSinglePricing> getSinglePropsPricingInSpecifiedWayWithAllProps(1: TAppId appId, 2: string scode);

}

service TUserChannelAccountService {
  i64 decrUserChannelAccountWithOperateTypeAndDescription(1: i64 uid, 2: i32 appid, 3: i64 sid, 4: TCurrencyType currencyType, 5: i64 deltaAmount, 6: TAccountOperateType operateType, 7: string description, 8: string userIp, 9: string expand) throws (1: TServiceException ex1);
  TUserChannelAccount getUserChannelAccountByUidAndSid(1: i64 uid, 2: TAppId appid, 3: i64 sid, 4: TCurrencyType currencyType);
  TUserChannelAccountHistoryPage getUserChannelAccountHistoryPage(1: i64 uid, 2: TAppId appid, 3: TCurrencyType currencyType, 4: TAccountOperateType operateType, 5: i64 sid, 6: i32 start, 7: i32 limit, 8: i64 startTime, 9: i64 endTime, 10: string expand);
  TUserChannelAccountPage getUserChannelAccountPage(1: i64 uid, 2: TAppId appid, 3: TCurrencyType currencyType, 4: i32 start, 5: i32 limit);
  i64 incrUserChannelAccountWithOperateTypeAndDescription(1: i64 uid, 2: i32 appid, 3: i64 sid, 4: TCurrencyType currencyType, 5: i64 deltaAmount, 6: TAccountOperateType operateType, 7: string description, 8: string userIp, 9: string expand) throws (1: TServiceException ex1);

/**
   * 查询用户频道账户
   * @param appid 业务ID
   * @param sid 频道id
   * @param uid 用户id
   * @param currencyType 货币类型
   * @throws ServiceException
   */
  TUserChannelAccountPage queryUserChannelAccountPage(1: TAppId appid, 2: i64 sid, 3: i64 uid, 4: TCurrencyType currencyType, 5: i32 page, 6: i32 pageSize) throws (1: TServiceException ex1);

  /**
   * 频道代充剩余额度
   * @param appid 业务ID
   * @param sid 频道id
   * @param placeholderContext 占位符，yrpc bug 请求发不出去，要连续的参数才行，这里设置为 null 就行
   * @param currencyType 货币类型
   * @throws ServiceException
   */
  i64 queryChannelReplaceChargeRemain(1: TAppId appid, 2: i64 sid, 3:string placeholderContext, 4: TCurrencyType currencyType) throws (1: TServiceException ex1);

}


struct TReverseConsumeProductRequest {
  1: i64 uid;               // 用户uid
  2: i64 amount;            // 金额，紫水晶价格
  3: TAppId appid;          // 业务类型
  4: string seqId;          // 流水号，产品订单id
  5: string description;    // 冲正返回描述，显示在后台流水中
}

struct TConsumeProductResult {
  /**
     * 0、失败
     * 1、成功(扣费则表示扣费成功，冲正则表示冲正成功)
     *
     * -23、余额不足
     * -400、参数错误
     * -500、服务端错误
     *
     */
  1: i32 code;
  2: string message;
  3: i64 orderId;
  4: i64 uid;
  5: string seqId;
  6: i64 amount;
  7: list<TCurrencyBean> realConsumeCurrencys;    // 实际消耗的货币信息
  8: string expand;
}

struct TCurrencyBean {
  1: i32 currencyType;      // 货币类型
  2: i64 amount;            // 货币数量
  3: i32 effectiveDays;     // 有效期天数
  4: i64 effectiveEndTime;  // 有效期截止时间
}

struct TConsumeProductRequest {
  1: i64 uid;               // 用户uid
  2: i64 anchorUid;         // 当前主持uid
  3: i64 sid;               // 当前顶级频道
  4: i64 ssid;              // 当前子频道
  5: i32 productId;         // 产品id，有业务方定义，没有则传0
  6: i32 productType;       // 产品类型，由营收分配
  7: i64 amount;            // 金额，紫水晶价格
  8: TAppId appid;          // 业务类型
  9: i32 usedChannel;       // 渠道：0、pc端
  10: i64 targetUid;        // 没有则传0
  11: string seqId;         // 流水号，产品订单id
  12: string description;   // 商品描述，显示在后台流水中
  13: string expand;        // 目前用于透传，原样返回
  14: i32 decreaseRest;
  15: string userIp;
  16: list<TCurrencyBean> extraConsumeCurrencys;  // 此次消费额外扣取指定货币账户指定余额
}

struct TProductConsumeData {
  1: i64 uid;               // 用户uid
  2: i32 productType;       // 产品类型
  3: i64 productId;         // 产品id
  4: i64 amount;            // 金额，紫水晶价格
  5: string description;    // 描述

  /**
     * 扣费状态
     * 1：成功
     * 2：Y币二次确认等待
     * 3：失败
     * 4：冲正返还
     */
  6: i32 status;
  7: string seqId;          // 流水号，产品订单id
}

service TProductService {

  /**
   * 查询扣费信息
   *
   * @param uid 用户uid
   * @param appid 业务类型
   * @param seqId 流水号
   * @return
   */
  TProductConsumeData getProductConsumeData(1: i64 uid, 2: TAppId appid, 3: string seqId);

  /**
   * 返回，冲正，只返还紫水晶 或 队友金
   *
   * @param reverseConsumeProductRequest 请求参数
   * @return
   */
  TConsumeProductResult reverseConsumeProduct(1: TReverseConsumeProductRequest reverseConsumeProductRequest);

  /**
   * 扣费
   *
   * @param consumeProductRequest 请求参数
   * @return 如果返回成功，则表示扣费成功
   *
   * 如果调用我们接口超时，业务方可以选择重试。如果订单号存在并已经成功扣费，则返回原订单的成功状态
   */
  TConsumeProductResult consumeProductNew(1: TConsumeProductRequest consumeProductRequest);

  /**
   * 查询Y币余额 单位元
   */
  double queryDuowanb(1: i64 uid, 2: i32 appid)

}
