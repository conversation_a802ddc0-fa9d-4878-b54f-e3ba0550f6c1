namespace java com.yy.hd.api.thrift.turnover.bag

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"
include "../../../../../hd_external_turnover_props_api/src/main/thrift/turnover/turnover_props.thrift"

typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TAppId TAppId
typedef turnover_props.TProps TProps

struct TCanBuyGiftBag {
  /**
   * true：能，false：不能
   */
  1: bool canBug;

  /**
   * 透传udb字段 0: <7 days, 1: <15 days, 2: <30 days, 3: <90 days, 4: <180 days, 5: <360 days, 6: >=360 days
   */
  2: i32 seq;

  /**
   * 透传udb字段 seq=0时，代表用户注册第x天，seq≠0 days字段无效
   */
  3: i32 days;

  /**
   * 扩展字段，预留
   */
  4: string expand;
}

struct TGiftBagProp {
  1: TProps giftBag;
  2: list<TGiftBag> config;
  3: map<i32, TProps> acquireProps;
}

struct TGiftBag {
  1: i32 giftPropId;
  2: i32 propId;
  3: i32 pricingId;
  4: i32 propCnt;
  5: double acquireProbability;
  6: i32 exclusive;
  7: i32 type;
  8: i32 currencyType;
  9: i32 currencyAmount;
  10: string name;
  11: string url;
  12: string tips;
  13: i32 weight;
  14: i32 status;
  15: string expand;
}

/** 是否有购买低价礼物资格 */
struct TCanBuyLowPriceProp {
  /**
   * true：能，false：不能
   */
  1: bool canBuy;

  /**
   * 扩展字段，预留
   */
  2: string expand;
}

struct TCanBuyGiftBagYyLive {
  /**
   * true：能，false：不能
   */
  1: bool canBuy;

  /**
   * url
   */
  2: string url;

  /**
   * 扩展字段，预留
   */
  3: string expand;
}

// s2sname: to_service/to_service_pre
service TGiftBagService {
  /**
   * 用户是否有资格购买礼包
   * @param appid 业务id
   * @param uid 用户id
   * @param giftPropIds 礼包id集合
   * @param channels 渠道集合 133：追玩语音房IOS，134：追玩语音房安卓
   */
  TCanBuyGiftBag canBuyGiftBag(1: TAppId appid, 2: i64 uid, 3: list<i32> giftPropIds, 4: list<i32> channels) throws (1: TServiceException ex1);
  TGiftBagProp getGiftBag(1: i32 giftPropId);
  list<TGiftBag> getGiftBagConfig(1: i32 giftPropId);
  bool hasBuyChargeGiftBags(1: i32 appid, 2: i64 uid, 3: list<i32> giftPropIds);
  bool hasBuyGiftBag(1: i64 uid, 2: i32 giftPropId, 3: TAppId appid);
  bool hasBuyGiftBags(1: i64 uid, 2: list<i32> giftPropIds, 3: TAppId appid);
  map<i32, bool> hasBuyGiftBagsMap(1: i64 uid, 2: list<i32> giftPropIds, 3: TAppId appid);
  list<TGiftBagProp> getGiftBags(1: list<i32> giftPropIds);
  /**
   * 修改礼包名称和有效期
   * 1-成功
  */
  i32 updateGiftbagMeta(1: TAppId appid, 2: i32 giftPropId, 3: string name, 4: i64 startTime, 5: i64 endTime) throws (1: TServiceException ex1);
  /**
   * 用户是否有资格购买低价礼物
   * @param appid 业务id
   * @param uid 用户id
   * @param propId 礼物id
   * @param usedChannel 渠道 和 canBuyGiftBag 方法中定义的一样
   */
  TCanBuyLowPriceProp canBuyLowPriceProp(1: TAppId appid, 2: i64 uid, 3: i32 propId, 4: i32 usedChannel) throws (1: TServiceException ex1);
  /**
   * 根据玩法返回能否送礼给主持
   * @param grabLoveType 玩法
   * @param sid sid
   * @param ssid ssid
   */
  bool anchorCanRecvProps(1: i32 grabLoveType, 2: i64 sid, 3: i64 ssid);

  /**
   * 用户是否有资格购买手Y礼包
   * @param appid 业务id 传2，当前不是交友模板的情况下，不用调用本接口（营收侧非2的会报错）
   * @param uid 用户id
   * @param channels 渠道 29-ios手y，30-安卓手y
   */
  TCanBuyGiftBagYyLive canBuyGiftBagYyLive(1: TAppId appid, 2: i64 uid, 3: i32 usedChannel) throws (1: TServiceException ex1);
}
