<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yy.hd</groupId>
        <artifactId>hd_api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>hd_external_turnover_finance_api</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_base_api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 拆分后的所有子模块依赖 -->
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_common_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_revenue_v2_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_props_stat_v2_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_function_user_white_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_currency_real_support_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_data_center_statistics_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_props_v2_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_activity_config_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_product_v2_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_dating_seal_stat_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_dating_garden_stat_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_dating_stat_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_udb_risk_gift_send_api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd_external_turnover_finance_revenue_daren_v2_api</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>