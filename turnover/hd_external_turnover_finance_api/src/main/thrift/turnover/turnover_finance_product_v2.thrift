namespace java com.yy.hd.api.thrift.turnover.finance.product

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId
typedef turnover_finance_common.TPropInfo TPropInfo
typedef turnover_finance_common.TConsumeProductType TConsumeProductType
typedef turnover_finance_common.TCurrencyType TCurrencyType

// TProductServiceV2 专用结构体
struct TWeekPropsRecvInfo {
  1: i64 uid;//送礼人uid
  2: i32 propId;//盖章id
  3: string propName;//没有
  4: i32 pricingId;
  5: double amount;//结算收入
  6: i64 usedTime;//时间
  7: i64 sid;
  8: i32 propCnt;//送礼数量
  9: string guestUid;//收礼人uid
  10: i64 anchorUid;//主持uid
  11: double sumAmount;//总价
  12: i64 id;
  13: TCurrencyType currencyType;
  14: i32 appid;
  15: i32 playType;
  16: string expand;
  17: i64 ssid;
  18: i32 extendUidType;
  19: string guestNick;
  20: string recvNick;
  21: string currencyName;
  22: string propsSourceName;//结算类型
  23: string propTypeName;
  24: string identity;
}

struct TWeekPropsRecvInfoQueryPage {
  1: list<TWeekPropsRecvInfo> content;
  2: i32 page;
  3: i32 pagesize;
  4: i32 totalElement;
  5: i32 totalPage;
  6: map<string, string> extend;
}

/**
  * appid=2, size:最多返回数量, channelType=0
*/
service TProductServiceV2 {
    // 获取PC礼物列表的礼物
    list<TPropInfo> getVisiblePropsByAppId(1: i32 appid, 2: i32 size, 3: i32 channelType);

    /**
    * 查询盖章明细，7天内
    * @param consumeProductType 盖章传2：Seal
    * @param startTime 毫秒，闭区间
    * @param endTime 毫秒，开区间
    * @param page 页码
    * @param pagesize 每页数量
    * @param role 房管厅下的消费记录：4
    * @param usedUid 送礼人uid
    * @param targetUid 收礼人uid
    * @param ssid ssid
    * @param orderAsc true：时间升序，false：时间倒序
    * @param expand 扩展json
    */
    TWeekPropsRecvInfoQueryPage queryWeekProductRecieveExternal(1: TAppId appid, 2: TConsumeProductType consumeProductType, 3: i64 startTime, 4: i64 endTime, 5: i32 page, 6: i32 pagesize, 7: i32 role, 8: i64 ssid, 9: i64 usedUid, 10: i64 targetUid, 11: bool orderAsc, 12: string expand) throws (1: TServiceException ex1);
}
