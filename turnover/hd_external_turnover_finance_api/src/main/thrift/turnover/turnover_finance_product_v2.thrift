namespace java com.yy.hd.api.thrift.turnover.finance.product

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId
typedef turnover_finance_common.TPropInfo TPropInfo
typedef turnover_finance_common.TConsumeProductType TConsumeProductType
typedef turnover_finance_common.TWeekPropsRecvInfoQueryPage TWeekPropsRecvInfoQueryPage

/**
  * appid=2, size:最多返回数量, channelType=0
*/
service TProductServiceV2 {
    // 获取PC礼物列表的礼物
    list<TPropInfo> getVisiblePropsByAppId(1: i32 appid, 2: i32 size, 3: i32 channelType);

    /**
    * 查询盖章明细，7天内
    * @param consumeProductType 盖章传2：Seal
    * @param startTime 毫秒，闭区间
    * @param endTime 毫秒，开区间
    * @param page 页码
    * @param pagesize 每页数量
    * @param role 房管厅下的消费记录：4
    * @param usedUid 送礼人uid
    * @param targetUid 收礼人uid
    * @param ssid ssid
    * @param orderAsc true：时间升序，false：时间倒序
    * @param expand 扩展json
    */
    TWeekPropsRecvInfoQueryPage queryWeekProductRecieveExternal(1: TAppId appid, 2: TConsumeProductType consumeProductType, 3: i64 startTime, 4: i64 endTime, 5: i32 page, 6: i32 pagesize, 7: i32 role, 8: i64 ssid, 9: i64 usedUid, 10: i64 targetUid, 11: bool orderAsc, 12: string expand) throws (1: TServiceException ex1);
}
