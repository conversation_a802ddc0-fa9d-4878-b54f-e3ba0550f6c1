namespace java com.yy.hd.api.thrift.turnover.finance.currency.real.support

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException

// TCurrencyRealSupportService 专用结构体
struct TRealAmountInfo{
  1: i64 uid;
  2: i64 queryTime;
  3: i64 realAmount;		//当前黄水晶
  4: i64 maxSendAmount;		//最大发送额度
  5: i64 maxRecvAmount;		//最大接收额度
  6: i64 currSendAmount;	//当前发送额度
  7: i64 currRecvAmount;	//当前接收额度
}

service TCurrencyRealSupportService{

  list<TRealAmountInfo> getRealAmountInfo(1: i32 appid, 2: list<i64> uidList) throws (1: TServiceException ex1);

  // 黄水晶帮扶
  i32 supportReal(1: i32 appid, 2: i64 fromUid, 3: list<i64> toUidList, 4: i64 amount, 5: string seq) throws (1: TServiceException ex1);

  //1.成功,  -505.处理中,  其他.失败
  i32 getSupportRealStatus(1: i32 appid, 2: string seq);
}
