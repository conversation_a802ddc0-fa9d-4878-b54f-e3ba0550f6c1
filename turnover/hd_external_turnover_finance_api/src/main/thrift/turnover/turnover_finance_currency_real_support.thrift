namespace java com.yy.hd.api.thrift.turnover.finance.currency.real.support

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TRealAmountInfo TRealAmountInfo

service TCurrencyRealSupportService{

  list<TRealAmountInfo> getRealAmountInfo(1: i32 appid, 2: list<i64> uidList) throws (1: TServiceException ex1);

  // 黄水晶帮扶
  i32 supportReal(1: i32 appid, 2: i64 fromUid, 3: list<i64> toUidList, 4: i64 amount, 5: string seq) throws (1: TServiceException ex1);

  //1.成功,  -505.处理中,  其他.失败
  i32 getSupportRealStatus(1: i32 appid, 2: string seq);
}
