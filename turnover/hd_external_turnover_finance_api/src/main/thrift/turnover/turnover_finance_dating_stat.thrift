namespace java com.yy.hd.api.thrift.turnover.finance.dating.stat

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId
typedef turnover_finance_common.TCurrencyType TCurrencyType

// TDatingStatService 专用结构体
struct TDatingAnchorDailyRealPreRevenueStat{
  1: i64 id;
  2: i64 dt;
  3: i64 uid;
  4: i64 propsRealAmount; // 礼物待日结黄水晶
  5: i64 propsRealAmountBullet;     // 礼物待日结黄水晶 只有弹幕礼物
  6: i64 propsRealAmountNoBullet;   // 礼物待日结黄水晶 所有礼物-弹幕礼物
}

struct TDatingDailyRoomStat{
	1: i64 sid
	2: i64 ssid
	3: i64 anchorUid
	4: i64 statDate
	5: i64 propsIncome
	6: i64 sealIncome
}

struct TRoomIncomeStat {
  1: string statPeriod;//2023-11-07
  2: double propsTotalIncome;//总礼物流水
  3: double sealTotalIncome;//总盖章流水
  4: double propsSuperIncome;//超级主持礼物流水
  5: double propsNormalIncome;//普通主礼物流水
  6: double propsStarIncome;//星光主持礼物流水
  7: double sealSuperIncome;//超级主持盖章流水
  8: double sealNormalIncome;//普通主持盖章流水
  9: double sealStarIncome;//星光主持盖章流水
  10: list<TDatingDailyRoomStat> details;//主持流水明细
}

struct TWeekPropsRecvInfo {
  1: i64 uid;//送礼人uid
  2: i32 propId;//盖章id
  3: string propName;//没有
  4: i32 pricingId;
  5: double amount;//结算收入
  6: i64 usedTime;//时间
  7: i64 sid;
  8: i32 propCnt;//送礼数量
  9: string guestUid;//收礼人uid
  10: i64 anchorUid;//主持uid
  11: double sumAmount;//总价
  12: i64 id;
  13: TCurrencyType currencyType;
  14: i32 appid;
  15: i32 playType;
  16: string expand;
  17: i64 ssid;
  18: i32 extendUidType;
  19: string guestNick;
  20: string recvNick;
  21: string currencyName;
  22: string propsSourceName;//结算类型
  23: string propTypeName;
  24: string identity;
}

struct TWeekPropsRecvInfoQueryPage {
  1: list<TWeekPropsRecvInfo> content;
  2: i32 page;
  3: i32 pagesize;
  4: i32 totalElement;
  5: i32 totalPage;
  6: map<string, string> extend;
}

service TDatingStatService {

  /**
  * 查询交友主持每日待结算黄水晶
  * anchorUids: 主持列表
  * startDate: 开始日期格式yyyymmdd
  * endDate: 结束日期格式yyyymmdd
  */
  list<TDatingAnchorDailyRealPreRevenueStat> queryAnchorDailyRealPreRevenue(1: list<i64> anchorUids, 2: string startDate, 3: string endDate) throws (1: TServiceException ex1);

  /**
  * 按照hgame后台口径查询今天的待日结黄水晶 startDate, 格式yyyymmdd, 仅能查当天、昨天(日结前)
  * startDate 查整天的，传 20231025 就查25这一天的，26凌晨日结前查的不包含26号的
  * 返回格式 {"propsRealAmount": 1000, "sealRealAmount": 1000, "propsRealAmountBullet": 1000, "propsRealAmountNoBullet": 1000}
  */
  map<string, i64> queryAnchorTodayRealPreRevenue(1: i64 anchorUid,2: string startDate);

    /**
    * 查询房管厅(天团厅/个播厅)礼物和盖章流水 总和填queryPublicRoom:true queryPersonalRoom:true
    * @param appId 业务ID
    * @param roomMgrUid 房管uid
    * @param queryPublicRoom 是否查询天团厅
    * @param queryPersonalRoom 是否查询个播厅
    * @return Map<时间,RoomIncomeStat>
    */
    map<string, TRoomIncomeStat> queryRoomIncomeByDate(1: TAppId appId, 2: i64 roomMgrUid, 3: bool queryPublicRoom, 4: bool queryPersonalRoom, 5: i64 startDate, 6: i64 endDate) throws (1: TServiceException ex1);

    /**
    * 查询礼物明细，单次查询时间范围最多一个自然月内，总时间范围两个自然月内
    * @param startTime 毫秒，闭区间
    * @param endTime 毫秒，开区间
    * @param page 页码
    * @param pagesize 每页数量
    * @param role 房管厅下的送礼记录：4
    * @param usedUid 送礼人uid
    * @param targetUid 收礼人uid
    * @param ssid ssid
    * @param orderAsc true：时间升序，false：时间倒序
    * @param expand 扩展json
    * @param totalParam totalParam > 0时，直接填到total不执行查询，totalParam==0时，查询total
    */
    TWeekPropsRecvInfoQueryPage queryDatingPropsUsedRecordStat(1: TAppId appid, 2: i64 startTime, 3: i64 endTime, 4: i32 page, 5: i32 pagesize, 6: i32 role, 7: i64 usedUid, 8: i64 targetUid, 9: i64 ssid, 10: bool orderAsc, 11: string expand, 12: i64 totalParam) throws (1: TServiceException ex1);

}
