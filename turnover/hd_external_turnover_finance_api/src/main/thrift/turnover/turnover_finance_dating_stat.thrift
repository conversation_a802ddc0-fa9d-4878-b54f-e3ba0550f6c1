namespace java com.yy.hd.api.thrift.turnover.finance.dating.stat

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId
typedef turnover_finance_common.TDatingAnchorDailyRealPreRevenueStat TDatingAnchorDailyRealPreRevenueStat
typedef turnover_finance_common.TRoomIncomeStat TRoomIncomeStat
typedef turnover_finance_common.TWeekPropsRecvInfoQueryPage TWeekPropsRecvInfoQueryPage

service TDatingStatService {

  /**
  * 查询交友主持每日待结算黄水晶
  * anchorUids: 主持列表
  * startDate: 开始日期格式yyyymmdd
  * endDate: 结束日期格式yyyymmdd
  */
  list<TDatingAnchorDailyRealPreRevenueStat> queryAnchorDailyRealPreRevenue(1: list<i64> anchorUids, 2: string startDate, 3: string endDate) throws (1: TServiceException ex1);

  /**
  * 按照hgame后台口径查询今天的待日结黄水晶 startDate, 格式yyyymmdd, 仅能查当天、昨天(日结前)
  * startDate 查整天的，传 20231025 就查25这一天的，26凌晨日结前查的不包含26号的
  * 返回格式 {"propsRealAmount": 1000, "sealRealAmount": 1000, "propsRealAmountBullet": 1000, "propsRealAmountNoBullet": 1000}
  */
  map<string, i64> queryAnchorTodayRealPreRevenue(1: i64 anchorUid,2: string startDate);

    /**
    * 查询房管厅(天团厅/个播厅)礼物和盖章流水 总和填queryPublicRoom:true queryPersonalRoom:true
    * @param appId 业务ID
    * @param roomMgrUid 房管uid
    * @param queryPublicRoom 是否查询天团厅
    * @param queryPersonalRoom 是否查询个播厅
    * @return Map<时间,RoomIncomeStat>
    */
    map<string, TRoomIncomeStat> queryRoomIncomeByDate(1: TAppId appId, 2: i64 roomMgrUid, 3: bool queryPublicRoom, 4: bool queryPersonalRoom, 5: i64 startDate, 6: i64 endDate) throws (1: TServiceException ex1);

    /**
    * 查询礼物明细，单次查询时间范围最多一个自然月内，总时间范围两个自然月内
    * @param startTime 毫秒，闭区间
    * @param endTime 毫秒，开区间
    * @param page 页码
    * @param pagesize 每页数量
    * @param role 房管厅下的送礼记录：4
    * @param usedUid 送礼人uid
    * @param targetUid 收礼人uid
    * @param ssid ssid
    * @param orderAsc true：时间升序，false：时间倒序
    * @param expand 扩展json
    * @param totalParam totalParam > 0时，直接填到total不执行查询，totalParam==0时，查询total
    */
    TWeekPropsRecvInfoQueryPage queryDatingPropsUsedRecordStat(1: TAppId appid, 2: i64 startTime, 3: i64 endTime, 4: i32 page, 5: i32 pagesize, 6: i32 role, 7: i64 usedUid, 8: i64 targetUid, 9: i64 ssid, 10: bool orderAsc, 11: string expand, 12: i64 totalParam) throws (1: TServiceException ex1);

}
