namespace java com.yy.hd.api.thrift.turnover.finance.dating.seal.stat

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TDatingRoomIncomeRequest TDatingRoomIncomeRequest
typedef turnover_finance_common.TDatingRoomIncome TDatingRoomIncome

// 盖章流水
service TDatingSealStatService {
  /**
   * 查询交友月度盖章流水统计（统计维度：yyyyMM + SID）
   * @param month 月份yyyyMM
   * @param sid 公会SID
   * @return 月度盖章流水单位厘
   * @throws ServiceException
   */
  i64 queryDatingMonthlySealIncomeStatBySid(1: string month, 2: i64 sid) throws (1: TServiceException ex1);

  /**
   * 查询交友月度盖章流水统计（统计维度：yyyyMM + SID + SSID + anchorUid）
   * 需要排除房管天团厅
   * @param month 月份yyyyMM
   * @param sid 公会SID
   * @param anchorUid 主持UID
   * @return Map<子厅SSID, 月度盖章流水单位厘>
   * @throws ServiceException
   */
  map<i64, i64> queryDatingMonthlySealIncomeStatBySidAnchorUid(1: string month, 2: i64 sid, 3: i64 anchorUid) throws (1: TServiceException ex1);

  /**
   * 查询交友月度盖章流水统计（统计维度：yyyyMM + SID + SSID）
   * 不区分是否房管天团厅
   * @param month 月份yyyyMM
   * @param sid 公会SID
   * @return Map<子厅SSID, 月度盖章流水单位厘>
   * @throws ServiceException
   */
  map<i64, i64> queryDatingMonthlySealIncomeStatBySidSsid(1: string month, 2: i64 sid) throws (1: TServiceException ex1);

    /**
    * 查询交友月度盖章流水统计（统计维度：yyyyMM + 经营SID + 经营SSID）
    * @param month 月份yyyyMM
    * @param req
    * @throws ServiceException
    */
    list<TDatingRoomIncome> queryDatingRoomMonthlySealIncomeBySidSsid(1: string month, 2: list<TDatingRoomIncomeRequest> reqs) throws (1: TServiceException ex1);

}
