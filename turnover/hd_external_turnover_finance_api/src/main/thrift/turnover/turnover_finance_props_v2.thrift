namespace java com.yy.hd.api.thrift.turnover.finance.props

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId
typedef turnover_finance_common.TSignedAnchorPropsUsedRecordPageResult TSignedAnchorPropsUsedRecordPageResult

service TPropsServiceV2 {

  /**
   * 查看旗下签约主播每条收到礼物明细记录
   * @param appid 业务ID
   * @param sid 工会sid。若imid>=0,则按imid查，不按sid查。
   * @param imid 主持YY号。若imid>=0,则按imid查，不按sid查。
   * @param startTime 开始日期 yyyy-MM-dd
   * @param endTime 结束日期 yyyy-MM-dd
   * @param page 页号(1, 2, 3...)
   * @param pagesize 每页大小
   * @param familyId 家族id
   * @param total 如果total大于0，直接返回total=参数total，否则执行查询
   * @param senderImid 送礼YY号
   * @param ssid 房间ssid
   * @return SignedAnchorPropsUsedRecordPageResult
   * @throws ServiceException
   */
  TSignedAnchorPropsUsedRecordPageResult querySignedAnchorPropsUsedRecords(1: TAppId appid, 2: i64 sid, 3: i64 imid, 4: i64 startTime, 5: i64 endTime, 6: i32 page, 7: i32 pagesize, 8: i64 familyId, 9: i64 total, 10: i64 senderImid, 11: i64 ssid) throws (1: TServiceException ex1);

}
