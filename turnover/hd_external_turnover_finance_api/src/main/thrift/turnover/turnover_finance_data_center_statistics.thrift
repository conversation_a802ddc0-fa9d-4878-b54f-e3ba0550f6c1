namespace java com.yy.hd.api.thrift.turnover.finance.data.center.statistics

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId

// TDataCenterStatisticsService 专用结构体
struct TSsidConsumeData {
    1: i64 ssid;
    2: string dt;
    3: i64 propsAmount;
    4: i64 sealAmount;
}

struct TPersonalRoomIncomeData{
	1: i64 sid;
	2: i64 ssid;
	3: string statDate;
	4: i64 superIncome;
	5: i64 normalIncome;
    6: i64 starlightIncome;
}

struct TDatingChannelIncomeStat {
  1: i64 id;
  2: i64 statDate;
  3: i32 sid;
  4: double giftIncome; // 礼物流水,单位元(要先四舍五入再乘以1000才是紫水晶)
  5: double nobleIncome;
  6: double pinkIncome;
  7: double guardIncome;
  8: double sealIncome;
  9: double sealDragonIncome;
  10: double propsIncomeWithoutMagic; // propsIncomeWithoutMagic: 排除魔法礼物的流水
}

struct TNewUserDailyConsumeAmount {
  1: i64 dt;
  2: i32 appid;
  3: i64 uid;
  4: i32 type;
  5: i64 amount;
}

struct TNewUserDailyConsumeAmountPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TNewUserDailyConsumeAmount> contents;
}

struct TDatingDailyAnchorIncomeStat {
  1: i32 id;
  2: i64 statDate;
  3: i64 anchorUid;
  4: i64 sid;
  5: double propsIncome; // propsIncome 礼物流水,单位元(要先四舍五入再乘以1000才是紫水晶)
  6: double sealIncome;
  7: double sealDragonIncome;
}

struct TRevenueSummaryRecord {
  1: i64 id;
  2: i64 uid;
  3: i64 sid;
  4: double income;
  5: i64 optTime;
  6: i64 revenueDate; // 统计日期毫秒时间戳
  7: i32 appid;
  8: i32 userType;
  9: i32 srcType;
  10: string expand;
  11: double allIncome; // 礼物流水,单位紫水晶
}

struct TFamilyDailyIncome {
  1: i64 statDate;    // 统计日期毫秒时间戳
  2: i64 totalIncome; // 总礼物流水,单位紫水晶
  3: i64 familyId; // 家族id
}

struct TZwyyfFamilyRoomDailyIncomeStat {
  1: i64 statDate; // 日期
  2: i64 familyUid; // 家族长uid
  3: i64 familyId; // 家族id
  4: i64 liveSid; // 收礼房间sid
  5: i64 liveSsid; // 收礼房间ssid
  6: i64 income; // 普通礼物流水
  7: i64 turnableIncome; // 转盘（约会）礼物流水
  8: i64 totalIncome; // 总流水
  9: i32 appid; // 业务id，34：语音房，58：yaya语音
}

struct TFamilyRoomDailyIncomePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfFamilyRoomDailyIncomeStat> contents; //  读5里面的流水-普通礼物流水

  6: i64 income;          // 总普通礼物流水 数据有点问题的
  7: i64 turnableIncome; // 总转盘礼物流水 数据有点问题的
  8: i64 totalIncome;    // 总流水 数据有点问题的
}

struct TZwyyfFamilyRoomIdDailyIncomeStat {
  1: i64 id; // ID
  2: i64 statDate; // 统计日期
  3: i32 appid; // 业务ID
  4: i64 familyId;// 家族ID
  5: i64 roomId;// 房间号
  6: i64 income;// 普通礼物流水
  7: i64 turnableIncome;// 技能卡礼物流水
  8: i64 updateDt; // 更新日期
}

struct TFamilyRoomIdDailyIncomePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfFamilyRoomIdDailyIncomeStat> contents;
  6: i64 totalIncome;
  7: i64 totalTurnableIncome;
}

struct TDatingDailyAnchorIncomeStatNotRoomMgr {
  1: i32 id;
  2: i64 statDate;
  3: i64 anchorUid;
  4: i64 sid;
  5: double propsIncome;  // 礼物流水
  6: double sealIncome; // 盖章流水（所有）
  7: double sealDragonIncome; // 神龙章流水
  8: double propsIncomeWithoutMagic; // 排除魔法礼物的流水
}

struct TDatingDailyRoomMgrIncomeStat {
  1: i64 id;
  2: i64 statDate;
  3: i64 roomMgrUid;
  4: i64 sid;
  5: double propsIncome;
  6: double sealIncome;
  7: i64 ssid;
  8: double sealDragonIncome;
  9: double propsIncomeWithoutMagic;
}

struct TDatingDailySidRoomTypeIncomeStat {
  1: i64 dt;
  2: i64 sid;
  3: i64 ssid;
  4: i32 roomType;
  5: i64 income;
}

service TDataCenterStatisticsService {
	/**
	 * 查询子厅有价值礼物流水
	 * @param appid=2 交友
	 * @param startDate 开始时间(包括这天),格式yyyymmdd
	 * @param endDate 结束时间(包括这天),格式yyyymmdd
	 * return<ssid, <yyyymmdd, 紫水晶>>>
	 */
  map<i64, map<string, i64>> querySsidIncome(1: i32 appid, 2: i64 sid, 3: list<i64> ssidList, 4: string startDate, 5: string endDate) throws (1: TServiceException ex1);

    /**
    * 查询子厅有价值礼物流水
    * @param appid=2 交友
    * @param startDate 开始时间(包括这天),格式yyyymmdd
    * @param endDate 结束时间(包括这天),格式yyyymmdd
    * return<ssid, <yyyymmdd, 紫水晶>>>
    */
    map<i64, map<string, i64>> querySsidIncomeNoBullet(1: i32 appid, 2: i64 sid, 3: list<i64> ssidList, 4: string startDate, 5: string endDate) throws (1: TServiceException ex1);

    /**
     * querySsidIncome第二版,返回礼物和盖章数据
     * 入参同querySsidIncome
     * 返回<ssid, 数据>
     */
  map<i64, list<TSsidConsumeData>> querySsidIncomeV2(1:i32 appid, 2:i64 sid, 3:list<i64> ssidList, 4:string startDate, 5:string endDate);

    /**
  	 * 交友appid = 2
  	 * startDate,endDate 格式:yyyymmdd
  	 */
  list<TPersonalRoomIncomeData> queryPersonalRoomIncome(1: i32 appid, 2: i64 sid, 3: list<i64> ssidList, 4: string startDate, 5: string endDate) throws (1: TServiceException ex1);

    /**
	* sid			0.返回全部
	* appid			2.返回交友
	* timeGreaterThan 	格式:yyyyMMddHHmmss, 包括这个时间
	* timeLessThan    	格式:yyyyMMddHHmmss, 包括这个时间
    */
  list<TDatingChannelIncomeStat> queryChannelIncomeStat(1: i64 sid, 2: i32 appid, 3: string timeGreaterThan, 4: string timeLessThan);
  
  /**
   * 查询新用户日消费金额
   * @param page 1为第一页，默认1
   * @param pagesize 每页数量，默认50
   * @param startDay 开始日期
   * @param endDay 结束日期
   * @param appid 2：交友，36：宝贝，34：语音房
   * @param types 1 普通礼物流水，2 互动礼物流水，3 盖章
   * @param uids 用户id
   */
  TNewUserDailyConsumeAmountPageResult queryNewUserDailyConsumeAmount(1: i32 page, 2: i32 pagesize, 3: i64 startDay, 4: i64 endDay, 5: TAppId appid, 6: list<i32> types, 7: list<i64> uids) throws (1: TServiceException ex1);

	/**
	* anchorUid		0.返回全部
	* timeGreaterThan 	格式:yyyyMMddHHmmss, 包括这个时间
	* timeLessThan    	格式:yyyyMMddHHmmss, 包括这个时间
	*/
  list<TDatingDailyAnchorIncomeStat> queryDatingDailyAnchorIncomeStatByAnchor(1: i64 anchorUid, 2: string timeGreaterThan, 3: string timeLessThan);

	/**
	* appid 	36.返回宝贝
	* uid 		0.返回全部
	* sid 		0.返回全部
	* startTime 	毫秒时间戳, 包括这个时间
	* endTime   	毫秒时间戳, 包括这个时间
	* srcTypes  	传空数组即可
	*/
  list<TRevenueSummaryRecord> queryRevenueSummaryRecord(1: i32 appid, 2: i64 uid, 3: i64 sid, 4: i64 startTime, 5: i64 endTime, 6: list<i32> srcTypes);

   /**
    * 查询家族流水明细（天）
    * appid 	业务ID  34
    * familyId 	家族id  0.返回全部
    * startTime 	开始日期 毫秒时间戳（毫秒）
    * endTime 	结束日期 毫秒时间戳（毫秒）
    */
  list<TFamilyDailyIncome> queryFamilyDailyIncome(1: TAppId appid, 2: i64 familyId, 3: i64 startTime, 4: i64 endTime) throws (1: TServiceException ex1);

   /**
   * 查询家族房间流水明细（天）
   * appid 业务ID	34
   * familyId 家族id	0.返回全部
   * liveSid 房间sid	0.返回全部
   * liveSsid 房间ssid	0.返回全部
   * startTime 开始日期 毫秒时间戳（毫秒）
   * endTime 结束日期 毫秒时间戳（毫秒）
   * page 页号(1, 2, 3...)
   * pagesize 每页大小
   * total 参数total >0 的时候 参数total=返回total，不再查询
   */
  TFamilyRoomDailyIncomePageResult queryFamilyDailyRoomIncome(1: TAppId appid, 2: i64 familyId, 3: i64 liveSid, 4: i64 liveSsid, 5: i64 startTime, 6: i64 endTime, 7: i32 page, 8: i32 pagesize, 9: i64 total) throws (1: TServiceException ex1);

  /**
   * 查询家族房间号流水明细（天）
   * @param appid 业务ID
   * @param familyId 家族id
   * @param roomId 房间号
   * @param startTime 开始日期 如 2021-12-30 00:00:00 的时间戳（毫秒）
   * @param endTime 结束日期 如 2021-12-30 00:00:00 的时间戳（毫秒）
   * @param page 页号(1, 2, 3...)
   * @param pagesize 每页大小
   * @param total >0则后台不进行count，并直接使用该值作为count
   * @param skipContent 0.返回contents明细, 1.不返回contents明细
   * @param skipSum =1则不汇总流水，返回totalIncome=0, totalTurnableIncome=0
   */
  TFamilyRoomIdDailyIncomePageResult queryFamilyDailyRoomIdIncome(1: TAppId appid, 2: i64 familyId, 3: i64 roomId, 4: i64 startTime, 5: i64 endTime, 6: i32 page, 7: i32 pagesize, 8: i64 total, 9: i32 skipContent, 10: i32 skipSum) throws (1: TServiceException ex1);


     /**
       * 公会礼物流水的前N名(魔法棒/水晶球不统计)
  	   * appid	2.交友
       * statDate  yyyyMMdd
       * size 最大返回数量
       */
  	list<TDatingChannelIncomeStat> getIncomeWithoutMagicGroupBySidTopN(1: i32 appid, 2: string statDate, 3: i32 size);

  	/**
       * 主持礼物流水的前N名(魔法棒/水晶球不统计)
       * appid	2.交友
  	   * statDate  yyyyMMdd
       * size 最大返回数量
       */
  	list<TDatingDailyAnchorIncomeStatNotRoomMgr> getIncomeWithoutMagicGroupByAnchorTopN(1: i32 appid, 2: string statDate, 3: i32 size);

  	/**
       * 厅ssid礼物流水总和的前10名(魔法棒/水晶球不统计)
       * appid	2.交友
  	   * statDate  yyyyMMdd
       * size 最大返回数量
       */
  	list<TDatingDailyRoomMgrIncomeStat> getIncomeWithoutMagicGroupBySsidTopN(1: i32 appid, 2: string statDate, 3: i32 size);

  	/**
     * 查询主持流水前x名
     * @param appid 2：交友
     * @param startDate 开始日期 yyyy-MM-dd
     * @param endDate 结束日期 yyyy-MM-dd
     * @param type 1 盖章 暂时只支持盖章
     * @param top 如：20，返回前20名
     */
    list<i64> queryTopAnchorIncome(1: TAppId appid, 2: i64 startDate, 3: i64 endDate, 4: i32 type, 5: i32 top) throws (1: TServiceException ex1);

   /**
    * 查询频道流水前x名
    * @param appid 2：交友
    * @param startDate 开始日期
    * @param endDate 结束日期
    * @param type 1 盖章 暂时只支持盖章
    * @param top 如：20，返回前20名
    */
   list<i64> queryTopSidIncome(1: TAppId appid, 2: i64 startDate, 3: i64 endDate, 4: i32 type, 5: i32 top) throws (1: TServiceException ex1);

    /**
    * 查询交友公会房管厅多人厅签约主持流水统计
    * @param startDate 开始日期 必填 毫秒闭区间
    * @param endDate 结束日期 必填 毫秒闭区间
    * @param sid 必填
    * @param ssid 可选
    * @param roomType 1：房管厅，2：多人厅
    */
    list<TDatingDailySidRoomTypeIncomeStat> queryDatingSidRoomTypeIncomeStat(1: i64 startDate, 2: i64 endDate, 3: i64 sid, 4: i64 ssid, 5: i32 roomType) throws (1: TServiceException ex1);
}
