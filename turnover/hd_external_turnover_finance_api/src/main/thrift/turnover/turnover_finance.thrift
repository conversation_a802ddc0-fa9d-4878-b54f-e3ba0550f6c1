namespace java com.yy.hd.api.thrift.turnover.finance

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"

typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TCurrencyType TCurrencyType
typedef turnover_common.TAppId TAppId
typedef turnover_common.TPropsType TPropsType

struct TSidInfo {
    1: i64 sid;
    2: i64 ssid;
}

struct TDatingDailyIncomeData{
  1: i64 dt;
  2: i64 uid;
  3: double ybPayAmount;
  4: double virtChargeAmount;
  5: double reddiamondConsumeAmount;
}

// 注意amount的单位都是厘
struct TDatingBigCustomerDailyStatistic {
  1: i64 id;
  2: i64 dt;
  3: i64 uid;
  4: i64 propAmount;    // 礼物
  5: i64 doubiAmount;   // 逗比章
  6: i64 nobleAmount;   // 贵族
  7: i64 guardAmount;   // 守护
  8: i64 pinkAmount;    // 粉钻
}

struct THatSidIncomeStat{
  1: i64 sid;
  2: i64 amount;
  3: i32 quota;
}

struct TAnchorIncomeStat{
  1: i64 anchorUid;
  2: i64 amount;
}

struct TAnchorMissionPrizeMonthDetail{
  1: i64 uid;
  2: i64 yyno;
  3: string nickName;
  4: i64 sid;
  5: i64 prizeYuan;
  6: string anchorType;
  7: string prizeType;
  8: double incomeYuan;
  9: string level;
}

struct TAnchorMissionPrizeMonthDetailList{
  1: list<TAnchorMissionPrizeMonthDetail> items;
}

// 结算来源类型
enum TRevenueSrcType {
  Props=1, Tutor=2, ExternalCharge=3, PropsWithoutDaySettleLevel=4, VipPkRevenue=5, FinanceStrategy=6, ForceRelieveContract=7, YoMallSalaryRevenue=8, XunhuanRoomRevenue=9, XunhuanPropsAndRoomRevenue=10, NiuWan=11, Ask=12, DATING_HATKING=13, IssueBonus=14, DATING_6P=15, DATING_PC6P=16, DATING_PC6P_VIRT=17, MGVRandom=18, DATING_6PFACE=19, XunhuanActSubsidy=20, DatingSeal=21, DatingSoccer=22, DatingYYLiveRedPacket=23, Award=24, QuasiSuper=25, DATING_AUCTION_AWARD=26, DATING_AUCTION_PROPS=27, XUNHUAN_SPECIAL_PROPS_REVENUE=28, VIPPK_PC6P=29, VIPPK_PC6P_EXTRA=30, DATING_PC_YYF=31, DATING_PC_YYFHD=32, VIPPK_SUPER_ANCHOR_ADDITION=33
}

struct TAnchorDailyIncome {
  1: i64 revenueDate;           // 日结时间
  2: i64 tid;                   // 厅id
  3: i64 anchorUid;             // 主持uid
  4: i64 anchorImid;            // 主持YY号
  5: string anchorNick;         // 主持昵称
  6: double anchorIncome;       // 待日结黄水晶
  7: TRevenueSrcType srcType;   // 结算来源类型：1：礼物；21：盖章
  8: i32 owWeight;              // 公会分成比例
  9: i32 tingWeight;            // 厅管抽成比例
  10: double tingIncome;        // 厅管总日结黄水晶
}

struct TAnchorDailyIncomePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TAnchorDailyIncome> contents;
}

struct TMonthAnchorGiftIncomeStat {
  1: i64 anchorUid;
  2: i64 amount;
}

struct TZwyyfRoomDailyRevenueStat {
  1: i64 dt;                        // 日结时间
  2: i32 appid;                     // 业务ID
  3: i64 uid;                       // 房主uid
  4: double realIncome;             // 普通礼物黄钻日结
  5: double turntableRealIncome;    // 约会礼物黄钻日结
  6: double otherRealIncome;        // 其他黄钻收入
  7: double totalRealIncome;        // 合计黄钻收入
}

struct TZwyyfAnchorDailyRevenueStat {
  1: i64 dt;                    // 日结时间
  2: i32 appid;                 // 业务ID
  3: i64 uid;                   // 主播uid
  4: i64 sid;                   // 工会sid
  5: double allIncome;          // 普通礼物金钻流水
  6: i32 rate;                  // 普通礼物日结比例
  7: double allIncomeTurntable; // 约会礼物金钻流水
  8: i32 rateTurntable;         // 约会礼物日结比例
  9: double weight;             // 房主抽成比例
  10: double anchorDailyIncome; // 主播日结黄钻
  11: double owDailyIncome;     // 房主日结黄钻
  12: string expand;            // 扩展字段 {"agentUid":"xxx"}
}

struct TDailyRoomRevenuePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfRoomDailyRevenueStat> contents;
}

struct TDailySignedAnchorRevenue {
  1: i64 revenueDate;
  2: i64 imid;
  3: i64 income;
  4: i64 incomeRate;
  5: i64 turntableIncome;
  6: i64 turntableIncomeRate;
  7: i64 weight;
  8: i64 totalIncome;
  9: i64 roomTotalIncome;
}

struct TDailySignedAnchorRevenuePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfAnchorDailyRevenueStat> contents;
}

struct TSignedAnchorPropsUsedRecord {
  1: i64 id;                // 记录ID
  2: i64 usedTime;          // 送礼时间
  3: TPropsType type;       // 礼物类型 2 收费道具, 14 宝箱礼物 17 约会礼物
  4: string name;           // 礼物名称
  5: i32 count;             // 礼物数量
  6: i64 senderImid;        // 送礼YY号
  7: string senderNick;     // 送礼昵称
  8: i64 receiverImid;      // 收礼YY号
  9: string receiverNick;   // 收礼昵称
  10: i64 signedSid;        // 签约房间
  11: i64 sid;              // 开播房间
  12: i32 currencyType;     // 货币类型 金钻-68
  13: double currencyAmount;// 价格
  14: i64 ssid;             // 开播房间ssid
  15: i64 familyId;         // 家族id
}

struct TSignedAnchorPropsUsedRecordPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TSignedAnchorPropsUsedRecord> contents;
}

struct TExchangePropsHistoryResult {
  1: i64 uid; // 用户id
  2: i64 createTime; // 兑换时间
  3: i32 type;
  4: i32 srcPropId; // 消耗道具id
  5: i64 srcPropCnt; // 消耗道具数量
  6: i32 srcPropPricingId;
  7: i32 srcCurrencyType;
  8: i64 srcCurrencyAmount;
  9: i32 destPropId; // 兑换道具id
  10: i64 destPropCnt; // 兑换道具数量
  11: i32 destPropPricingId;
  12: i32 destCurrencyType;
  13: i64 destCurrencyAmount;
  14: i32 appid; // 业务id
  15: i64 exchangeConfigId;
  16: string srcPropName; // 消耗道具名称
  17: string destPropName; // 兑换道具名称
  18: string exchangeConfigName; // 兑换配置名称
  19: i64 sid;
  20: i64 ssid;
  21: i64 csid; // 签约频道id
  22: i64 anchorUid;
}

struct TExchangePropsHistoryPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TExchangePropsHistoryResult> contents;
}

struct TProductConsumeLevel{
  1: i32 appid;
  2: i64 uid;
  3: i32 level; //加油派对等级
  4: i64 allAmount; //当前经验
  5: i64 todayAmount; //当日累计经验
  6: i64 nextAmount; //下一级经验
}

struct TChannelIncomeRank{
  1: i32 rank;
  2: i64 sid;
  3: double income;
}

struct TDatingDailyTingMgrIncomeStat {
  1: i64 id;
  2: i64 dt;
  3: i32 appid;
  4: i64 uid;
  5: double income;
}

struct TFunctionUserWhiteList {
  1: i64 id;
  2: i32 appid;
  3: string funcName;
  4: i64 startTime;
  5: i64 endTime;
  6: i32 status;
  7: string targetId;
  8: i64 addTime;
  9: string expand;     //数据
  10: i64 operatorUid;  //操作人
}

struct TRealAmountInfo{
  1: i64 uid;
  2: i64 queryTime;
  3: i64 realAmount;		//当前黄水晶
  4: i64 maxSendAmount;		//最大发送额度
  5: i64 maxRecvAmount;		//最大接收额度
  6: i64 currSendAmount;	//当前发送额度
  7: i64 currRecvAmount;	//当前接收额度
}

/**
 * statDate格式:yyyymmdd
 */
struct TPersonalRoomIncomeData{
	1: i64 sid;
	2: i64 ssid;
	3: string statDate;
	4: i64 superIncome;
	5: i64 normalIncome;
    6: i64 starlightIncome;
}

struct TSsidConsumeData {
    1: i64 ssid;
    2: string dt;
    3: i64 propsAmount;
    4: i64 sealAmount;
}

struct TDatingDailyPersonalRoomStat {
  1: i32 id;
  2: i64 statDate;
  3: i64 sid;
  4: i64 ssid;
  5: i64 anchorUid;
  6: i32 isSuper;
  7: i64 propsIncome;
  8: i64 sealIncome;
}

struct TDatingDailyAnchorIncomeStatNotRoomMgr {
  1: i32 id;
  2: i64 statDate;
  3: i64 anchorUid;
  4: i64 sid;
  5: double propsIncome;  // 礼物流水
  6: double sealIncome; // 盖章流水（所有）
  7: double sealDragonIncome; // 神龙章流水
  8: double propsIncomeWithoutMagic; // 排除魔法礼物的流水
}

struct TDatingDailyRoomMgrIncomeStat {
  1: i64 id;
  2: i64 statDate;
  3: i64 roomMgrUid;
  4: i64 sid;
  5: double propsIncome;
  6: double sealIncome;
  7: i64 ssid;
  8: double sealDragonIncome;
  9: double propsIncomeWithoutMagic;
}

struct TDatingDailyAnchorIncomeStat {
  1: i32 id;
  2: i64 statDate;
  3: i64 anchorUid;
  4: i64 sid;
  5: double propsIncome; // propsIncome 礼物流水,单位元(要先四舍五入再乘以1000才是紫水晶)
  6: double sealIncome;
  7: double sealDragonIncome;
}

struct TRevenueSummaryRecord {
  1: i64 id;
  2: i64 uid;
  3: i64 sid;
  4: double income;
  5: i64 optTime;
  6: i64 revenueDate; // 统计日期毫秒时间戳
  7: i32 appid;
  8: i32 userType;
  9: i32 srcType;
  10: string expand;
  11: double allIncome; // 礼物流水,单位紫水晶
}

struct TFamilyDailyIncome {
  1: i64 statDate;    // 统计日期毫秒时间戳
  2: i64 totalIncome; // 总礼物流水,单位紫水晶
  3: i64 familyId; // 家族id
}

struct TZwyyfFamilyRoomDailyIncomeStat {
  1: i64 statDate; // 日期
  2: i64 familyUid; // 家族长uid
  3: i64 familyId; // 家族id
  4: i64 liveSid; // 收礼房间sid
  5: i64 liveSsid; // 收礼房间ssid
  6: i64 income; // 普通礼物流水
  7: i64 turnableIncome; // 转盘（约会）礼物流水
  8: i64 totalIncome; // 总流水
  9: i32 appid; // 业务id，34：语音房，58：yaya语音
}

struct TFamilyRoomDailyIncomePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfFamilyRoomDailyIncomeStat> contents; //  读5里面的流水-普通礼物流水

  6: i64 income;          // 总普通礼物流水 数据有点问题的
  7: i64 turnableIncome; // 总转盘礼物流水 数据有点问题的
  8: i64 totalIncome;    // 总流水 数据有点问题的
}

struct TZwyyfFamilyRoomIdDailyIncomeStat {
  1: i64 id; // ID
  2: i64 statDate; // 统计日期
  3: i32 appid; // 业务ID
  4: i64 familyId;// 家族ID
  5: i64 roomId;// 房间号
  6: i64 income;// 普通礼物流水
  7: i64 turnableIncome;// 技能卡礼物流水
  8: i64 updateDt; // 更新日期
}

/**
* 礼物流水从contents里读取
*/
struct TFamilyRoomIdDailyIncomePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfFamilyRoomIdDailyIncomeStat> contents;
  6: i64 totalIncome;
  7: i64 totalTurnableIncome;
}

struct TDatingChannelIncomeStat {
  1: i64 id;
  2: i64 statDate;
  3: i32 sid;
  4: double giftIncome; // 礼物流水,单位元(要先四舍五入再乘以1000才是紫水晶)
  5: double nobleIncome;
  6: double pinkIncome;
  7: double guardIncome;
  8: double sealIncome;
  9: double sealDragonIncome;
  10: double propsIncomeWithoutMagic; // propsIncomeWithoutMagic: 排除魔法礼物的流水
}

struct TAnchorIncomeStatData{
  1: i64 statDate;
  2: i64 anchorUid;
  3: i64 sid;
  4: i64 ssid;
  5: double propsIncome;
  6: double sealIncome;
}

struct TNewUserDailyConsumeAmount {
  1: i64 dt;
  2: i32 appid;
  3: i64 uid;
  4: i32 type;
  5: i64 amount;
}

struct TNewUserDailyConsumeAmountPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TNewUserDailyConsumeAmount> contents;
}

/**
	propsIncome单位:厘
*/
struct TPersonalRoomIncomeRankData {
	1: i64 sid
	2: i64 ssid
	3: i64 roomUid
	4: i64 propsIncome
}

/**
*	流水单位:厘
*/
struct TDatingDailyRoomStat{
	1: i64 sid
	2: i64 ssid
	3: i64 anchorUid
	4: i64 statDate
	5: i64 propsIncome
	6: i64 sealIncome
}

struct TUserPropsPeriodHistory {
  1: i64 id;
  2: i64 periodId;
  3: string seqId;
  4: i64 uid;
  5: i64 propId;
  6: i64 pricingId;
  7: i32 appid;
  8: i64 oriCnt;
  9: i64 changeCnt;
  10: string description;
  11: i32 srcType;
  12: i64 activityId;
  13: i64 createTime; // 操作时间
  14: i64 startTime;
  15: i64 endTime;
  16: string userIp;
  17: string expand;
  18: i32 usedChannel;
}

struct TUserPropsPeriodHistoryPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TUserPropsPeriodHistory> contents;
}

struct TDatingDailySidRoomTypeIncomeStat {
  1: i64 dt;
  2: i64 sid;
  3: i64 ssid;
  4: i32 roomType;
  5: i64 income;
}


struct TDatingRoomRecvIncomeStat {
  /**
   * 日期
   */
  1: i64 dt;

  /**
   * ssid
   */
  2: i64 ssid;

  /**
   * 收礼人uid
   */
  3: i64 recvUid;

  /**
   * 流水金额
   */
  4: i64 propsIncome;

  /**
   * 1：主持，2：嘉宾
   */
  5: i32 recvUserType;
}

service TRevenueServiceV2 {
  /**
   * 统计用户在指定日期范围内的日结收入
   * @param appIds 业务ID列表 必填，2交友，14约战
   * @param uid 用户uid
   * @param startDay 开始日期(精确到毫秒，非日期部分填0)
   * @param endDay 结束日期(精确到毫秒，非日期部分填0)
   * @return <业务ID, 日结收入统计(单位厘)>
   */
  map<i32, double> sumRevenueRecByDayRange(1: list<i32> appIds, 2: i64 uid, 4: i64 startDay, 5: i64 endDay) throws (1: TServiceException ex1);

  /**
     * 统计频道在指定日期范围内的有收入主持数
     * @param appId 业务ID列表 必填，2交友，14约战
     * @param sids 频道id
     * @param startTime 开始日期(精确到毫秒)
     * @param endTime 结束日期(精确到毫秒)
     * @return <业务ID, 日结收入统计(单位厘)>
     */
  map<i64, i32> batchGetRevenueAnchorCountByDay(1: i32 appid, 2: list<i64> sids, 3: i64 startTime, 4: i64 endTime, 5: i64 minIncome, 6: list<i32> srcTypes);

   /**
    * 统计频道在指定日期范围内的日结收入 @李启华
    * @param appid
    * @param sids
    * @param startTime 开始日期(到毫秒，闭区间，>=)
    * @param endTime 结束日期(到毫秒，开区间，<)
    * @param srcTypes 结算类型
    * @param excludeSrcTypes 排除结算类型
    * @return <sid, 日结收入统计(单位厘)>
    */
   map<i64, double> sumSidRevenueRecByDayRange(1: TAppId appid, 2: list<i64> sids, 3: i64 startTime, 4: i64 endTime, 5: list<i32> srcTypes, 6: list<i32> excludeSrcTypes) throws (1: TServiceException ex1);
}

service TPropsStatServiceV2 {
    void ping2()
    map<i64, double> last7DayAnchorPropsUseForStat(1: list<i64> anchorUids, 2: TAppId appid);

  /**
   * 查询指定时间段内,某活动下发的礼物价值(折算成紫水晶)
   * 慢查询,仅用于对账,endTime-startTime 每次最多查3天
   * @param appId
   * @param activityId 活动id
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return 礼物紫水晶价值
   */
    i64 getTotalIssuePropsAmountByActivity(1: i32 appId, 2: i32 activityId, 3: i64 startTime, 4: i64 endTime);

   /**
    * 是否用户最近消费过
    * @param startTime 统计起始时间
    * @return 0.没有  1.有
    */
    i32 isUserConsumedRecently(1: i64 uid, 2: TAppId appid, 3: i64 startTime);

   /**
    * 统计子频道礼物消费总额记录数
    * (每天1点生成昨天的数据,因为是从hive取数,所以即使是测试环境,查到的数据也是生产数据)
    * @param appid 交友:2
    * @param date yyyy-MM-dd
    * @param sidInfo sid信息
    * @return nil: 表示未生成数据
    */
    map<TSidInfo, i64> queryDailySsidConsume(1: i32 appid, 2: string date, 3: list<TSidInfo> sidInfoList);

    //queryTime 格式"yyyy-mm"
    //return map<sid, 流水总和>
    map<i64,double> queryGiftIncomeByMonth(1: i32 appid, 2: string queryTime, 3: list<i64> sidList);

    
    /**
    * 获取N天用户流水总和(N-2天的统计数据+2天的实时数据) (旧接口,即将下线)
    * @param uid 				用户uid 
    * @param appid 				业务id(交友填2)
    * @param daysBeforeList     待查的天数列表(比如list(0,1,30)表示查今天,1天内(昨天和今天), 30天内(今天和之前30天)的数据)
    */
    map<i32, TDatingDailyIncomeData> queryDailyIncomeData(1: i32 appid, 2: i64 uid, 3: list<i32> daysBeforeList);

     /**
       * 查询交友大客户日统计数据
       * 查旧统计口径的数据，汇聚指定时间范围的数据，上线之后90天内需要使用，之后可以下线
       * @param appid 业务id
       * @param uid 客户uid
       * @param fromDate 开始日期yyyyMMdd
       * @param toDate 结束日期yyyyMMdd
       * @return DatingDailyIncomeData（注意单位是元）
     */
     TDatingDailyIncomeData queryDailyIncomeDataV2(1: i32 appid, 2: i64 uid, 3: string fromDate, 4: string toDate);

    /**
     * 查询交友大客户日统计数据
     * 查新统计口径的数据，汇聚指定时间范围的数据
     * @param appid 业务id
     * @param uid 客户uid
     * @param fromDate 开始日期yyyyMMdd
     * @param toDate 结束日期yyyyMMdd
     * @return DatingBigCustomerDailyStatistic（单位：厘）
     */
    TDatingBigCustomerDailyStatistic queryDatingBigCustomerDailyStatistic(1: i32 appid, 2: i64 uid, 3: string fromDate, 4: string toDate);

    /*
     * 定时计算工会流水 （每月1号)
     * queryMonth格式: yyyymm
     */
    list<THatSidIncomeStat> queryHatSidIncomeStat(1: i32 appid, 2: string queryMonth, 3: i64 sid);

    /*
     * 定时计算特定主持的盖章流水
     * queryMonth格式: yyyymm
     */
    list<TAnchorIncomeStat> queryMonthAnchorSealIncomeStat(1: i32 appid, 2: string queryMonth, 3: list<i64> anchorUidList);

    // 0：（默认）紫水晶券、紫水晶、Y币等扣费，1：仅使用吃鸡队友金扣费，2：仅使用紫金币扣费
    i32 getCurrencyConsumeMode(1: i32 appid, 2: i64 uid);

    /*
    * 查询指定月流水范围的主播uid
    * appid
    * queryMonth
    * anchorUidList
    * minIncome 最小金额(包括该值,忽略该字段填0)
    * maxIncome 最大金额(不包括该值,忽略该字段填0)
    * 返回 主播uid列表
     */
    list<i64> queryGiftIncomeAnchorsByMonth(1: i32 appid, 2: string queryMonth, 3: double minIncome, 4: double maxIncome);

    /**
    * 查询主持某月结算等级次数
    * queryMonth 格式yyyymm
    * srcType:结算类型 1.普通结算  39.多人结算
    * 返回<等级, 次数> 比如 {"A":7,"B":3,"C":4,"D":4}
    */
    map<string, i32> queryAnchorRevenueLevelBySrcType(1:i32 appid ,2:i64 uid, 3:string queryMonth, 4:i32 srcType)
    // @丁拥 settleMode 结算模式，默认"AnchorMode"。 "AnchorMode"：主持人模式；"ReceiverMode"：收礼人模式；
    map<string, i32> queryAnchorRevenueLevel(1:i32 appid ,2:i64 uid, 3:string queryMonth, 4: string settleMode)

   /**
    * 多人视频抽成玩法
    * 返回 <sid或ssid, 结果>  1:白名单  0.非白名单
    */
    map<i64, i32> querySidInWhiteList(1:i32 appid, 2:i64 sid,  3:i64 ssid)

    /**
    * 全量查询多人视频抽成玩法白名单
    * 返回 map<"sid"/"ssid", sid/ssid列表> 比如 {"sid": ["1", "2", "3"], "ssid": ["4", "5", "6"]}
    * 白名单为空, 返回{}
    */
    map<string, list<string>> querySidWhiteList(1: i32 appid)

    /**
    * 查询主持某月日结档位次数
    * queryMonth 格式yyyymm
    * srcType:结算类型 1.普通结算  39.多人结算
    * uid 为0,则批量查询。
    */
    TAnchorMissionPrizeMonthDetailList queryAnchorRevenueLevelDetailBySrcType(1:i32 appid ,2:string queryMonth, 3:i64 uid,  4:i32 srcType)
    // @丁拥 settleMode 结算模式，默认"AnchorMode"。 "AnchorMode"：主持人模式；"ReceiverMode"：收礼人模式；
    TAnchorMissionPrizeMonthDetailList queryAnchorRevenueLevelDetail(1:i32 appid ,2:string queryMonth, 3:i64 uid, 4: string settleMode)

    /**
     * 查询公会指定月份流水 单位/元
     */
    map<i64, double> queryDatingChannelPropsIncome(1: list<i64> sidList, 2: string yyyyMM);

    /**
    * 查主持日结明细
    * @param appid 业务ID 必填
	* @param sid 公会id 必填
	* @param tingMgrUid 厅管uid 必填
	* @param tid 厅ID
	* @param anchorImid 主持YY号
	* @param startTime 日结开始时间
	* @param endTime 日结结束时间 
	* @return AnchorDailyIncomePageResult
    */
    TAnchorDailyIncomePageResult queryAnchorDailyIncomes(1: TAppId appid, 2: i64 sid, 3: i64 tingMgrUid, 4: i64 tid, 5: i64 anchorImid, 6: i64 startTime, 7: i64 endTime, 8: i32 page, 9: i32 pagesize) throws (1: TServiceException ex1);

    /**
    * 查指定主播的月礼物流水（单位：黄水晶）
    * @param appid 业务ID
    * @param queryMonth 格式: yyyymm
    * @param anchorUidList 主播uid列表
    * @return List&lt;MonthAnchorGiftIncomeStat&gt;
    */
    list<TMonthAnchorGiftIncomeStat> queryMonthAnchorGiftIncomeStat(1: TAppId appid, 2: string queryMonth, 3: list<i64> anchorUidList) throws (1: TServiceException ex1);

    /**
    * 查询每日房主的黄钻收入
    * @param appid 业务ID
    * @param uid 房主uid
    * @param startTime 开始日期
    * @param endTime 结束日期
    * @param page 页号(1, 2, 3...)
    * @param pagesize 每页大小
    * @return DailyRoomRevenuePageResult
    * @throws ServiceException
    */
    TDailyRoomRevenuePageResult queryDailyRoomRevenues(1: TAppId appid, 2: i64 uid, 3: i64 startTime, 4: i64 endTime, 5: i32 page, 6: i32 pagesize) throws (1: TServiceException ex1);

    /**
    * 查询每日频道签约主播的日结黄钻
    * @param appid 业务ID
    * @param sid 工会sid
    * @param imid 主持YY号
    * @param startTime 开始日期
    * @param endTime 结束日期
    * @param page 页号(1, 2, 3...)
    * @param pagesize 每页大小
    * @return 符合条件的记录列表
    * @throws DailySignedAnchorRevenuePageResult
    */
    TDailySignedAnchorRevenuePageResult queryDailySignedAnchorRevenues(1: TAppId appid, 2: i64 sid, 3: i64 imid, 4: i64 startTime, 5: i64 endTime, 6: i32 page, 7: i32 pagesize) throws (1: TServiceException ex1);

    /**
    * 查看旗下签约主播每条收到礼物明细记录
    * @param appid 业务ID
    * @param sid 工会sid。若imid>=0,则按imid查，不按sid查。
    * @param imid 主持YY号。若imid>=0,则按imid查，不按sid查。
    * @param startTime 开始日期 yyyy-MM-dd
    * @param endTime 结束日期 yyyy-MM-dd
    * @param page 页号(1, 2, 3...)
    * @param pagesize 每页大小
    * @return SignedAnchorPropsUsedRecordPageResult
    * @throws ServiceException
    */
    TSignedAnchorPropsUsedRecordPageResult querySignedAnchorPropsUsedRecords(1: TAppId appid, 2: i64 sid, 3: i64 imid, 4: i64 startTime, 5: i64 endTime, 6: i32 page, 7: i32 pagesize) throws (1: TServiceException ex1);

    /**
     * 查询用户道具兑换记录(按时间倒序)
     * @param appid 业务ID 必填
     * @param uid 用户ID 必填
     * @param srcPropId 消耗礼物id，豆荚：20209 必填
     * @param type 0-不区分类型 1-道具，2-货币, 3-货币券, 4-交友抽取道具券, 5-勋章
     * @param page 页码，1为第一页，默认1 必填
     * @param pagesize 每页数量，默认50 必填
     */
    TExchangePropsHistoryPageResult getExchangePropsHistorys(1: TAppId appid, 2: i64 uid, 3: i32 srcPropId, 4: i32 type, 5: i64 date, 6: i32 page, 7: i32 pagesize) throws (1: TServiceException ex1);

    /**
     * 查询用户道具兑换记录
     * @param appid 业务ID 必填
     * @param uid 用户ID
     * @param srcPropId 消耗礼物id，豆荚：20209 必填
     * @param type 1-道具，2-货币, 3-货币券, 4-交友抽取道具券, 5-勋章
     * @param startTime 起始时间
     * @param endTime 结束时间
     * @param page 页码，1为第一页，默认1 必填
     * @param pagesize 每页数量，默认50 必填
     */
    TExchangePropsHistoryPageResult getExchangePropsHistorysV2(1: TAppId appid, 2: i64 uid, 3: i32 srcPropId, 4: i32 type, 5: i64 startTime, 6: i64 endTime, 7: i32 page, 8: i32 pagesize) throws (1: TServiceException ex1);

   /**
    * 查交友频道礼物收入
    * @param sidList
    * @param fromDate (包括该日) yyyymmdd 0点
    * @param toDate  (不包括该日) yyyymmdd 0点
    * fromDate "20210528",  toDate "20210628" =  20210528 0点 到 20210628 0点期间的流水
    * @return
    */
    map<i64, double> queryDatingChannelPropsIncomeByDate(1: list<i64> sidList, 2: string fromDate, 3: string toDate);

    /**
     * 查询公会有价值礼物流水
     * return 紫水晶
     */

    i64 querySidIncomeByMonth(1:i32 appid, 2:i64 sid, 3:string yyyymm) throws (1: TServiceException ex1);

    /**
     * 查询公会有价值礼物流水(不包含弹幕礼物)
     * return 紫水晶
     */
    i64 querySidIncomeByMonthNoBullet(1:i32 appid, 2:i64 sid, 3:string yyyymm) throws (1: TServiceException ex1);


    /** 查询达人等级
    * appid 业务id
    * uid
    * @return TProductConsumeLevel
    */
    /*TProductConsumeLevel getUserProductConsumeLevel(1: i32 appid, 2: i64 uid);*/

    /** 批量查询达人等级
    * appid 业务id
    * uidList
    * @return TProductConsumeLevel
    */
    /*list<TProductConsumeLevel> getUsersProductConsumeLevel(1: i32 appid, 2:list<i64> uidList)*/

    /**
     * 按日期查询有收入主持数量
     * @param appid 业务ID 必填
     * @param sid 频道ID 必填
     * @param startTime 开始时间 必填
     * @param endTime 结束时间 必填
     * @return Map<结算日期,主持数量>
     */
    map<i64, i64> countGiftIncomeAnchor(1: TAppId appid, 2: i64 sid, 3: i64 startTime, 4: i64 endTime) throws (1: TServiceException ex1);

     /**
      * 主持礼物日流水 (按天统计，最多取最近90天，包括今天)
      * @param appId 业务ID 必填
      * @param anchorUid 主持uid
      * @param startDay 日结开始日期
      * @param endDay 日结结束日期
      * @return <'yyyy-MM-dd', 日流水（元）>
      */
     map<string, double> getAnchorPropIncomesByDay(1: TAppId appId, 2: i64 anchorUid, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
     * 批量查主持礼物日流水 (按天统计，最多取最近90天，包括今天)
     * @param appId 业务ID 必填
     * @param anchorUid 主持uid
     * @param startDay 日结开始日期
     * @param endDay 日结结束日期
     * @return <uid,<'yyyy-MM-dd', 日流水（元）>>
    */
    map<i64, map<string, double>> batchGetAnchorPropIncomesByDay(1: TAppId appId, 2: list<i64> anchorUids, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

     /**
      * 主持礼物月流水 (按月统计，最多取最近6个月，包括本月)
      * @param appId 业务ID 必填
      * @param anchorUid 主持uid
      * @param startMonth 月结开始月份
      * @param endMonth 月结结束月份
      * @return <'yyyy-MM', 月流水（元）>
      */
     map<string, double> getAnchorPropIncomesByMonth(1: TAppId appId, 2: i64 anchorUid, 3: i64 startMonth, 4: i64 endMonth) throws (1: TServiceException ex1);

    /**
     * 查主持日结黄水晶（仅支持查最近6个自然月）实时 (各种交友结算模式都会有覆盖到)
     * @param appId 业务ID 必填
     * @param anchorUid 主持uid
     * @param startDay 日结开始日期 ms
     * @param endDay 日结结束日期 ms
     * @return <'yyyy-MM-dd', 日结黄水晶>
     */
    map<string, i64> getAnchorPropRevenueIncomesByDay(1: TAppId appId, 2: i64 anchorUid, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 批量查主持日结黄水晶（仅支持查最近6个自然月）
    * @param appId 业务ID 必填
    * @param anchorUids 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <uid,<'yyyy-MM-dd', 日结黄水晶>>
    */
    map<i64, map<string, i64>> batchGetAnchorPropRevenueIncomesByDay(1: TAppId appId, 2: list<i64> anchorUids, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

   /**
    * 最近7天频道礼物流水排名
    * days: 往前查几天
    */
   list<TChannelIncomeRank> queryGiftIncomeRankByDays(1: i32 appid, 2: i32 days, 3: list<i64> sidList);

    /**
    * 查询交友全部频道总流水
    * @param appId      业务ID 必填
    * @param startTime  开始时间(如20211001)
    * @param endTime    结束时间(如20211031)
    * @return 所有频道总礼物流水
    */
    double queryAllGiftIncome(1: i32 appid, 2: string startTime, 3: string endTime) throws (1: TServiceException ex1);

    /**
    * 查询交友全部频道总流水(不包含弹幕礼物)
    * @param appId      业务ID 必填
    * @param startTime  开始时间(如20211001)
    * @param endTime    结束时间(如20211031)
    * @return 所有频道总礼物流水
    */
    double QueryAllGiftIncomeNoBullet(1: i32 appid, 2: string startTime, 3: string endTime) throws (1: TServiceException ex1);


    /**
    * 批量查询每日厅管礼物流水（包含今天的数据，今天的数据每小时刷新）
    * @param tingMgrUids 厅管uid列表
    * @param timeGreaterThan 开始日期
    * @param timeLessThan 结束日期
    * @return List<DatingDailyTingMgrIncomeStat>
    * @throws ServiceException
    */
    list<TDatingDailyTingMgrIncomeStat> batchQueryDatingDailyTingMgrIncomeStat(1: list<i64> tingMgrUids, 2: i64 timeGreaterThan, 3: i64 timeLessThan) throws (1: TServiceException ex1);

    /**
    * 查询每日厅管礼物流水（包含今天的实时数据）
    * @param tingMgrUid 厅管uid
    * @param timeGreaterThan 开始日期
    * @param timeLessThan 结束日期
    * @return List<DatingDailyTingMgrIncomeStat>
    * @throws ServiceException
    */
    list<TDatingDailyTingMgrIncomeStat> queryDatingDailyTingMgrIncomeStatWithTodayRealTime(1: i64 tingMgrUid, 2: i64 timeGreaterThan, 3: i64 timeLessThan) throws (1: TServiceException ex1);

    /**
    * 主持礼物日流水 只含房管模式(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <'yyyy-MM-dd', 日流水（元）>
    */
    map<string, double> getAnchorPropIncomesByDayInRoomMgr(1: TAppId appId, 2: i64 anchorUid, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 主持礼物日流水 排除房管模式(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <'yyyy-MM-dd', 日流水（元）>
    */
    map<string, double> getAnchorPropIncomesByDayNotRoomMgr(1: TAppId appId, 2: i64 anchorUid, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 批量查主持礼物日流水 天团厅房管(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <uid, <'yyyy-MM-dd', 日流水（元）>>
    */
    map<i64, map<string, double>> batchGetAnchorPropIncomesByDayInRoomMgr(1: TAppId appId, 2: list<i64> anchorUids, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 批量查主持礼物日流水 个播厅房管(按天统计，最多取最近90天，不包括今天)
    * @param startDate 日结开始日期
    * @param endDate 日结结束日期
    * @param isSuper 0:普通主持 1：超级主持
    *
    */
    list<TDatingDailyPersonalRoomStat> getPersonRoomPropsIncomeByDate(1: i64 startDate, 2: i64 endDate, 3: i32 isSuper, 4: i64 sid, 5: i64 ssid);

    /**
    * 批量查主持礼物日流水 排除房管模式(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <uid, <'yyyy-MM-dd', 日流水（元）>>
    */
    map<i64, map<string, double>> batchGetAnchorPropIncomesByDayNotRoomMgr(1: TAppId appId, 2: list<i64> anchorUids, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 主持礼物日流水 按ssid维度查 只含房管模式(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param ssid ssid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <'yyyy-MM-dd', 日流水（元）>
    */
    map<string, double> getAnchorPropIncomesBySsidInRoomMgr(1: TAppId appId, 2: i64 anchorUid, 3: i64 ssid, 4: i64 startDay, 5: i64 endDay) throws (1: TServiceException ex1);

    //返回  map<ssid, 紫水晶>
    map<i64, i64> getSsidMonthlyIncomeFromRank(1: i64 sid, 2: list<i64> ssidList, 3: string yyyymm) throws (1: TServiceException ex1);

    /**
      * 主持日流水 只含房管模式(按天统计，最多取最近90天，包括今天)
      * @param appId 业务ID 必填
      * @param sid 公会sid 填0表示忽略
      * @param anchorUid 主持uid
      * @param startDay 日结开始日期
      * @param endDay 日结结束日期
      * @return List<DatingDailyAnchorIncomeStatNotRoomMgr>
    */
    list<TDatingDailyAnchorIncomeStatNotRoomMgr> getDatingDailyAnchorIncomeStatNotRoomMgrV2(1: TAppId appId, 2: i64 sid, 3: i64 anchorUid, 4: i64 startDay, 5: i64 endDay) throws (1: TServiceException ex1);

    // 房管模式下按照ssid 查询
    list<TAnchorIncomeStatData> queryAnchorIncomesBySsidInAllRoomMode(1: TAppId appid, 2: i64 anchorUid, 3: i64 ssid, 4: i64 startDay, 5: i64 endDay) throws (1: TServiceException ex1);

    /**
     * 查个播厅本周topN流水 @吴熙
     */
    list<TPersonalRoomIncomeRankData> queryTopNPersonalRoomIncomeThisWeek(1: TAppId appid, 2: i32 limitSize)

    /**
     * 日期格式: yyyymmdd 流水单位/紫水晶 @吴熙
     */
    list<TDatingDailyRoomStat> getRoomIncome(1: string startYMD, 2: string endYMD, 3:i64 sid, 4:i64 ssid);

    /**
      "查询某月房管厅普通主持礼物流水之和",
      "@param appId 业务ID",
      "@param ssid 房管ssid",
      "@param queryPublicRoom 是否累计天团厅流水",
      "@param queryPersonalRoom 是否累计个播厅流水",
      "@param monthOffset 0.本月 -1.上月 -2.上上月",
      "@return 礼物流水(元)"
      */
    double queryRoomNormalPropsIncomeByMonth(1: TAppId appId, 2: i64 ssid, 3: bool queryPublicRoom, 4: bool queryPersonalRoom, 5: i32 monthOffset) throws (1: TServiceException ex1);

   /**
    "查询某月房管厅 普通主持\超级主持\星光主持 礼物流水礼物流水之和",
    "@param appId 业务ID",
    "@param ssid 房管ssid",
    "@param queryPublicRoom 是否累计天团厅流水",
    "@param queryPersonalRoom 是否累计个播厅流水",
    "@param monthOffset 0.本月 -1.上月 -2.上上月",
    "@param noBullet false:所有礼物；true排除弹幕礼物",
    "@return 礼物流水(元)"
    */
    double queryRoomAllPropsIncomeByMonth(1: TAppId appId, 2: i64 ssid, 3: bool queryPublicRoom, 4: bool queryPersonalRoom, 5: i32 monthOffset, 6:bool noBullet) throws
    (1: TServiceException ex1);

    /**
     * 查询用户指定礼物的变更流水
     * @param appid 业务ID
     * @param uid 用户uid
     * @param propId 礼物id，玉石20209
     * @param propAdd 流水类型：1增；0减；-1所有
     * @param startTime 开始日期（包含，单位毫秒）
     * @param endTime 结束日期（不包含，单位毫秒）
     * @param page 页号(1, 2, 3...)
     * @param pagesize 每页大小，最大2000
     * @param srcTypes 14标识用礼物消费
     * @return UserPropsPeriodHistoryPageResult
     * @throws ServiceException
     */
    TUserPropsPeriodHistoryPageResult queryUserPropsPeriodHistoryV2(1: i32 appid, 2: i64 uid, 3: i64 propId, 4: i32 propAdd, 5: i64 startTime, 6: i64 endTime, 7: i32 page, 8: i32 pagesize, 9: list<i32> srcTypes) throws (1: TServiceException ex1);

    /**
     * 查询交友房管厅收礼人流水
     * @param startDate YYYY-MM-dd 毫秒,送礼时间 >= startDate,必填
     * @param endDate YYYY-MM-dd 毫秒,送礼时间 < endDate,必填
     * @param ssids,必填
     * @param recvUids 可选
     * @param recvUserType 1：主持，2：嘉宾
     * @return list
     */
    list<TDatingRoomRecvIncomeStat> queryRoomRecvUserIncome(1: i64 startDate, 2: i64 endDate, 3: list<i64> ssids, 4: list<i64> recvUids, 5: i32 recvUserType) throws (1: TServiceException ex1);

    /**
    * 新榜单(day,week,month;top10)
    * 重构：/v4/datingRoomMgr/getNewRank
    * @param roomMgrUid 房管uid
    * @param type 榜单类型：1-byDate, 2-byWeek, 3-byMonth, 4-latestNDays
    * @return 同/v4/datingRoomMgr/getNewRank -> data
    * ServiceException -401:未签约房管,  -500:服务器异常
    */
    string getNewRank(1: i64 roomMgrUid, 2: i32 type) throws (1: TServiceException ex1);

    /**
    * 房管佣金收支（全部）
    * 重构：/v4/datingRoomMgr/personCommission
    * @param roomMgrUid 房管uid
    * @param justRJ 是否仅返回日结数据，默认全部返回
    * @return 同/v4/datingRoomMgr/personCommission -> data
    * ServiceException -401:未签约房管,  -500:服务器异常
    */
    string personCommission(1: i64 roomMgrUid, 2: bool justRJ) throws (1: TServiceException ex1);

    /**
    * 房管礼物盖章流水核心数据
    * 重构：/v5/datingRoomMgr/roomIncomeSummary
    * @param roomMgrUid 房管uid
    * @return 同/v5/datingRoomMgr/roomIncomeSummary -> data
    * ServiceException -401:未签约房管,  -500:服务器异常
    */
    string roomIncomeSummary(1: i64 roomMgrUid) throws (1: TServiceException ex1);

    /**
    * 房管本月普通主持礼物流水总和
    * 重构：/v5/datingRoomMgr/roomNormalPropsIncome
    * @param roomMgrUid 房管uid
    * @param noBullet 是否排除弹幕礼物
    * @return 同/v5/datingRoomMgr/roomNormalPropsIncome -> data
    * ServiceException -401:未签约房管,  -500:服务器异常
    */
    string roomNormalPropsIncome(1: i64 roomMgrUid, 2: bool noBullet) throws (1: TServiceException ex1);
}

service TFunctionUserWhiteService{
  //查询白名单
  //functionName 发送白名单取值: "dating_send_real_white_uid", 接收白名单取值: "dating_recv_real_white_uid"
  //appid = 2
  //uidList 查询uid列表
  //yyList  查询yy号列表
  //addTimeFrom  查询加入日期开始,格式yyyymmdd(包含这天)
  //addTimeTo	 查询加入日期结束,格式yyyymmdd(不包含这天)
  list<TFunctionUserWhiteList> getFunctionUserWhiteList(1: string functionName, 2: i32 appid, 3: list<i64> uidList, 4: list<i64> yyList, 5: string addTimeFrom, 6: string addTimeTo);

  //添加白名单
  //targetId 主持uid
  //return 1.成功 其他.失败
  i32 addFunctionUserWhiteList(1: string functionName, 2: i32 appid, 3: i64 targetId, 4: string expand, 5:i64 operatorUid);

  //修改白名单
  //return 1.成功 其他.失败
  i32 updateFunctionUserWhiteList(1: string functionName, 2: i32 appid, 3: i64 targetId, 4: string expand, 5:i64 operatorUid);

  //删除白名单
  //return 1.成功 其他.失败
  i32 deleteFunctionUserWhiteList(1: string functionName, 2: i32 appid, 3: i64 targetId, 4:i64 operatorUid);

  //用户是否在白名单内
  //return map<用户uid, 在否>
  map<i64, bool> isUidInWhiteList(1: string functionName, 2: i32 appid, 3: list<i64> uidList);
}

service TCurrencyRealSupportService{

  list<TRealAmountInfo> getRealAmountInfo(1: i32 appid, 2: list<i64> uidList) throws (1: TServiceException ex1);

  // 黄水晶帮扶
  i32 supportReal(1: i32 appid, 2: i64 fromUid, 3: list<i64> toUidList, 4: i64 amount, 5: string seq) throws (1: TServiceException ex1);

  //1.成功,  -505.处理中,  其他.失败
  i32 getSupportRealStatus(1: i32 appid, 2: string seq);
}

service TDataCenterStatisticsService {
	/**
	 * 查询子厅有价值礼物流水
	 * @param appid=2 交友
	 * @param startDate 开始时间(包括这天),格式yyyymmdd
	 * @param endDate 结束时间(包括这天),格式yyyymmdd
	 * return<ssid, <yyyymmdd, 紫水晶>>>
	 */
  map<i64, map<string, i64>> querySsidIncome(1: i32 appid, 2: i64 sid, 3: list<i64> ssidList, 4: string startDate, 5: string endDate) throws (1: TServiceException ex1);

    /**
    * 查询子厅有价值礼物流水
    * @param appid=2 交友
    * @param startDate 开始时间(包括这天),格式yyyymmdd
    * @param endDate 结束时间(包括这天),格式yyyymmdd
    * return<ssid, <yyyymmdd, 紫水晶>>>
    */
    map<i64, map<string, i64>> querySsidIncomeNoBullet(1: i32 appid, 2: i64 sid, 3: list<i64> ssidList, 4: string startDate, 5: string endDate) throws (1: TServiceException ex1);

    /**
     * querySsidIncome第二版,返回礼物和盖章数据
     * 入参同querySsidIncome
     * 返回<ssid, 数据>
     */
  map<i64, list<TSsidConsumeData>> querySsidIncomeV2(1:i32 appid, 2:i64 sid, 3:list<i64> ssidList, 4:string startDate, 5:string endDate);

    /**
  	 * 交友appid = 2
  	 * startDate,endDate 格式:yyyymmdd
  	 */
  list<TPersonalRoomIncomeData> queryPersonalRoomIncome(1: i32 appid, 2: i64 sid, 3: list<i64> ssidList, 4: string startDate, 5: string endDate) throws (1: TServiceException ex1);

    /**
	* sid			0.返回全部
	* appid			2.返回交友
	* timeGreaterThan 	格式:yyyyMMddHHmmss, 包括这个时间
	* timeLessThan    	格式:yyyyMMddHHmmss, 包括这个时间
    */
  list<TDatingChannelIncomeStat> queryChannelIncomeStat(1: i64 sid, 2: i32 appid, 3: string timeGreaterThan, 4: string timeLessThan);
  
  /**
   * 查询新用户日消费金额
   * @param page 1为第一页，默认1
   * @param pagesize 每页数量，默认50
   * @param startDay 开始日期
   * @param endDay 结束日期
   * @param appid 2：交友，36：宝贝，34：语音房
   * @param types 1 普通礼物流水，2 互动礼物流水，3 盖章
   * @param uids 用户id
   */
  TNewUserDailyConsumeAmountPageResult queryNewUserDailyConsumeAmount(1: i32 page, 2: i32 pagesize, 3: i64 startDay, 4: i64 endDay, 5: TAppId appid, 6: list<i32> types, 7: list<i64> uids) throws (1: TServiceException ex1);

	/**
	* anchorUid		0.返回全部
	* timeGreaterThan 	格式:yyyyMMddHHmmss, 包括这个时间
	* timeLessThan    	格式:yyyyMMddHHmmss, 包括这个时间
	*/
  list<TDatingDailyAnchorIncomeStat> queryDatingDailyAnchorIncomeStatByAnchor(1: i64 anchorUid, 2: string timeGreaterThan, 3: string timeLessThan);

	/**
	* appid 	36.返回宝贝
	* uid 		0.返回全部
	* sid 		0.返回全部
	* startTime 	毫秒时间戳, 包括这个时间
	* endTime   	毫秒时间戳, 包括这个时间
	* srcTypes  	传空数组即可
	*/
  list<TRevenueSummaryRecord> queryRevenueSummaryRecord(1: i32 appid, 2: i64 uid, 3: i64 sid, 4: i64 startTime, 5: i64 endTime, 6: list<i32> srcTypes);

   /**
    * 查询家族流水明细（天）
    * appid 	业务ID  34
    * familyId 	家族id  0.返回全部
    * startTime 	开始日期 毫秒时间戳（毫秒）
    * endTime 	结束日期 毫秒时间戳（毫秒）
    */
  list<TFamilyDailyIncome> queryFamilyDailyIncome(1: TAppId appid, 2: i64 familyId, 3: i64 startTime, 4: i64 endTime) throws (1: TServiceException ex1);

   /**
   * 查询家族房间流水明细（天）
   * appid 业务ID	34
   * familyId 家族id	0.返回全部
   * liveSid 房间sid	0.返回全部
   * liveSsid 房间ssid	0.返回全部
   * startTime 开始日期 毫秒时间戳（毫秒）
   * endTime 结束日期 毫秒时间戳（毫秒）
   * page 页号(1, 2, 3...)
   * pagesize 每页大小
   * total 参数total >0 的时候 参数total=返回total，不再查询
   */
  TFamilyRoomDailyIncomePageResult queryFamilyDailyRoomIncome(1: TAppId appid, 2: i64 familyId, 3: i64 liveSid, 4: i64 liveSsid, 5: i64 startTime, 6: i64 endTime, 7: i32 page, 8: i32 pagesize, 9: i64 total) throws (1: TServiceException ex1);

  /**
   * 查询家族房间号流水明细（天）
   * @param appid 业务ID
   * @param familyId 家族id
   * @param roomId 房间号
   * @param startTime 开始日期 如 2021-12-30 00:00:00 的时间戳（毫秒）
   * @param endTime 结束日期 如 2021-12-30 00:00:00 的时间戳（毫秒）
   * @param page 页号(1, 2, 3...)
   * @param pagesize 每页大小
   * @param total >0则后台不进行count，并直接使用该值作为count
   * @param skipContent 0.返回contents明细, 1.不返回contents明细
   * @param skipSum =1则不汇总流水，返回totalIncome=0, totalTurnableIncome=0
   */
  TFamilyRoomIdDailyIncomePageResult queryFamilyDailyRoomIdIncome(1: TAppId appid, 2: i64 familyId, 3: i64 roomId, 4: i64 startTime, 5: i64 endTime, 6: i32 page, 7: i32 pagesize, 8: i64 total, 9: i32 skipContent, 10: i32 skipSum) throws (1: TServiceException ex1);


     /**
       * 公会礼物流水的前N名(魔法棒/水晶球不统计)
  	   * appid	2.交友
       * statDate  yyyyMMdd
       * size 最大返回数量
       */
  	list<TDatingChannelIncomeStat> getIncomeWithoutMagicGroupBySidTopN(1: i32 appid, 2: string statDate, 3: i32 size);

  	/**
       * 主持礼物流水的前N名(魔法棒/水晶球不统计)
       * appid	2.交友
  	   * statDate  yyyyMMdd
       * size 最大返回数量
       */
  	list<TDatingDailyAnchorIncomeStatNotRoomMgr> getIncomeWithoutMagicGroupByAnchorTopN(1: i32 appid, 2: string statDate, 3: i32 size);

  	/**
       * 厅ssid礼物流水总和的前10名(魔法棒/水晶球不统计)
       * appid	2.交友
  	   * statDate  yyyyMMdd
       * size 最大返回数量
       */
  	list<TDatingDailyRoomMgrIncomeStat> getIncomeWithoutMagicGroupBySsidTopN(1: i32 appid, 2: string statDate, 3: i32 size);

  	/**
     * 查询主持流水前x名
     * @param appid 2：交友
     * @param startDate 开始日期 yyyy-MM-dd
     * @param endDate 结束日期 yyyy-MM-dd
     * @param type 1 盖章 暂时只支持盖章
     * @param top 如：20，返回前20名
     */
    list<i64> queryTopAnchorIncome(1: TAppId appid, 2: i64 startDate, 3: i64 endDate, 4: i32 type, 5: i32 top) throws (1: TServiceException ex1);

   /**
    * 查询频道流水前x名
    * @param appid 2：交友
    * @param startDate 开始日期
    * @param endDate 结束日期
    * @param type 1 盖章 暂时只支持盖章
    * @param top 如：20，返回前20名
    */
   list<i64> queryTopSidIncome(1: TAppId appid, 2: i64 startDate, 3: i64 endDate, 4: i32 type, 5: i32 top) throws (1: TServiceException ex1);

    /**
    * 查询交友公会房管厅多人厅签约主持流水统计
    * @param startDate 开始日期 必填 毫秒闭区间
    * @param endDate 结束日期 必填 毫秒闭区间
    * @param sid 必填
    * @param ssid 可选
    * @param roomType 1：房管厅，2：多人厅
    */
    list<TDatingDailySidRoomTypeIncomeStat> queryDatingSidRoomTypeIncomeStat(1: i64 startDate, 2: i64 endDate, 3: i64 sid, 4: i64 ssid, 5: i32 roomType) throws (1: TServiceException ex1);
}

service TPropsServiceV2 {

  /**
   * 查看旗下签约主播每条收到礼物明细记录
   * @param appid 业务ID
   * @param sid 工会sid。若imid>=0,则按imid查，不按sid查。
   * @param imid 主持YY号。若imid>=0,则按imid查，不按sid查。
   * @param startTime 开始日期 yyyy-MM-dd
   * @param endTime 结束日期 yyyy-MM-dd
   * @param page 页号(1, 2, 3...)
   * @param pagesize 每页大小
   * @param familyId 家族id
   * @param total 如果total大于0，直接返回total=参数total，否则执行查询
   * @param senderImid 送礼YY号
   * @param ssid 房间ssid
   * @return SignedAnchorPropsUsedRecordPageResult
   * @throws ServiceException
   */
  TSignedAnchorPropsUsedRecordPageResult querySignedAnchorPropsUsedRecords(1: TAppId appid, 2: i64 sid, 3: i64 imid, 4: i64 startTime, 5: i64 endTime, 6: i32 page, 7: i32 pagesize, 8: i64 familyId, 9: i64 total, 10: i64 senderImid, 11: i64 ssid) throws (1: TServiceException ex1);

}

// =================== 语音房联运礼包活动需求-活动配置查看和更新接口 =======================

struct TPeiwanChargeGiftbagActivityInfo {
  1: i64 activityId;    // 活动id，0则表示新增，非0表示修改
  2: i64 startTime;     // 开始时间戳，单位ms
  3: i64 endTime;       // 结束时间戳，单位ms
  4: i64 dayLimitAmount;  // 每日限量，单位元，-1表示为限制
  5: double amount;     // 累计充值金额，单位元
}

struct TResult {
  1: i32 code;          // 1则成功
  2: string message;
  3: string expand;
}

service TActivityConfigService {

/**
 * 查询活动配置信息接口
 *
 * @param activityId 为0，则查全部
 * @return
 *
 */
list<TPeiwanChargeGiftbagActivityInfo> queryPeiwanChargeGiftbagActivityInfo(1: i64 activityId);

/**
 * 修改活动配置信息接口
 *
 * @param
 * @return
 *
 */
TResult addOrUpdatePeiwanChargeGiftbagActivityConfig(1: TPeiwanChargeGiftbagActivityInfo info);

}

// **************************************************** //
struct TPropInfo{
  1: i64 propId;
  2: string propName;
  3: string propIcon;
  4: i64 propPrice;
}

enum TConsumeProductType {
  Skin=1, Seal=2, BlackShell=3, VirtLottery=4, SpoofHanging=5, LuckyTreasures=6, XhHalloweenUpgrade=7, HatKing=10, LoveLetter=11, ZhouNianDaZhuanPan=12, YYLiveRedDiamondLottery=16, YYLiveRedPacket=23, DatingNobleCardConsume=29, DatingSeptemberActivity=30, DatingAuctionAward=31, DatingKainianHongbao=46, DatingTaoHuaQian=60, VippkTaoHuaQian=63, DatingChickenCard=68, ChallengeTicket=71, PartySeal=77, PrizeRedWineSeal=90, VippkChickenCard=102, BabyChickenCard=103, GoldenShellTicket=105, StarCruise=110, ZhuiwanStarCruise=112, DatingPopularityCard=119, BabyTaoHuaQian=120, BabyTaoHuaQian2=121, AppearanceSeal=123
}

struct TWeekPropsRecvInfo {
  1: i64 uid;//送礼人uid
  2: i32 propId;//盖章id
  3: string propName;//没有
  4: i32 pricingId;
  5: double amount;//结算收入
  6: i64 usedTime;//时间
  7: i64 sid;
  8: i32 propCnt;//送礼数量
  9: string guestUid;//收礼人uid
  10: i64 anchorUid;//主持uid
  11: double sumAmount;//总价
  12: i64 id;
  13: TCurrencyType currencyType;
  14: i32 appid;
  15: i32 playType;
  16: string expand;
  17: i64 ssid;
  18: i32 extendUidType;
  19: string guestNick;
  20: string recvNick;
  21: string currencyName;
  22: string propsSourceName;//结算类型
  23: string propTypeName;
  24: string identity;
}

struct TWeekPropsRecvInfoQueryPage {
  1: list<TWeekPropsRecvInfo> content;
  2: i32 page;
  3: i32 pagesize;
  4: i32 totalElement;
  5: i32 totalPage;
  6: map<string, string> extend;
}

struct TDatingRoomIncomeRequest {
  1: i64 sid;
  2: i64 ssid;
}

struct TDatingRoomIncome {
  1: i64 sid;
  2: i64 ssid;
  3: i64 amount;
}

struct TDatingGuestPropsRecvStatOfPlayType {
  1: i64 dt;
  2: i64 sid;
  3: i64 ssid;
  4: i64 guestUid;
  5: i64 amount;
  6: i32 type;
}

/**
  * appid=2, size:最多返回数量, channelType=0
*/
service TProductServiceV2 {
    // 获取PC礼物列表的礼物
    list<TPropInfo> getVisiblePropsByAppId(1: i32 appid, 2: i32 size, 3: i32 channelType);

    /**
    * 查询盖章明细，7天内
    * @param consumeProductType 盖章传2：Seal
    * @param startTime 毫秒，闭区间
    * @param endTime 毫秒，开区间
    * @param page 页码
    * @param pagesize 每页数量
    * @param role 房管厅下的消费记录：4
    * @param usedUid 送礼人uid
    * @param targetUid 收礼人uid
    * @param ssid ssid
    * @param orderAsc true：时间升序，false：时间倒序
    * @param expand 扩展json
    */
    TWeekPropsRecvInfoQueryPage queryWeekProductRecieveExternal(1: TAppId appid, 2: TConsumeProductType consumeProductType, 3: i64 startTime, 4: i64 endTime, 5: i32 page, 6: i32 pagesize, 7: i32 role, 8: i64 ssid, 9: i64 usedUid, 10: i64 targetUid, 11: bool orderAsc, 12: string expand) throws (1: TServiceException ex1);
}

// 盖章流水
service TDatingSealStatService {
  /**
   * 查询交友月度盖章流水统计（统计维度：yyyyMM + SID）
   * @param month 月份yyyyMM
   * @param sid 公会SID
   * @return 月度盖章流水单位厘
   * @throws ServiceException
   */
  i64 queryDatingMonthlySealIncomeStatBySid(1: string month, 2: i64 sid) throws (1: TServiceException ex1);

  /**
   * 查询交友月度盖章流水统计（统计维度：yyyyMM + SID + SSID + anchorUid）
   * 需要排除房管天团厅
   * @param month 月份yyyyMM
   * @param sid 公会SID
   * @param anchorUid 主持UID
   * @return Map<子厅SSID, 月度盖章流水单位厘>
   * @throws ServiceException
   */
  map<i64, i64> queryDatingMonthlySealIncomeStatBySidAnchorUid(1: string month, 2: i64 sid, 3: i64 anchorUid) throws (1: TServiceException ex1);

  /**
   * 查询交友月度盖章流水统计（统计维度：yyyyMM + SID + SSID）
   * 不区分是否房管天团厅
   * @param month 月份yyyyMM
   * @param sid 公会SID
   * @return Map<子厅SSID, 月度盖章流水单位厘>
   * @throws ServiceException
   */
  map<i64, i64> queryDatingMonthlySealIncomeStatBySidSsid(1: string month, 2: i64 sid) throws (1: TServiceException ex1);

    /**
    * 查询交友月度盖章流水统计（统计维度：yyyyMM + 经营SID + 经营SSID）
    * @param month 月份yyyyMM
    * @param req
    * @throws ServiceException
    */
    list<TDatingRoomIncome> queryDatingRoomMonthlySealIncomeBySidSsid(1: string month, 2: list<TDatingRoomIncomeRequest> reqs) throws (1: TServiceException ex1);

}

// --- 假日庄园相关统计接口

struct TDatingGardenDailyStat {
  1: i64 id;
  2: i64 dt; // 统计日期，如：1695571200000表示2023-09-25
  3: i32 appid;
  4: i64 uid; // 庄主UID
  5: i32 weixinTotal; // 包裹礼包微信支付购买数
  6: i32 alipayTotal; // 包裹礼包支付宝购买数
  7: i32 total; // 礼包购买总数（weixinTotal + alipayTotal + weixinTotalVirt + alipayTotalVirt + weixinTotalYb + alipayTotalYb）
  8: i64 createTime;
  9: i32 weixinTotalVirt; //紫水晶券礼包微信支付购买数
  10: i32 alipayTotalVirt; //紫水晶券礼包支付宝购买数
  11: i32 weixinTotalYb; //YB礼包微信支付购买数
  12: i32 alipayTotalYb;//YB礼包支付宝购买数
  13: i32 gardenType;
  14: i32 weixinQrcodeTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  15: i32 weixinWapTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  16: i32 alipayQrcodeTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  17: i32 alipayWapTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  18: i32 alipayH5miniTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  19: i32 weixinQrcodeTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  20: i32 weixinWapTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  21: i32 alipayQrcodeTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  22: i32 alipayWapTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  23: i32 alipayH5miniTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  24: i32 weixinQrcodeTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  25: i32 weixinWapTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  26: i32 alipayQrcodeTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  27: i32 alipayWapTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  28: i32 alipayH5miniTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
}

struct TDatingGardenDailyStatPageResult {
  1: i32 page; // 当前页号
  2: i32 pagesize; // 每页大小
  3: i32 total; // 总条数
  4: i32 totalPage; // 总页数
  5: list<TDatingGardenDailyStat> contents;
}

/**
* dt:日期毫秒数
* propsRealAmount: 待日结黄水晶数量
*/
struct TDatingAnchorDailyRealPreRevenueStat{
  1: i64 id;
  2: i64 dt;
  3: i64 uid;
  4: i64 propsRealAmount; // 礼物待日结黄水晶
  5: i64 propsRealAmountBullet;     // 礼物待日结黄水晶 只有弹幕礼物
  6: i64 propsRealAmountNoBullet;   // 礼物待日结黄水晶 所有礼物-弹幕礼物
}

service TDatingGardenStatService {
  /**
   * 查询假日庄园流水统计
   * @param appId 2：交友
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @param uid 庄主uid（可选传0）
   * @param page 页号，从1开始
   * @param pagesize 每页大小
   */
  TDatingGardenDailyStatPageResult queryDatingGardenDailyStats(1: TAppId appid, 2: i64 startDate, 3: i64 endDate, 4: i64 uid, 5: i32 page, 6: i32 pagesize, 7: i32 gardenType) throws (1: TServiceException ex1);
}


struct TRoomIncomeStat {
  1: string statPeriod;//2023-11-07
  2: double propsTotalIncome;//总礼物流水
  3: double sealTotalIncome;//总盖章流水
  4: double propsSuperIncome;//超级主持礼物流水
  5: double propsNormalIncome;//普通主礼物流水
  6: double propsStarIncome;//星光主持礼物流水
  7: double sealSuperIncome;//超级主持盖章流水
  8: double sealNormalIncome;//普通主持盖章流水
  9: double sealStarIncome;//星光主持盖章流水
  10: list<TDatingDailyRoomStat> details;//主持流水明细
}

service TDatingStatService {

  /**
  * 查询交友主持每日待结算黄水晶
  * anchorUids: 主持列表
  * startDate: 开始日期格式yyyymmdd
  * endDate: 结束日期格式yyyymmdd
  */
  list<TDatingAnchorDailyRealPreRevenueStat> queryAnchorDailyRealPreRevenue(1: list<i64> anchorUids, 2: string startDate, 3: string endDate) throws (1: TServiceException ex1);

  /**
  * 按照hgame后台口径查询今天的待日结黄水晶 startDate, 格式yyyymmdd, 仅能查当天、昨天(日结前)
  * startDate 查整天的，传 20231025 就查25这一天的，26凌晨日结前查的不包含26号的
  * 返回格式 {"propsRealAmount": 1000, "sealRealAmount": 1000, "propsRealAmountBullet": 1000, "propsRealAmountNoBullet": 1000}
  */
  map<string, i64> queryAnchorTodayRealPreRevenue(1: i64 anchorUid,2: string startDate);

    /**
    * 查询房管厅(天团厅/个播厅)礼物和盖章流水 总和填queryPublicRoom:true queryPersonalRoom:true
    * @param appId 业务ID
    * @param roomMgrUid 房管uid
    * @param queryPublicRoom 是否查询天团厅
    * @param queryPersonalRoom 是否查询个播厅
    * @return Map<时间,RoomIncomeStat>
    */
    map<string, TRoomIncomeStat> queryRoomIncomeByDate(1: TAppId appId, 2: i64 roomMgrUid, 3: bool queryPublicRoom, 4: bool queryPersonalRoom, 5: i64 startDate, 6: i64 endDate) throws (1: TServiceException ex1);

    /**
    * 查询礼物明细，单次查询时间范围最多一个自然月内，总时间范围两个自然月内
    * @param startTime 毫秒，闭区间
    * @param endTime 毫秒，开区间
    * @param page 页码
    * @param pagesize 每页数量
    * @param role 房管厅下的送礼记录：4
    * @param usedUid 送礼人uid
    * @param targetUid 收礼人uid
    * @param ssid ssid
    * @param orderAsc true：时间升序，false：时间倒序
    * @param expand 扩展json
    * @param totalParam totalParam > 0时，直接填到total不执行查询，totalParam==0时，查询total
    */
    TWeekPropsRecvInfoQueryPage queryDatingPropsUsedRecordStat(1: TAppId appid, 2: i64 startTime, 3: i64 endTime, 4: i32 page, 5: i32 pagesize, 6: i32 role, 7: i64 usedUid, 8: i64 targetUid, 9: i64 sid, 10: i64 ssid, 11: bool orderAsc, 12: string expand, 13: i64 totalParam) throws (1: TServiceException ex1);

    /**
     * 特定玩法下嘉宾收礼流水
     * @param startTime 毫秒，闭区间
     * @param endTime 毫秒，开区间
     */
    list<TDatingGuestPropsRecvStatOfPlayType> queryDatingGuestPropsRecvStatOfPlayType(1: i64 startTime, 2: i64 endTime, 3: list<i64> sids, 4: list<i64> ssids) throws (1: TServiceException ex1);
}

struct TCheckIdentityResult {
  /**
   * 受益人uids
   */
  1: list<i64> settlementUids;

  /**
   * 受益人当中身份和送礼人相同的uids
   */
  2: list<i64> sameIdentityUids;

  /**
   * true：通过，false：不通过
   */
  3: bool check;
  4: string expand;
}


service TUdbRiskGiftSendService {
  void ping2()
  list<i64> getSameIdentityUids(1: i64 fromUid, 2: list<i64> toUids);
 /**
    * 检查送礼合法性
    * @param appId 交友：2，聊天室：34
    * @param sid
    * @param ssid
    * @param uid 送礼人uid
    * @param anchorUid 主持位uid
    * @param recverUids 收礼人uid
    * @param grabLoveType 玩法参数
    * @param expand
    * @throws ServiceException
    */
   TCheckIdentityResult checkUsePropsIdentity(1: TAppId appId, 2: i64 sid, 3: i64 ssid, 4: i64 uid, 5: i64 anchorUid, 6: list<i64> recverUids, 7: i32 grabLoveType, 8: string expand) throws (1: TServiceException ex1);

   /**
    * 检查消费合法性
    * @param appId 交友：2，聊天室：34
    * @param sid
    * @param ssid
    * @param productId
    * @param productType
    * @param uid 送礼人uid
    * @param anchorUid 主持位uid
    * @param targetUid 目标人uid
    * @param grabLoveType 玩法参数
    * @param expand
    * @throws ServiceException
    */
   TCheckIdentityResult checkConsumeProductIdentity(1: TAppId appId, 2: i64 sid, 3: i64 ssid, 4: i32 productId, 5: i32 productType, 6: i64 uid, 7: i64 anchorUid, 8: i64 targetUid, 9: i32 grabLoveType, 10: string expand) throws (1: TServiceException ex1);
}

service TRevenueServiceDarenV2 {
    void ping2()
    /** 查询达人等级
    * appid 业务id
    * uid
    * @return TProductConsumeLevel
    */
    TProductConsumeLevel getUserProductConsumeLevel(1: i32 appid, 2: i64 uid);

    /** 批量查询达人等级
    * appid 业务id
    * uidList
    * @return TProductConsumeLevel
    */
    list<TProductConsumeLevel> getUsersProductConsumeLevel(1: i32 appid, 2:list<i64> uidList)
    /** 批量查询达人等级
    * appid 业务id
    * decrAmount 扣减经验值
    * uid
    * @i32 1 成功 其他失败
    */
    i32 decrUserProductConsumeLevel(1: i32 appid, 2: i64 uid, 3: i64 decrAmount, 4: string seqId) throws (1: TServiceException ex1);
}



