namespace java com.yy.hd.api.thrift.turnover.finance.revenue.daren

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TProductConsumeLevel TProductConsumeLevel

service TRevenueServiceDarenV2 {
    void ping2()
    /** 查询达人等级
    * appid 业务id
    * uid
    * @return TProductConsumeLevel
    */
    TProductConsumeLevel getUserProductConsumeLevel(1: i32 appid, 2: i64 uid);

    /** 批量查询达人等级
    * appid 业务id
    * uidList
    * @return TProductConsumeLevel
    */
    list<TProductConsumeLevel> getUsersProductConsumeLevel(1: i32 appid, 2:list<i64> uidList)
}
