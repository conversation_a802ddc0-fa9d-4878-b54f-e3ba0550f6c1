namespace java com.yy.hd.api.thrift.turnover.finance.revenue.daren

include "turnover_finance_common.thrift"

// TRevenueServiceDarenV2 专用结构体
struct TProductConsumeLevel{
  1: i32 appid;
  2: i64 uid;
  3: i32 level; //加油派对等级
  4: i64 allAmount; //当前经验
  5: i64 todayAmount; //当日累计经验
  6: i64 nextAmount; //下一级经验
}

service TRevenueServiceDarenV2 {
    void ping2()
    /** 查询达人等级
    * appid 业务id
    * uid
    * @return TProductConsumeLevel
    */
    TProductConsumeLevel getUserProductConsumeLevel(1: i32 appid, 2: i64 uid);

    /** 批量查询达人等级
    * appid 业务id
    * uidList
    * @return TProductConsumeLevel
    */
    list<TProductConsumeLevel> getUsersProductConsumeLevel(1: i32 appid, 2:list<i64> uidList)
}
