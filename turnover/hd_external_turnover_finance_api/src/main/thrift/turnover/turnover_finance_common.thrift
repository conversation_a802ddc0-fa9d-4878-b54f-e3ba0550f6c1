namespace java com.yy.hd.api.thrift.turnover.finance.common

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"

// 基础类型定义 - 被多个服务使用
typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TCurrencyType TCurrencyType
typedef turnover_common.TAppId TAppId
typedef turnover_common.TPropsType TPropsType

// 基础信息结构 - 被多个服务使用
struct TSidInfo {
    1: i64 sid;
    2: i64 ssid;
}

// 结算来源类型 - 被多个服务使用
enum TRevenueSrcType {
  Props=1, Tutor=2, ExternalCharge=3, PropsWithoutDaySettleLevel=4, VipPkRevenue=5, FinanceStrategy=6, ForceRelieveContract=7, YoMallSalaryRevenue=8, XunhuanRoomRevenue=9, XunhuanPropsAndRoomRevenue=10, NiuWan=11, Ask=12, DATING_HATKING=13, IssueBonus=14, DATING_6P=15, DATING_PC6P=16, DATING_PC6P_VIRT=17, MGVRandom=18, DATING_6PFACE=19, XunhuanActSubsidy=20, DatingSeal=21, DatingSoccer=22, DatingYYLiveRedPacket=23, Award=24, QuasiSuper=25, DATING_AUCTION_AWARD=26, DATING_AUCTION_PROPS=27, XUNHUAN_SPECIAL_PROPS_REVENUE=28, VIPPK_PC6P=29, VIPPK_PC6P_EXTRA=30, DATING_PC_YYF=31, DATING_PC_YYFHD=32, VIPPK_SUPER_ANCHOR_ADDITION=33
}

// 消费产品类型 - 被多个服务使用
enum TConsumeProductType {
  Skin=1, Seal=2, BlackShell=3, VirtLottery=4, SpoofHanging=5, LuckyTreasures=6, XhHalloweenUpgrade=7, HatKing=10, LoveLetter=11, ZhouNianDaZhuanPan=12, YYLiveRedDiamondLottery=16, YYLiveRedPacket=23, DatingNobleCardConsume=29, DatingSeptemberActivity=30, DatingAuctionAward=31, DatingKainianHongbao=46, DatingTaoHuaQian=60, VippkTaoHuaQian=63, DatingChickenCard=68, ChallengeTicket=71, PartySeal=77, PrizeRedWineSeal=90, VippkChickenCard=102, BabyChickenCard=103, GoldenShellTicket=105, StarCruise=110, ZhuiwanStarCruise=112, DatingPopularityCard=119, BabyTaoHuaQian=120, BabyTaoHuaQian2=121, AppearanceSeal=123
}

// 礼物信息 - 被多个服务使用
struct TPropInfo{
  1: i64 propId;
  2: string propName;
  3: string propIcon;
  4: i64 propPrice;
}

// 房间收入请求 - 被多个服务使用
struct TDatingRoomIncomeRequest {
  1: i64 sid;
  2: i64 ssid;
}

// 房间收入结果 - 被多个服务使用
struct TDatingRoomIncome {
  1: i64 sid;
  2: i64 ssid;
  3: i64 amount;
}

// 身份检查结果 - 被多个服务使用
struct TCheckIdentityResult {
  1: i32 code;
  2: string message;
  3: bool check;
  4: string expand;
}
