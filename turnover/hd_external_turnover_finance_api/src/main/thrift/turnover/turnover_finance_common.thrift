namespace java com.yy.hd.api.thrift.turnover.finance.common

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"

typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TCurrencyType TCurrencyType
typedef turnover_common.TAppId TAppId
typedef turnover_common.TPropsType TPropsType

struct TSidInfo {
    1: i64 sid;
    2: i64 ssid;
}

struct TDatingDailyIncomeData{
  1: i64 dt;
  2: i64 uid;
  3: double ybPayAmount;
  4: double virtChargeAmount;
  5: double reddiamondConsumeAmount;
}

// 注意amount的单位都是厘
struct TDatingBigCustomerDailyStatistic {
  1: i64 id;
  2: i64 dt;
  3: i64 uid;
  4: i64 propAmount;    // 礼物
  5: i64 doubiAmount;   // 逗比章
  6: i64 nobleAmount;   // 贵族
  7: i64 guardAmount;   // 守护
  8: i64 pinkAmount;    // 粉钻
}

struct THatSidIncomeStat{
  1: i64 sid;
  2: i64 amount;
  3: i32 quota;
}

struct TAnchorIncomeStat{
  1: i64 anchorUid;
  2: i64 amount;
}

struct TAnchorMissionPrizeMonthDetail{
  1: i64 uid;
  2: i64 yyno;
  3: string nickName;
  4: i64 sid;
  5: i64 prizeYuan;
  6: string anchorType;
  7: string prizeType;
  8: double incomeYuan;
  9: string level;
}

struct TAnchorMissionPrizeMonthDetailList{
  1: list<TAnchorMissionPrizeMonthDetail> items;
}

// 结算来源类型
enum TRevenueSrcType {
  Props=1, Tutor=2, ExternalCharge=3, PropsWithoutDaySettleLevel=4, VipPkRevenue=5, FinanceStrategy=6, ForceRelieveContract=7, YoMallSalaryRevenue=8, XunhuanRoomRevenue=9, XunhuanPropsAndRoomRevenue=10, NiuWan=11, Ask=12, DATING_HATKING=13, IssueBonus=14, DATING_6P=15, DATING_PC6P=16, DATING_PC6P_VIRT=17, MGVRandom=18, DATING_6PFACE=19, XunhuanActSubsidy=20, DatingSeal=21, DatingSoccer=22, DatingYYLiveRedPacket=23, Award=24, QuasiSuper=25, DATING_AUCTION_AWARD=26, DATING_AUCTION_PROPS=27, XUNHUAN_SPECIAL_PROPS_REVENUE=28, VIPPK_PC6P=29, VIPPK_PC6P_EXTRA=30, DATING_PC_YYF=31, DATING_PC_YYFHD=32, VIPPK_SUPER_ANCHOR_ADDITION=33
}

struct TAnchorDailyIncome {
  1: i64 revenueDate;           // 日结时间
  2: i64 tid;                   // 厅id
  3: i64 anchorUid;             // 主持uid
  4: i64 anchorImid;            // 主持YY号
  5: string anchorNick;         // 主持昵称
  6: double anchorIncome;       // 待日结黄水晶
  7: TRevenueSrcType srcType;   // 结算来源类型：1：礼物；21：盖章
  8: i32 owWeight;              // 公会分成比例
  9: i32 tingWeight;            // 厅管抽成比例
  10: double tingIncome;        // 厅管总日结黄水晶
}

struct TAnchorDailyIncomePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TAnchorDailyIncome> contents;
}

struct TMonthAnchorGiftIncomeStat {
  1: i64 anchorUid;
  2: i64 amount;
}

struct TZwyyfRoomDailyRevenueStat {
  1: i64 dt;                        // 日结时间
  2: i32 appid;                     // 业务ID
  3: i64 uid;                       // 房主uid
  4: double realIncome;             // 普通礼物黄钻日结
  5: double turntableRealIncome;    // 约会礼物黄钻日结
  6: double otherRealIncome;        // 其他黄钻收入
  7: double totalRealIncome;        // 合计黄钻收入
}

struct TZwyyfAnchorDailyRevenueStat {
  1: i64 dt;                    // 日结时间
  2: i32 appid;                 // 业务ID
  3: i64 uid;                   // 主播uid
  4: i64 sid;                   // 工会sid
  5: double allIncome;          // 普通礼物金钻流水
  6: i32 rate;                  // 普通礼物日结比例
  7: double allIncomeTurntable; // 约会礼物金钻流水
  8: i32 rateTurntable;         // 约会礼物日结比例
  9: double weight;             // 房主抽成比例
  10: double anchorDailyIncome; // 主播日结黄钻
  11: double owDailyIncome;     // 房主日结黄钻
  12: string expand;            // 扩展字段 {"agentUid":"xxx"}
}

struct TDailyRoomRevenuePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfRoomDailyRevenueStat> contents;
}

struct TDailySignedAnchorRevenue {
  1: i64 revenueDate;
  2: i64 imid;
  3: i64 income;
  4: i64 incomeRate;
  5: i64 turntableIncome;
  6: i64 turntableIncomeRate;
  7: i64 weight;
  8: i64 totalIncome;
  9: i64 roomTotalIncome;
}

struct TDailySignedAnchorRevenuePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfAnchorDailyRevenueStat> contents;
}

struct TSignedAnchorPropsUsedRecord {
  1: i64 id;                // 记录ID
  2: i64 usedTime;          // 送礼时间
  3: TPropsType type;       // 礼物类型 2 收费道具, 14 宝箱礼物 17 约会礼物
  4: string name;           // 礼物名称
  5: i32 count;             // 礼物数量
  6: i64 senderImid;        // 送礼YY号
  7: string senderNick;     // 送礼昵称
  8: i64 receiverImid;      // 收礼YY号
  9: string receiverNick;   // 收礼昵称
  10: i64 signedSid;        // 签约房间
  11: i64 sid;              // 开播房间
  12: i32 currencyType;     // 货币类型 金钻-68
  13: double currencyAmount;// 价格
  14: i64 ssid;             // 开播房间ssid
  15: i64 familyId;         // 家族id
}

struct TSignedAnchorPropsUsedRecordPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TSignedAnchorPropsUsedRecord> contents;
}

struct TExchangePropsHistoryResult {
  1: i64 uid; // 用户id
  2: i64 createTime; // 兑换时间
  3: i32 type;
  4: i32 srcPropId; // 消耗道具id
  5: i64 srcPropCnt; // 消耗道具数量
  6: i32 srcPropPricingId;
  7: i32 srcCurrencyType;
  8: i64 srcCurrencyAmount;
  9: i32 destPropId; // 兑换道具id
  10: i64 destPropCnt; // 兑换道具数量
  11: i32 destPropPricingId;
  12: i32 destCurrencyType;
  13: i64 destCurrencyAmount;
  14: i32 appid; // 业务id
  15: i64 exchangeConfigId;
  16: string srcPropName; // 消耗道具名称
  17: string destPropName; // 兑换道具名称
  18: string exchangeConfigName; // 兑换配置名称
  19: i64 sid;
  20: i64 ssid;
  21: i64 csid; // 签约频道id
  22: i64 anchorUid;
}

struct TExchangePropsHistoryPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TExchangePropsHistoryResult> contents;
}

struct TProductConsumeLevel{
  1: i32 appid;
  2: i64 uid;
  3: i32 level; //加油派对等级
  4: i64 allAmount; //当前经验
  5: i64 todayAmount; //当日累计经验
  6: i64 nextAmount; //下一级经验
}

struct TChannelIncomeRank{
  1: i32 rank;
  2: i64 sid;
  3: double income;
}

struct TDatingDailyTingMgrIncomeStat {
  1: i64 id;
  2: i64 dt;
  3: i32 appid;
  4: i64 uid;
  5: double income;
}

struct TFunctionUserWhiteList {
  1: i64 id;
  2: i32 appid;
  3: string funcName;
  4: i64 startTime;
  5: i64 endTime;
  6: i32 status;
  7: string targetId;
  8: i64 addTime;
  9: string expand;     //数据
  10: i64 operatorUid;  //操作人
}

struct TRealAmountInfo{
  1: i64 uid;
  2: i64 queryTime;
  3: i64 realAmount;		//当前黄水晶
  4: i64 maxSendAmount;		//最大发送额度
  5: i64 maxRecvAmount;		//最大接收额度
  6: i64 currSendAmount;	//当前发送额度
  7: i64 currRecvAmount;	//当前接收额度
}

/**
 * statDate格式:yyyymmdd
 */
struct TPersonalRoomIncomeData{
	1: i64 sid;
	2: i64 ssid;
	3: string statDate;
	4: i64 superIncome;
	5: i64 normalIncome;
    6: i64 starlightIncome;
}

struct TSsidConsumeData {
    1: i64 ssid;
    2: string dt;
    3: i64 propsAmount;
    4: i64 sealAmount;
}

struct TDatingDailyPersonalRoomStat {
  1: i32 id;
  2: i64 statDate;
  3: i64 sid;
  4: i64 ssid;
  5: i64 anchorUid;
  6: i32 isSuper;
  7: i64 propsIncome;
  8: i64 sealIncome;
}

struct TDatingDailyAnchorIncomeStatNotRoomMgr {
  1: i32 id;
  2: i64 statDate;
  3: i64 anchorUid;
  4: i64 sid;
  5: double propsIncome;  // 礼物流水
  6: double sealIncome; // 盖章流水（所有）
  7: double sealDragonIncome; // 神龙章流水
  8: double propsIncomeWithoutMagic; // 排除魔法礼物的流水
}

struct TDatingDailyRoomMgrIncomeStat {
  1: i64 id;
  2: i64 statDate;
  3: i64 roomMgrUid;
  4: i64 sid;
  5: double propsIncome;
  6: double sealIncome;
  7: i64 ssid;
  8: double sealDragonIncome;
  9: double propsIncomeWithoutMagic;
}

struct TDatingDailyAnchorIncomeStat {
  1: i32 id;
  2: i64 statDate;
  3: i64 anchorUid;
  4: i64 sid;
  5: double propsIncome; // propsIncome 礼物流水,单位元(要先四舍五入再乘以1000才是紫水晶)
  6: double sealIncome;
  7: double sealDragonIncome;
}

struct TRevenueSummaryRecord {
  1: i64 id;
  2: i64 uid;
  3: i64 sid;
  4: double income;
  5: i64 optTime;
  6: i64 revenueDate; // 统计日期毫秒时间戳
  7: i32 appid;
  8: i32 userType;
  9: i32 srcType;
  10: string expand;
  11: double allIncome; // 礼物流水,单位紫水晶
}

struct TFamilyDailyIncome {
  1: i64 statDate;    // 统计日期毫秒时间戳
  2: i64 totalIncome; // 总礼物流水,单位紫水晶
  3: i64 familyId; // 家族id
}

struct TZwyyfFamilyRoomDailyIncomeStat {
  1: i64 statDate; // 日期
  2: i64 familyUid; // 家族长uid
  3: i64 familyId; // 家族id
  4: i64 liveSid; // 收礼房间sid
  5: i64 liveSsid; // 收礼房间ssid
  6: i64 income; // 普通礼物流水
  7: i64 turnableIncome; // 转盘（约会）礼物流水
  8: i64 totalIncome; // 总流水
  9: i32 appid; // 业务id，34：语音房，58：yaya语音
}

struct TFamilyRoomDailyIncomePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfFamilyRoomDailyIncomeStat> contents; //  读5里面的流水-普通礼物流水

  6: i64 income;          // 总普通礼物流水 数据有点问题的
  7: i64 turnableIncome; // 总转盘礼物流水 数据有点问题的
  8: i64 totalIncome;    // 总流水 数据有点问题的
}

struct TZwyyfFamilyRoomIdDailyIncomeStat {
  1: i64 id; // ID
  2: i64 statDate; // 统计日期
  3: i32 appid; // 业务ID
  4: i64 familyId;// 家族ID
  5: i64 roomId;// 房间号
  6: i64 income;// 普通礼物流水
  7: i64 turnableIncome;// 技能卡礼物流水
  8: i64 updateDt; // 更新日期
}

/**
* 礼物流水从contents里读取
*/
struct TFamilyRoomIdDailyIncomePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfFamilyRoomIdDailyIncomeStat> contents;
  6: i64 totalIncome;
  7: i64 totalTurnableIncome;
}

struct TDatingChannelIncomeStat {
  1: i64 id;
  2: i64 statDate;
  3: i32 sid;
  4: double giftIncome; // 礼物流水,单位元(要先四舍五入再乘以1000才是紫水晶)
  5: double nobleIncome;
  6: double pinkIncome;
  7: double guardIncome;
  8: double sealIncome;
  9: double sealDragonIncome;
  10: double propsIncomeWithoutMagic; // propsIncomeWithoutMagic: 排除魔法礼物的流水
}

struct TAnchorIncomeStatData{
  1: i64 statDate;
  2: i64 anchorUid;
  3: i64 sid;
  4: i64 ssid;
  5: double propsIncome;
  6: double sealIncome;
}

struct TNewUserDailyConsumeAmount {
  1: i64 dt;
  2: i32 appid;
  3: i64 uid;
  4: i32 type;
  5: i64 amount;
}

struct TNewUserDailyConsumeAmountPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TNewUserDailyConsumeAmount> contents;
}

/**
	propsIncome单位:厘
*/
struct TPersonalRoomIncomeRankData {
	1: i64 sid
	2: i64 ssid
	3: i64 roomUid
	4: i64 propsIncome
}

/**
*	流水单位:厘
*/
struct TDatingDailyRoomStat{
	1: i64 sid
	2: i64 ssid
	3: i64 anchorUid
	4: i64 statDate
	5: i64 propsIncome
	6: i64 sealIncome
}

struct TUserPropsPeriodHistory {
  1: i64 id;
  2: i64 periodId;
  3: string seqId;
  4: i64 uid;
  5: i64 propId;
  6: i64 pricingId;
  7: i32 appid;
  8: i64 oriCnt;
  9: i64 changeCnt;
  10: string description;
  11: i32 srcType;
  12: i64 activityId;
  13: i64 createTime; // 操作时间
  14: i64 startTime;
  15: i64 endTime;
  16: string userIp;
  17: string expand;
  18: i32 usedChannel;
}

struct TUserPropsPeriodHistoryPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TUserPropsPeriodHistory> contents;
}

struct TDatingDailySidRoomTypeIncomeStat {
  1: i64 dt;
  2: i64 sid;
  3: i64 ssid;
  4: i32 roomType;
  5: i64 income;
}

struct TDatingRoomRecvIncomeStat {
  /**
   * 日期
   */
  1: i64 dt;

  /**
   * ssid
   */
  2: i64 ssid;

  /**
   * 收礼人uid
   */
  3: i64 recvUid;

  /**
   * 流水金额
   */
  4: i64 propsIncome;

  /**
   * 1：主持，2：嘉宾
   */
  5: i32 recvUserType;
}

// =================== 语音房联运礼包活动需求-活动配置查看和更新接口 =======================

struct TPeiwanChargeGiftbagActivityInfo {
  1: i64 activityId;    // 活动id，0则表示新增，非0表示修改
  2: i64 startTime;     // 开始时间戳，单位ms
  3: i64 endTime;       // 结束时间戳，单位ms
  4: i64 dayLimitAmount;  // 每日限量，单位元，-1表示为限制
  5: double amount;     // 累计充值金额，单位元
}

struct TResult {
  1: i32 code;          // 1则成功
  2: string message;
  3: string expand;
}

// **************************************************** //
struct TPropInfo{
  1: i64 propId;
  2: string propName;
  3: string propIcon;
  4: i64 propPrice;
}

enum TConsumeProductType {
  Skin=1, Seal=2, BlackShell=3, VirtLottery=4, SpoofHanging=5, LuckyTreasures=6, XhHalloweenUpgrade=7, HatKing=10, LoveLetter=11, ZhouNianDaZhuanPan=12, YYLiveRedDiamondLottery=16, YYLiveRedPacket=23, DatingNobleCardConsume=29, DatingSeptemberActivity=30, DatingAuctionAward=31, DatingKainianHongbao=46, DatingTaoHuaQian=60, VippkTaoHuaQian=63, DatingChickenCard=68, ChallengeTicket=71, PartySeal=77, PrizeRedWineSeal=90, VippkChickenCard=102, BabyChickenCard=103, GoldenShellTicket=105, StarCruise=110, ZhuiwanStarCruise=112, DatingPopularityCard=119, BabyTaoHuaQian=120, BabyTaoHuaQian2=121, AppearanceSeal=123
}

struct TWeekPropsRecvInfo {
  1: i64 uid;//送礼人uid
  2: i32 propId;//盖章id
  3: string propName;//没有
  4: i32 pricingId;
  5: double amount;//结算收入
  6: i64 usedTime;//时间
  7: i64 sid;
  8: i32 propCnt;//送礼数量
  9: string guestUid;//收礼人uid
  10: i64 anchorUid;//主持uid
  11: double sumAmount;//总价
  12: i64 id;
  13: TCurrencyType currencyType;
  14: i32 appid;
  15: i32 playType;
  16: string expand;
  17: i64 ssid;
  18: i32 extendUidType;
  19: string guestNick;
  20: string recvNick;
  21: string currencyName;
  22: string propsSourceName;//结算类型
  23: string propTypeName;
  24: string identity;
}

struct TWeekPropsRecvInfoQueryPage {
  1: list<TWeekPropsRecvInfo> content;
  2: i32 page;
  3: i32 pagesize;
  4: i32 totalElement;
  5: i32 totalPage;
  6: map<string, string> extend;
}

struct TDatingRoomIncomeRequest {
  1: i64 sid;
  2: i64 ssid;
}

struct TDatingRoomIncome {
  1: i64 sid;
  2: i64 ssid;
  3: i64 amount;
}

struct TDatingGuestPropsRecvStatOfPlayType {
  1: i64 dt;
  2: i64 sid;
  3: i64 ssid;
  4: i64 guestUid;
  5: i64 amount;
  6: i32 type;
}

// --- 假日庄园相关统计接口

struct TDatingGardenDailyStat {
  1: i64 id;
  2: i64 dt; // 统计日期，如：1695571200000表示2023-09-25
  3: i32 appid;
  4: i64 uid; // 庄主UID
  5: i32 weixinTotal; // 包裹礼包微信支付购买数
  6: i32 alipayTotal; // 包裹礼包支付宝购买数
  7: i32 total; // 礼包购买总数（weixinTotal + alipayTotal + weixinTotalVirt + alipayTotalVirt + weixinTotalYb + alipayTotalYb）
  8: i64 createTime;
  9: i32 weixinTotalVirt; //紫水晶券礼包微信支付购买数
  10: i32 alipayTotalVirt; //紫水晶券礼包支付宝购买数
  11: i32 weixinTotalYb; //YB礼包微信支付购买数
  12: i32 alipayTotalYb;//YB礼包支付宝购买数
  13: i32 gardenType;
  14: i32 weixinQrcodeTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  15: i32 weixinWapTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  16: i32 alipayQrcodeTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  17: i32 alipayWapTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  18: i32 alipayH5miniTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  19: i32 weixinQrcodeTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  20: i32 weixinWapTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  21: i32 alipayQrcodeTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  22: i32 alipayWapTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  23: i32 alipayH5miniTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  24: i32 weixinQrcodeTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  25: i32 weixinWapTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  26: i32 alipayQrcodeTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  27: i32 alipayWapTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  28: i32 alipayH5miniTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
}

struct TDatingGardenDailyStatPageResult {
  1: i32 page; // 当前页号
  2: i32 pagesize; // 每页大小
  3: i32 total; // 总条数
  4: i32 totalPage; // 总页数
  5: list<TDatingGardenDailyStat> contents;
}

/**
* dt:日期毫秒数
* propsRealAmount: 待日结黄水晶数量
*/
struct TDatingAnchorDailyRealPreRevenueStat{
  1: i64 id;
  2: i64 dt;
  3: i64 uid;
  4: i64 propsRealAmount; // 礼物待日结黄水晶
  5: i64 propsRealAmountBullet;     // 礼物待日结黄水晶 只有弹幕礼物
  6: i64 propsRealAmountNoBullet;   // 礼物待日结黄水晶 所有礼物-弹幕礼物
}

struct TRoomIncomeStat {
  1: string statPeriod;//2023-11-07
  2: double propsTotalIncome;//总礼物流水
  3: double sealTotalIncome;//总盖章流水
  4: double propsSuperIncome;//超级主持礼物流水
  5: double propsNormalIncome;//普通主礼物流水
  6: double propsStarIncome;//星光主持礼物流水
  7: double sealSuperIncome;//超级主持盖章流水
  8: double sealNormalIncome;//普通主持盖章流水
  9: double sealStarIncome;//星光主持盖章流水
  10: list<TDatingDailyRoomStat> details;//主持流水明细
}

struct TCheckIdentityResult {
  1: i32 code;
  2: string message;
  3: bool check;
  4: string expand;
}
