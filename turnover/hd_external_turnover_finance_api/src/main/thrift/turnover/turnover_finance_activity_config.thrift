namespace java com.yy.hd.api.thrift.turnover.finance.activity.config

include "turnover_finance_common.thrift"

// TActivityConfigService 专用结构体
struct TPeiwanChargeGiftbagActivityInfo {
  1: i64 activityId;    // 活动id，0则表示新增，非0表示修改
  2: i64 startTime;     // 开始时间戳，单位ms
  3: i64 endTime;       // 结束时间戳，单位ms
  4: i64 dayLimitAmount;  // 每日限量，单位元，-1表示为限制
  5: double amount;     // 累计充值金额，单位元
}

struct TResult {
  1: i32 code;          // 1则成功
  2: string message;
  3: string expand;
}

service TActivityConfigService {

/**
 * 查询活动配置信息接口
 *
 * @param activityId 为0，则查全部
 * @return
 *
 */
list<TPeiwanChargeGiftbagActivityInfo> queryPeiwanChargeGiftbagActivityInfo(1: i64 activityId);

/**
 * 修改活动配置信息接口
 *
 * @param
 * @return
 *
 */
TResult addOrUpdatePeiwanChargeGiftbagActivityConfig(1: TPeiwanChargeGiftbagActivityInfo info);

}
