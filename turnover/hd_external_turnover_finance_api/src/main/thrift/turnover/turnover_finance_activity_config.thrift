namespace java com.yy.hd.api.thrift.turnover.finance.activity.config

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TPeiwanChargeGiftbagActivityInfo TPeiwanChargeGiftbagActivityInfo
typedef turnover_finance_common.TResult TResult

service TActivityConfigService {

/**
 * 查询活动配置信息接口
 *
 * @param activityId 为0，则查全部
 * @return
 *
 */
list<TPeiwanChargeGiftbagActivityInfo> queryPeiwanChargeGiftbagActivityInfo(1: i64 activityId);

/**
 * 修改活动配置信息接口
 *
 * @param
 * @return
 *
 */
TResult addOrUpdatePeiwanChargeGiftbagActivityConfig(1: TPeiwanChargeGiftbagActivityInfo info);

}
