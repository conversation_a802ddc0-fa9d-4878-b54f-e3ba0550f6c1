namespace java com.yy.hd.api.thrift.turnover.finance.dating.garden.stat

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId
typedef turnover_finance_common.TDatingGardenDailyStatPageResult TDatingGardenDailyStatPageResult

service TDatingGardenStatService {
  /**
   * 查询假日庄园流水统计
   * @param appId 2：交友
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @param uid 庄主uid（可选传0）
   * @param page 页号，从1开始
   * @param pagesize 每页大小
   */
  TDatingGardenDailyStatPageResult queryDatingGardenDailyStats(1: TAppId appid, 2: i64 startDate, 3: i64 endDate, 4: i64 uid, 5: i32 page, 6: i32 pagesize, 7: i32 gardenType) throws (1: TServiceException ex1);
}
