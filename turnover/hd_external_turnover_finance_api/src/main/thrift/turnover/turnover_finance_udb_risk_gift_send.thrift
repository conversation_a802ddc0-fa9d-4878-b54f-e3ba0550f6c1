namespace java com.yy.hd.api.thrift.turnover.finance.udb.risk.gift.send

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId
typedef turnover_finance_common.TCheckIdentityResult TCheckIdentityResult

service TUdbRiskGiftSendService {
  void ping2()
  list<i64> getSameIdentityUids(1: i64 fromUid, 2: list<i64> toUids);
 /**
    * 检查送礼合法性
    * @param appId 交友：2，聊天室：34
    * @param sid 频道id
    * @param ssid 子频道id
    * @param productId 礼物id
    * @param productType 礼物类型
    * @param uid 送礼人uid
    * @param anchorUid 主持uid
    * @param targetUid 收礼人uid
    * @param grabLoveType 抢爱心类型
    * @param expand 扩展字段
    * @return CheckIdentityResult
    * @throws ServiceException
    */
   TCheckIdentityResult checkGiftSendIdentity(1: TAppId appId, 2: i64 sid, 3: i64 ssid, 4: i32 productId, 5: i32 productType, 6: i64 uid, 7: i64 anchorUid, 8: i64 targetUid, 9: i32 grabLoveType, 10: string expand) throws (1: TServiceException ex1);

   /**
    * 检查消费道具合法性
    * @param appId 交友：2，聊天室：34
    * @param sid 频道id
    * @param ssid 子频道id
    * @param productId 礼物id
    * @param productType 礼物类型
    * @param uid 送礼人uid
    * @param anchorUid 主持uid
    * @param targetUid 收礼人uid
    * @param grabLoveType 抢爱心类型
    * @param expand 扩展字段
    * @return CheckIdentityResult
    * @throws ServiceException
    */
   TCheckIdentityResult checkConsumeProductIdentity(1: TAppId appId, 2: i64 sid, 3: i64 ssid, 4: i32 productId, 5: i32 productType, 6: i64 uid, 7: i64 anchorUid, 8: i64 targetUid, 9: i32 grabLoveType, 10: string expand) throws (1: TServiceException ex1);
}
