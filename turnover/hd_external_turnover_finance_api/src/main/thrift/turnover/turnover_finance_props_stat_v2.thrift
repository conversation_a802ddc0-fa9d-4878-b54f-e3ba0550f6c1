namespace java com.yy.hd.api.thrift.turnover.finance.props.stat

include "turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId
typedef turnover_finance_common.TSidInfo TSidInfo
typedef turnover_finance_common.TRevenueSrcType TRevenueSrcType
typedef turnover_finance_common.TPropsType TPropsType

// TPropsStatServiceV2 专用结构体
struct TDatingDailyIncomeData{
  1: i64 dt;
  2: i64 uid;
  3: double ybPayAmount;
  4: double virtChargeAmount;
  5: double reddiamondConsumeAmount;
}

// 注意amount的单位都是厘
struct TDatingBigCustomerDailyStatistic {
  1: i64 id;
  2: i64 dt;
  3: i64 uid;
  4: i64 propAmount;    // 礼物
  5: i64 doubiAmount;   // 逗比章
  6: i64 nobleAmount;   // 贵族
  7: i64 guardAmount;   // 守护
  8: i64 pinkAmount;    // 粉钻
}

struct THatSidIncomeStat{
  1: i64 sid;
  2: i64 amount;
  3: i32 quota;
}

struct TAnchorIncomeStat{
  1: i64 anchorUid;
  2: i64 amount;
}

struct TAnchorMissionPrizeMonthDetail{
  1: i64 uid;
  2: i64 yyno;
  3: string nickName;
  4: i64 sid;
  5: i64 prizeYuan;
  6: string anchorType;
  7: string prizeType;
  8: double incomeYuan;
  9: string level;
}

struct TAnchorMissionPrizeMonthDetailList{
  1: list<TAnchorMissionPrizeMonthDetail> items;
}

struct TAnchorDailyIncome {
  1: i64 revenueDate;           // 日结时间
  2: i64 tid;                   // 厅id
  3: i64 anchorUid;             // 主持uid
  4: i64 anchorImid;            // 主持YY号
  5: string anchorNick;         // 主持昵称
  6: double anchorIncome;       // 待日结黄水晶
  7: TRevenueSrcType srcType;   // 结算来源类型：1：礼物；21：盖章
  8: i32 owWeight;              // 公会分成比例
  9: i32 tingWeight;            // 厅管抽成比例
  10: double tingIncome;        // 厅管总日结黄水晶
}

struct TAnchorDailyIncomePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TAnchorDailyIncome> contents;
}

struct TMonthAnchorGiftIncomeStat {
  1: i64 anchorUid;
  2: i64 amount;
}

struct TZwyyfRoomDailyRevenueStat {
  1: i64 dt;                        // 日结时间
  2: i32 appid;                     // 业务ID
  3: i64 uid;                       // 房主uid
  4: double realIncome;             // 普通礼物黄钻日结
  5: double turntableRealIncome;    // 约会礼物黄钻日结
  6: double otherRealIncome;        // 其他黄钻收入
  7: double totalRealIncome;        // 合计黄钻收入
}

struct TZwyyfAnchorDailyRevenueStat {
  1: i64 dt;                    // 日结时间
  2: i32 appid;                 // 业务ID
  3: i64 uid;                   // 主播uid
  4: i64 sid;                   // 工会sid
  5: double allIncome;          // 普通礼物金钻流水
  6: i32 rate;                  // 普通礼物日结比例
  7: double allIncomeTurntable; // 约会礼物金钻流水
  8: i32 rateTurntable;         // 约会礼物日结比例
  9: double weight;             // 房主抽成比例
  10: double anchorDailyIncome; // 主播日结黄钻
  11: double owDailyIncome;     // 房主日结黄钻
  12: string expand;            // 扩展字段 {"agentUid":"xxx"}
}

struct TDailyRoomRevenuePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfRoomDailyRevenueStat> contents;
}

struct TDailySignedAnchorRevenuePageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TZwyyfAnchorDailyRevenueStat> contents;
}

struct TSignedAnchorPropsUsedRecord {
  1: i64 id;                // 记录ID
  2: i64 usedTime;          // 送礼时间
  3: TPropsType type;       // 礼物类型 2 收费道具, 14 宝箱礼物 17 约会礼物
  4: string name;           // 礼物名称
  5: i32 count;             // 礼物数量
  6: i64 senderImid;        // 送礼YY号
  7: string senderNick;     // 送礼昵称
  8: i64 receiverImid;      // 收礼YY号
  9: string receiverNick;   // 收礼昵称
  10: i64 signedSid;        // 签约房间
  11: i64 sid;              // 开播房间
  12: i32 currencyType;     // 货币类型 金钻-68
  13: double currencyAmount;// 价格
  14: i64 ssid;             // 开播房间ssid
  15: i64 familyId;         // 家族id
}

struct TSignedAnchorPropsUsedRecordPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TSignedAnchorPropsUsedRecord> contents;
}

struct TExchangePropsHistoryResult {
  1: i64 uid; // 用户id
  2: i64 createTime; // 兑换时间
  3: i32 type;
  4: i32 srcPropId; // 消耗道具id
  5: i64 srcPropCnt; // 消耗道具数量
  6: i32 srcPropPricingId;
  7: i32 srcCurrencyType;
  8: i64 srcCurrencyAmount;
  9: i32 destPropId; // 兑换道具id
  10: i64 destPropCnt; // 兑换道具数量
  11: i32 destPropPricingId;
  12: i32 destCurrencyType;
  13: i64 destCurrencyAmount;
  14: i32 appid; // 业务id
  15: i64 exchangeConfigId;
  16: string srcPropName; // 消耗道具名称
  17: string destPropName; // 兑换道具名称
  18: string exchangeConfigName; // 兑换配置名称
  19: i64 sid;
  20: i64 ssid;
  21: i64 csid; // 签约频道id
  22: i64 anchorUid;
}

struct TExchangePropsHistoryPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TExchangePropsHistoryResult> contents;
}

struct TProductConsumeLevel{
  1: i32 appid;
  2: i64 uid;
  3: i32 level; //加油派对等级
  4: i64 allAmount; //当前经验
  5: i64 todayAmount; //当日累计经验
  6: i64 nextAmount; //下一级经验
}

struct TChannelIncomeRank{
  1: i32 rank;
  2: i64 sid;
  3: double income;
}

struct TDatingDailyTingMgrIncomeStat {
  1: i64 id;
  2: i64 dt;
  3: i32 appid;
  4: i64 uid;
  5: double income;
}

struct TDatingDailyPersonalRoomStat {
  1: i32 id;
  2: i64 statDate;
  3: i64 sid;
  4: i64 ssid;
  5: i64 anchorUid;
  6: i32 isSuper;
  7: i64 propsIncome;
  8: i64 sealIncome;
}

struct TDatingDailyAnchorIncomeStatNotRoomMgr {
  1: i32 id;
  2: i64 statDate;
  3: i64 anchorUid;
  4: i64 sid;
  5: double propsIncome;  // 礼物流水
  6: double sealIncome; // 盖章流水（所有）
  7: double sealDragonIncome; // 神龙章流水
  8: double propsIncomeWithoutMagic; // 排除魔法礼物的流水
}

struct TAnchorIncomeStatData{
  1: i64 statDate;
  2: i64 anchorUid;
  3: i64 sid;
  4: i64 ssid;
  5: double propsIncome;
  6: double sealIncome;
}

struct TPersonalRoomIncomeRankData {
	1: i64 sid
	2: i64 ssid
	3: i64 roomUid
	4: i64 propsIncome
}

struct TDatingDailyRoomStat{
	1: i64 sid
	2: i64 ssid
	3: i64 anchorUid
	4: i64 statDate
	5: i64 propsIncome
	6: i64 sealIncome
}

struct TUserPropsPeriodHistory {
  1: i64 id;
  2: i64 periodId;
  3: string seqId;
  4: i64 uid;
  5: i64 propId;
  6: i64 pricingId;
  7: i32 appid;
  8: i64 oriCnt;
  9: i64 changeCnt;
  10: string description;
  11: i32 srcType;
  12: i64 activityId;
  13: i64 createTime; // 操作时间
  14: i64 startTime;
  15: i64 endTime;
  16: string userIp;
  17: string expand;
  18: i32 usedChannel;
}

struct TUserPropsPeriodHistoryPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TUserPropsPeriodHistory> contents;
}

struct TDatingRoomRecvIncomeStat {
  1: i64 dt;
  2: i64 ssid;
  3: i64 recvUid;
  4: i64 propsIncome;
  5: i32 recvUserType;
}

service TPropsStatServiceV2 {
    void ping2()
    map<i64, double> last7DayAnchorPropsUseForStat(1: list<i64> anchorUids, 2: TAppId appid);

  /**
   * 查询指定时间段内,某活动下发的礼物价值(折算成紫水晶)
   * 慢查询,仅用于对账,endTime-startTime 每次最多查3天
   * @param appId
   * @param activityId 活动id
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return 礼物紫水晶价值
   */
    i64 getTotalIssuePropsAmountByActivity(1: i32 appId, 2: i32 activityId, 3: i64 startTime, 4: i64 endTime);

   /**
    * 是否用户最近消费过
    * @param startTime 统计起始时间
    * @return 0.没有  1.有
    */
    i32 isUserConsumedRecently(1: i64 uid, 2: TAppId appid, 3: i64 startTime);

   /**
    * 统计子频道礼物消费总额记录数
    * (每天1点生成昨天的数据,因为是从hive取数,所以即使是测试环境,查到的数据也是生产数据)
    * @param appid 交友:2
    * @param date yyyy-MM-dd
    * @param sidInfo sid信息
    * @return nil: 表示未生成数据
    */
    map<TSidInfo, i64> queryDailySsidConsume(1: i32 appid, 2: string date, 3: list<TSidInfo> sidInfoList);

    //queryTime 格式"yyyy-mm"
    //return map<sid, 流水总和>
    map<i64,double> queryGiftIncomeByMonth(1: i32 appid, 2: string queryTime, 3: list<i64> sidList);

    
    /**
    * 获取N天用户流水总和(N-2天的统计数据+2天的实时数据) (旧接口,即将下线)
    * @param uid 				用户uid 
    * @param appid 				业务id(交友填2)
    * @param daysBeforeList     待查的天数列表(比如list(0,1,30)表示查今天,1天内(昨天和今天), 30天内(今天和之前30天)的数据)
    */
    map<i32, TDatingDailyIncomeData> queryDailyIncomeData(1: i32 appid, 2: i64 uid, 3: list<i32> daysBeforeList);

     /**
       * 查询交友大客户日统计数据
       * 查旧统计口径的数据，汇聚指定时间范围的数据，上线之后90天内需要使用，之后可以下线
       * @param appid 业务id
       * @param uid 客户uid
       * @param fromDate 开始日期yyyyMMdd
       * @param toDate 结束日期yyyyMMdd
       * @return DatingDailyIncomeData（注意单位是元）
     */
     TDatingDailyIncomeData queryDailyIncomeDataV2(1: i32 appid, 2: i64 uid, 3: string fromDate, 4: string toDate);

    /**
     * 查询交友大客户日统计数据
     * 查新统计口径的数据，汇聚指定时间范围的数据
     * @param appid 业务id
     * @param uid 客户uid
     * @param fromDate 开始日期yyyyMMdd
     * @param toDate 结束日期yyyyMMdd
     * @return DatingBigCustomerDailyStatistic（单位：厘）
     */
    TDatingBigCustomerDailyStatistic queryDatingBigCustomerDailyStatistic(1: i32 appid, 2: i64 uid, 3: string fromDate, 4: string toDate);

    /*
     * 定时计算工会流水 （每月1号)
     * queryMonth格式: yyyymm
     */
    list<THatSidIncomeStat> queryHatSidIncomeStat(1: i32 appid, 2: string queryMonth, 3: i64 sid);

    /*
     * 定时计算特定主持的盖章流水
     * queryMonth格式: yyyymm
     */
    list<TAnchorIncomeStat> queryMonthAnchorSealIncomeStat(1: i32 appid, 2: string queryMonth, 3: list<i64> anchorUidList);

    // 0：（默认）紫水晶券、紫水晶、Y币等扣费，1：仅使用吃鸡队友金扣费，2：仅使用紫金币扣费
    i32 getCurrencyConsumeMode(1: i32 appid, 2: i64 uid);

    /*
    * 查询指定月流水范围的主播uid
    * appid
    * queryMonth
    * anchorUidList
    * minIncome 最小金额(包括该值,忽略该字段填0)
    * maxIncome 最大金额(不包括该值,忽略该字段填0)
    * 返回 主播uid列表
     */
    list<i64> queryGiftIncomeAnchorsByMonth(1: i32 appid, 2: string queryMonth, 3: double minIncome, 4: double maxIncome);

    /**
    * 查询主持某月结算等级次数
    * queryMonth 格式yyyymm
    * srcType:结算类型 1.普通结算  39.多人结算
    * 返回<等级, 次数> 比如 {"A":7,"B":3,"C":4,"D":4}
    */
    map<string, i32> queryAnchorRevenueLevelBySrcType(1:i32 appid ,2:i64 uid, 3:string queryMonth, 4:i32 srcType)
    // @丁拥 settleMode 结算模式，默认"AnchorMode"。 "AnchorMode"：主持人模式；"ReceiverMode"：收礼人模式；
    map<string, i32> queryAnchorRevenueLevel(1:i32 appid ,2:i64 uid, 3:string queryMonth, 4: string settleMode)

   /**
    * 多人视频抽成玩法
    * 返回 <sid或ssid, 结果>  1:白名单  0.非白名单
    */
    map<i64, i32> querySidInWhiteList(1:i32 appid, 2:i64 sid,  3:i64 ssid)

    /**
    * 全量查询多人视频抽成玩法白名单
    * 返回 map<"sid"/"ssid", sid/ssid列表> 比如 {"sid": ["1", "2", "3"], "ssid": ["4", "5", "6"]}
    * 白名单为空, 返回{}
    */
    map<string, list<string>> querySidWhiteList(1: i32 appid)

    /**
    * 查询主持某月日结档位次数
    * queryMonth 格式yyyymm
    * srcType:结算类型 1.普通结算  39.多人结算
    * uid 为0,则批量查询。
    */
    TAnchorMissionPrizeMonthDetailList queryAnchorRevenueLevelDetailBySrcType(1:i32 appid ,2:string queryMonth, 3:i64 uid,  4:i32 srcType)
    // @丁拥 settleMode 结算模式，默认"AnchorMode"。 "AnchorMode"：主持人模式；"ReceiverMode"：收礼人模式；
    TAnchorMissionPrizeMonthDetailList queryAnchorRevenueLevelDetail(1:i32 appid ,2:string queryMonth, 3:i64 uid, 4: string settleMode)

    /**
     * 查询公会指定月份流水 单位/元
     */
    map<i64, double> queryDatingChannelPropsIncome(1: list<i64> sidList, 2: string yyyyMM);

    /**
    * 查主持日结明细
    * @param appid 业务ID 必填
	* @param sid 公会id 必填
	* @param tingMgrUid 厅管uid 必填
	* @param tid 厅ID
	* @param anchorImid 主持YY号
	* @param startTime 日结开始时间
	* @param endTime 日结结束时间 
	* @return AnchorDailyIncomePageResult
    */
    TAnchorDailyIncomePageResult queryAnchorDailyIncomes(1: TAppId appid, 2: i64 sid, 3: i64 tingMgrUid, 4: i64 tid, 5: i64 anchorImid, 6: i64 startTime, 7: i64 endTime, 8: i32 page, 9: i32 pagesize) throws (1: TServiceException ex1);

    /**
    * 查指定主播的月礼物流水（单位：黄水晶）
    * @param appid 业务ID
    * @param queryMonth 格式: yyyymm
    * @param anchorUidList 主播uid列表
    * @return List&lt;MonthAnchorGiftIncomeStat&gt;
    */
    list<TMonthAnchorGiftIncomeStat> queryMonthAnchorGiftIncomeStat(1: TAppId appid, 2: string queryMonth, 3: list<i64> anchorUidList) throws (1: TServiceException ex1);

    /**
    * 查询每日房主的黄钻收入
    * @param appid 业务ID
    * @param uid 房主uid
    * @param startTime 开始日期
    * @param endTime 结束日期
    * @param page 页号(1, 2, 3...)
    * @param pagesize 每页大小
    * @return DailyRoomRevenuePageResult
    * @throws ServiceException
    */
    TDailyRoomRevenuePageResult queryDailyRoomRevenues(1: TAppId appid, 2: i64 uid, 3: i64 startTime, 4: i64 endTime, 5: i32 page, 6: i32 pagesize) throws (1: TServiceException ex1);

    /**
    * 查询每日频道签约主播的日结黄钻
    * @param appid 业务ID
    * @param sid 工会sid
    * @param imid 主持YY号
    * @param startTime 开始日期
    * @param endTime 结束日期
    * @param page 页号(1, 2, 3...)
    * @param pagesize 每页大小
    * @return 符合条件的记录列表
    * @throws DailySignedAnchorRevenuePageResult
    */
    TDailySignedAnchorRevenuePageResult queryDailySignedAnchorRevenues(1: TAppId appid, 2: i64 sid, 3: i64 imid, 4: i64 startTime, 5: i64 endTime, 6: i32 page, 7: i32 pagesize) throws (1: TServiceException ex1);

    /**
    * 查看旗下签约主播每条收到礼物明细记录
    * @param appid 业务ID
    * @param sid 工会sid。若imid>=0,则按imid查，不按sid查。
    * @param imid 主持YY号。若imid>=0,则按imid查，不按sid查。
    * @param startTime 开始日期 yyyy-MM-dd
    * @param endTime 结束日期 yyyy-MM-dd
    * @param page 页号(1, 2, 3...)
    * @param pagesize 每页大小
    * @return SignedAnchorPropsUsedRecordPageResult
    * @throws ServiceException
    */
    TSignedAnchorPropsUsedRecordPageResult querySignedAnchorPropsUsedRecords(1: TAppId appid, 2: i64 sid, 3: i64 imid, 4: i64 startTime, 5: i64 endTime, 6: i32 page, 7: i32 pagesize) throws (1: TServiceException ex1);

    /**
     * 查询用户道具兑换记录(按时间倒序)
     * @param appid 业务ID 必填
     * @param uid 用户ID 必填
     * @param srcPropId 消耗礼物id，豆荚：20209 必填
     * @param type 0-不区分类型 1-道具，2-货币, 3-货币券, 4-交友抽取道具券, 5-勋章
     * @param page 页码，1为第一页，默认1 必填
     * @param pagesize 每页数量，默认50 必填
     */
    TExchangePropsHistoryPageResult getExchangePropsHistorys(1: TAppId appid, 2: i64 uid, 3: i32 srcPropId, 4: i32 type, 5: i64 date, 6: i32 page, 7: i32 pagesize) throws (1: TServiceException ex1);

    /**
     * 查询用户道具兑换记录
     * @param appid 业务ID 必填
     * @param uid 用户ID
     * @param srcPropId 消耗礼物id，豆荚：20209 必填
     * @param type 1-道具，2-货币, 3-货币券, 4-交友抽取道具券, 5-勋章
     * @param startTime 起始时间
     * @param endTime 结束时间
     * @param page 页码，1为第一页，默认1 必填
     * @param pagesize 每页数量，默认50 必填
     */
    TExchangePropsHistoryPageResult getExchangePropsHistorysV2(1: TAppId appid, 2: i64 uid, 3: i32 srcPropId, 4: i32 type, 5: i64 startTime, 6: i64 endTime, 7: i32 page, 8: i32 pagesize) throws (1: TServiceException ex1);

   /**
    * 查交友频道礼物收入
    * @param sidList
    * @param fromDate (包括该日) yyyymmdd 0点
    * @param toDate  (不包括该日) yyyymmdd 0点
    * fromDate "20210528",  toDate "20210628" =  20210528 0点 到 20210628 0点期间的流水
    * @return
    */
    map<i64, double> queryDatingChannelPropsIncomeByDate(1: list<i64> sidList, 2: string fromDate, 3: string toDate);

    /**
     * 查询公会有价值礼物流水
     * return 紫水晶
     */

    i64 querySidIncomeByMonth(1:i32 appid, 2:i64 sid, 3:string yyyymm) throws (1: TServiceException ex1);

    /**
     * 查询公会有价值礼物流水(不包含弹幕礼物)
     * return 紫水晶
     */
    i64 querySidIncomeByMonthNoBullet(1:i32 appid, 2:i64 sid, 3:string yyyymm) throws (1: TServiceException ex1);


    /** 查询达人等级
    * appid 业务id
    * uid
    * @return TProductConsumeLevel
    */
    /*TProductConsumeLevel getUserProductConsumeLevel(1: i32 appid, 2: i64 uid);*/

    /** 批量查询达人等级
    * appid 业务id
    * uidList
    * @return TProductConsumeLevel
    */
    /*list<TProductConsumeLevel> getUsersProductConsumeLevel(1: i32 appid, 2:list<i64> uidList)*/

    /**
     * 按日期查询有收入主持数量
     * @param appid 业务ID 必填
     * @param sid 频道ID 必填
     * @param startTime 开始时间 必填
     * @param endTime 结束时间 必填
     * @return Map<结算日期,主持数量>
     */
    map<i64, i64> countGiftIncomeAnchor(1: TAppId appid, 2: i64 sid, 3: i64 startTime, 4: i64 endTime) throws (1: TServiceException ex1);

     /**
      * 主持礼物日流水 (按天统计，最多取最近90天，包括今天)
      * @param appId 业务ID 必填
      * @param anchorUid 主持uid
      * @param startDay 日结开始日期
      * @param endDay 日结结束日期
      * @return <'yyyy-MM-dd', 日流水（元）>
      */
     map<string, double> getAnchorPropIncomesByDay(1: TAppId appId, 2: i64 anchorUid, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
     * 批量查主持礼物日流水 (按天统计，最多取最近90天，包括今天)
     * @param appId 业务ID 必填
     * @param anchorUid 主持uid
     * @param startDay 日结开始日期
     * @param endDay 日结结束日期
     * @return <uid,<'yyyy-MM-dd', 日流水（元）>>
    */
    map<i64, map<string, double>> batchGetAnchorPropIncomesByDay(1: TAppId appId, 2: list<i64> anchorUids, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

     /**
      * 主持礼物月流水 (按月统计，最多取最近6个月，包括本月)
      * @param appId 业务ID 必填
      * @param anchorUid 主持uid
      * @param startMonth 月结开始月份
      * @param endMonth 月结结束月份
      * @return <'yyyy-MM', 月流水（元）>
      */
     map<string, double> getAnchorPropIncomesByMonth(1: TAppId appId, 2: i64 anchorUid, 3: i64 startMonth, 4: i64 endMonth) throws (1: TServiceException ex1);

    /**
     * 查主持日结黄水晶（仅支持查最近6个自然月）实时 (各种交友结算模式都会有覆盖到)
     * @param appId 业务ID 必填
     * @param anchorUid 主持uid
     * @param startDay 日结开始日期 ms
     * @param endDay 日结结束日期 ms
     * @return <'yyyy-MM-dd', 日结黄水晶>
     */
    map<string, i64> getAnchorPropRevenueIncomesByDay(1: TAppId appId, 2: i64 anchorUid, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 批量查主持日结黄水晶（仅支持查最近6个自然月）
    * @param appId 业务ID 必填
    * @param anchorUids 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <uid,<'yyyy-MM-dd', 日结黄水晶>>
    */
    map<i64, map<string, i64>> batchGetAnchorPropRevenueIncomesByDay(1: TAppId appId, 2: list<i64> anchorUids, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

   /**
    * 最近7天频道礼物流水排名
    * days: 往前查几天
    */
   list<TChannelIncomeRank> queryGiftIncomeRankByDays(1: i32 appid, 2: i32 days, 3: list<i64> sidList);

    /**
    * 查询交友全部频道总流水
    * @param appId      业务ID 必填
    * @param startTime  开始时间(如20211001)
    * @param endTime    结束时间(如20211031)
    * @return 所有频道总礼物流水
    */
    double queryAllGiftIncome(1: i32 appid, 2: string startTime, 3: string endTime) throws (1: TServiceException ex1);

    /**
    * 查询交友全部频道总流水(不包含弹幕礼物)
    * @param appId      业务ID 必填
    * @param startTime  开始时间(如20211001)
    * @param endTime    结束时间(如20211031)
    * @return 所有频道总礼物流水
    */
    double QueryAllGiftIncomeNoBullet(1: i32 appid, 2: string startTime, 3: string endTime) throws (1: TServiceException ex1);


    /**
    * 批量查询每日厅管礼物流水（包含今天的数据，今天的数据每小时刷新）
    * @param tingMgrUids 厅管uid列表
    * @param timeGreaterThan 开始日期
    * @param timeLessThan 结束日期
    * @return List<DatingDailyTingMgrIncomeStat>
    * @throws ServiceException
    */
    list<TDatingDailyTingMgrIncomeStat> batchQueryDatingDailyTingMgrIncomeStat(1: list<i64> tingMgrUids, 2: i64 timeGreaterThan, 3: i64 timeLessThan) throws (1: TServiceException ex1);

    /**
    * 查询每日厅管礼物流水（包含今天的实时数据）
    * @param tingMgrUid 厅管uid
    * @param timeGreaterThan 开始日期
    * @param timeLessThan 结束日期
    * @return List<DatingDailyTingMgrIncomeStat>
    * @throws ServiceException
    */
    list<TDatingDailyTingMgrIncomeStat> queryDatingDailyTingMgrIncomeStatWithTodayRealTime(1: i64 tingMgrUid, 2: i64 timeGreaterThan, 3: i64 timeLessThan) throws (1: TServiceException ex1);

    /**
    * 主持礼物日流水 只含房管模式(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <'yyyy-MM-dd', 日流水（元）>
    */
    map<string, double> getAnchorPropIncomesByDayInRoomMgr(1: TAppId appId, 2: i64 anchorUid, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 主持礼物日流水 排除房管模式(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <'yyyy-MM-dd', 日流水（元）>
    */
    map<string, double> getAnchorPropIncomesByDayNotRoomMgr(1: TAppId appId, 2: i64 anchorUid, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 批量查主持礼物日流水 天团厅房管(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <uid, <'yyyy-MM-dd', 日流水（元）>>
    */
    map<i64, map<string, double>> batchGetAnchorPropIncomesByDayInRoomMgr(1: TAppId appId, 2: list<i64> anchorUids, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 批量查主持礼物日流水 个播厅房管(按天统计，最多取最近90天，不包括今天)
    * @param startDate 日结开始日期
    * @param endDate 日结结束日期
    * @param isSuper 0:普通主持 1：超级主持
    *
    */
    list<TDatingDailyPersonalRoomStat> getPersonRoomPropsIncomeByDate(1: i64 startDate, 2: i64 endDate, 3: i32 isSuper, 4: i64 sid, 5: i64 ssid);

    /**
    * 批量查主持礼物日流水 排除房管模式(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <uid, <'yyyy-MM-dd', 日流水（元）>>
    */
    map<i64, map<string, double>> batchGetAnchorPropIncomesByDayNotRoomMgr(1: TAppId appId, 2: list<i64> anchorUids, 3: i64 startDay, 4: i64 endDay) throws (1: TServiceException ex1);

    /**
    * 主持礼物日流水 按ssid维度查 只含房管模式(按天统计，最多取最近90天，包括今天)
    * @param appId 业务ID 必填
    * @param anchorUid 主持uid
    * @param ssid ssid
    * @param startDay 日结开始日期
    * @param endDay 日结结束日期
    * @return <'yyyy-MM-dd', 日流水（元）>
    */
    map<string, double> getAnchorPropIncomesBySsidInRoomMgr(1: TAppId appId, 2: i64 anchorUid, 3: i64 ssid, 4: i64 startDay, 5: i64 endDay) throws (1: TServiceException ex1);

    //返回  map<ssid, 紫水晶>
    map<i64, i64> getSsidMonthlyIncomeFromRank(1: i64 sid, 2: list<i64> ssidList, 3: string yyyymm) throws (1: TServiceException ex1);

    /**
      * 主持日流水 只含房管模式(按天统计，最多取最近90天，包括今天)
      * @param appId 业务ID 必填
      * @param sid 公会sid 填0表示忽略
      * @param anchorUid 主持uid
      * @param startDay 日结开始日期
      * @param endDay 日结结束日期
      * @return List<DatingDailyAnchorIncomeStatNotRoomMgr>
    */
    list<TDatingDailyAnchorIncomeStatNotRoomMgr> getDatingDailyAnchorIncomeStatNotRoomMgrV2(1: TAppId appId, 2: i64 sid, 3: i64 anchorUid, 4: i64 startDay, 5: i64 endDay) throws (1: TServiceException ex1);

    // 房管模式下按照ssid 查询
    list<TAnchorIncomeStatData> queryAnchorIncomesBySsidInAllRoomMode(1: TAppId appid, 2: i64 anchorUid, 3: i64 ssid, 4: i64 startDay, 5: i64 endDay) throws (1: TServiceException ex1);

    /**
     * 查个播厅本周topN流水 @吴熙
     */
    list<TPersonalRoomIncomeRankData> queryTopNPersonalRoomIncomeThisWeek(1: TAppId appid, 2: i32 limitSize)

    /**
     * 日期格式: yyyymmdd 流水单位/紫水晶 @吴熙
     */
    list<TDatingDailyRoomStat> getRoomIncome(1: string startYMD, 2: string endYMD, 3:i64 sid, 4:i64 ssid);

    /**
      "查询某月房管厅普通主持礼物流水之和",
      "@param appId 业务ID",
      "@param ssid 房管ssid",
      "@param queryPublicRoom 是否累计天团厅流水",
      "@param queryPersonalRoom 是否累计个播厅流水",
      "@param monthOffset 0.本月 -1.上月 -2.上上月",
      "@return 礼物流水(元)"
      */
    double queryRoomNormalPropsIncomeByMonth(1: TAppId appId, 2: i64 ssid, 3: bool queryPublicRoom, 4: bool queryPersonalRoom, 5: i32 monthOffset) throws (1: TServiceException ex1);

   /**
    "查询某月房管厅 普通主持\超级主持\星光主持 礼物流水礼物流水之和",
    "@param appId 业务ID",
    "@param ssid 房管ssid",
    "@param queryPublicRoom 是否累计天团厅流水",
    "@param queryPersonalRoom 是否累计个播厅流水",
    "@param monthOffset 0.本月 -1.上月 -2.上上月",
    "@param noBullet false:所有礼物；true排除弹幕礼物",
    "@return 礼物流水(元)"
    */
    double queryRoomAllPropsIncomeByMonth(1: TAppId appId, 2: i64 ssid, 3: bool queryPublicRoom, 4: bool queryPersonalRoom, 5: i32 monthOffset, 6:bool noBullet) throws
    (1: TServiceException ex1);

    /**
     * 查询用户指定礼物的变更流水
     * @param appid 业务ID
     * @param uid 用户uid
     * @param propId 礼物id，玉石20209
     * @param propAdd 流水类型：1增；0减；-1所有
     * @param startTime 开始日期（包含，单位毫秒）
     * @param endTime 结束日期（不包含，单位毫秒）
     * @param page 页号(1, 2, 3...)
     * @param pagesize 每页大小，最大2000
     * @param srcTypes 14标识用礼物消费
     * @return UserPropsPeriodHistoryPageResult
     * @throws ServiceException
     */
    TUserPropsPeriodHistoryPageResult queryUserPropsPeriodHistoryV2(1: i32 appid, 2: i64 uid, 3: i64 propId, 4: i32 propAdd, 5: i64 startTime, 6: i64 endTime, 7: i32 page, 8: i32 pagesize, 9: list<i32> srcTypes) throws (1: TServiceException ex1);

    /**
     * 查询交友房管厅收礼人流水
     * @param startDate YYYY-MM-dd 毫秒,送礼时间 >= startDate,必填
     * @param endDate YYYY-MM-dd 毫秒,送礼时间 < endDate,必填
     * @param ssids,必填
     * @param recvUids 可选
     * @param recvUserType 1：主持，2：嘉宾
     * @return list
     */
    list<TDatingRoomRecvIncomeStat> queryRoomRecvUserIncome(1: i64 startDate, 2: i64 endDate, 3: list<i64> ssids, 4: list<i64> recvUids, 5: i32 recvUserType) throws (1: TServiceException ex1);

    /**
    * 新榜单(day,week,month;top10)
    * 重构：/v4/datingRoomMgr/getNewRank
    * @param roomMgrUid 房管uid
    * @param type 榜单类型：1-byDate, 2-byWeek, 3-byMonth, 4-latestNDays
    * @return 同/v4/datingRoomMgr/getNewRank -> data
    * ServiceException -401:未签约房管,  -500:服务器异常
    */
    string getNewRank(1: i64 roomMgrUid, 2: i32 type) throws (1: TServiceException ex1);

    /**
    * 房管佣金收支（全部）
    * 重构：/v4/datingRoomMgr/personCommission
    * @param roomMgrUid 房管uid
    * @param justRJ 是否仅返回日结数据，默认全部返回
    * @return 同/v4/datingRoomMgr/personCommission -> data
    * ServiceException -401:未签约房管,  -500:服务器异常
    */
    string personCommission(1: i64 roomMgrUid, 2: bool justRJ) throws (1: TServiceException ex1);

    /**
    * 房管礼物盖章流水核心数据
    * 重构：/v5/datingRoomMgr/roomIncomeSummary
    * @param roomMgrUid 房管uid
    * @return 同/v5/datingRoomMgr/roomIncomeSummary -> data
    * ServiceException -401:未签约房管,  -500:服务器异常
    */
    string roomIncomeSummary(1: i64 roomMgrUid) throws (1: TServiceException ex1);

    /**
    * 房管本月普通主持礼物流水总和
    * 重构：/v5/datingRoomMgr/roomNormalPropsIncome
    * @param roomMgrUid 房管uid
    * @param noBullet 是否排除弹幕礼物
    * @return 同/v5/datingRoomMgr/roomNormalPropsIncome -> data
    * ServiceException -401:未签约房管,  -500:服务器异常
    */
    string roomNormalPropsIncome(1: i64 roomMgrUid, 2: bool noBullet) throws (1: TServiceException ex1);
}
