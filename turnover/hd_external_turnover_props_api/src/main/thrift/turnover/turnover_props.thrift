namespace java com.yy.hd.api.thrift.turnover

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"

typedef turnover_common.TCurrencyType TCurrencyType
typedef turnover_common.TAppId TAppId
typedef turnover_common.TYyInfo TYyInfo
typedef turnover_common.UsedChannelType UsedChannelType

enum TPropsType {
  Activity = 1,
  Premium = 2,
  TipsPack = 3,
  Tutor = 4,
  GiftBag = 5,
  ExternalCurrency = 6,
  VipNiuNiu = 7,
  PropsPieces = 8,
  OneTimeProp = 9,
  Free = 10,
  Meme = 11,
  GameProps = 12,
  Random = 13,
  Box = 14,
  Exclusive = 15,
  Special = 16,
  Turntable = 17
}

enum TAccountOperateType {
  Withdraw = 1,
  Exchange = 2,
  ConsumeProps = 3,
  PropsRevenue = 4,
  BuyVirtOverage = 5,
  BuyVirtOverageFail = 6,
  ActivityAutoInc = 7,
  AutoMonthSettle = 8,
  AccountFreeze = 9,
  AccountUnfreeze = 10,
  ChannelRealToPersonVirt = 11,
  IssueSycee = 12,
  Transfer = 13,
  SystemOper = 14,
  SystemCompensate = 15,
  ExternalModification = 16,
  GiftBagLottery = 17,
  ChargeCurrency = 18,
  ChargeCurrencyPresent = 19,
  ChargeCurrencyDiscount = 20,
  DatingBackupGroup = 21,
  DatingBackupGroupBackFee = 22,
  DatingBackupGroupFinish = 23,
  BuySkin = 24,
  BuySeal = 25,
  BuySealBackFee = 26,
  RedPacketIssue = 27,
  RedPacketCharge = 28,
  RedPacketGrab = 29,
  RedPacketClose = 30,
  NobleOpen = 31,
  NobleRenew = 32,
  NobleUpgrade = 33,
  UPGRADE_PROPS = 34,
  REVERT_PAY = 35,
  WithdrawBack = 36,
  NobleRenewExchangeVirt = 37,
  OfficialIssue = 38,
  PayVipRoom = 39,
  ConsumePropsForOther = 40,
  VirtLottery = 41,
  BuySpoofHanging = 42,
  LuckyTreasures = 43,
  ProductConsume = 44,
  ProductConsumeRevert = 45,
  BuyLotteryChance = 46,
  HatKing = 47,
  GuardOpen = 48,
  GuardRenew = 49,
  ModifyGuardIntimateAccount = 50,
  ModifyHatKingPond = 51,
  HatKingReward = 52,
  PropsExchange = 53,
  ComboBonus = 54,
  ExtraAccountEntry = 55,
  RedPacketExpireBack = 56,
  NobleDowngrade = 57,
  ActRevenueSubsidy = 58,
  ImmediateWithdraw = 59,
  Clear = 60,
  AuctionAward = 61,
  DragonBall = 62,
  NobleRebate = 63,
  ActivityAward = 64,
  ActivityConsume = 65,
  ExchangeFreeHeartToActivity = 66,
  ManualWithdraw = 67,
  Issue = 68,
  BuyChallengeTicket = 69,
  ConsumeChallengeTicket = 70,
  HuabaDraw = 71,
  HuabaRepay = 72,
  OfficialChargeIssue = 73,
  HuabaManualRepay = 74,
  PayExternalMarketPromotion = 75,
  RevenueBonus = 76,
  ChargeAddAdditionCard = 77,
  ConsumeAnchorMotivation = 78,
  AgencyChargeCurrency = 79,
  VipRebate = 80,
  ChickenTeammateExchange = 81,
  ExchangeSmallHeartToActivity = 82,
  ConsumeGoldenShellTicket = 83,
  ViolatePunish = 84,
  Commission = 85,
  TingMgrRevenue = 86,
  CommissionSupply = 87,
  ViolatePunishRebate = 88
}

struct TChannelAccount {
  1: i64 uid;
  2: TCurrencyType currencyType;
  3: i64 amount;
  4: i64 freezed;
  5: i32 appid;
  6: i64 sid;
}

struct TPropsMeta {
  1: i32 id;
  2: string name;
  3: string shortName;
  4: TPropsType type;
  5: TAppId appId;
  6: string expand;
  7: string afterConsume;
  8: string afterUse;
  9: string beforeConsume;
  10: string beforeUse;
  11: i32 visible;
  12: i64 endTime;
  13: i64 startTime;
  14: i64 createTime;
}

struct TPropsPricing {
  1: i32 id;
  2: i32 propsId;
  3: TCurrencyType currencyType;
  4: double currencyAmount;
  5: i32 month;
  6: string priceHints;
  7: i32 visible;
  8: string userTypeLimit;
  9: i32 usedChannel;
  10: i64 effectStartTime;
  11: i64 effectEndTime;
  12: i32 weight;
  13: double revenueRate;
  14: i32 revenueType;
  15: i32 appid;
}

struct TPropsChannel {
  1: i32 id;
  2: i32 propId;
  3: UsedChannelType channelType;
  4: string description;
  5: string priority;
  6: string imgs;
  7: i32 visible;
  8: i32 usable;
  9: string tips;
  10: string packagePriority;
  11: i32 useType;
  12: string userTitle;
  13: string userTitleName;
}

struct TProps {
  1: i32 id;
  2: TPropsMeta meta;
  3: list<TPropsPricing> pricing;
  4: list<TPropsChannel> channels;
}

struct TAddPropsAndUseInfo {
  1: TYyInfo recvYyInfo;//收礼人信息，交友会设置为主持人uid，可能不是实际的收礼uid
  2: i32 propId;//礼物id
  3: i32 count;//数量
  4: string expand;//送礼相关的服务端标记，例如displayType
  5: string seqId;//单条送礼的seqId
  6: i64 targetUid;//送礼对象uid
}

struct TPropsMetaAndSinglePricing {
  1: i32 id;
  2: TPropsMeta meta;			// 道具元信息
  3: TPropsPricing pricing;		// 道具价格信息
}
