namespace java com.yy.hd.api.thrift.turnover

enum TAppId {
  Finance = 1,
  Dating = 2,   // 交友
  Hundred = 3,
  FreeShow = 4,
  GameGuild = 5,
  Ktv = 6,
  Blackjack = 7,
  Spy = 8,
  SlaveSales = 9,
  ScratchOff = 10,
  <PERSON><PERSON>u = 11,
  MedicalTreatment = 12,
  Sport = 13,
  VipPk = 14,
  HelloApp = 15,
  FinanceForceRelieveContract = 16,
  GameSpot = 17,
  Bilin = 18,
  XunHuan = 19,
  WeiFang = 20,
  TinyTime = 21,
  YoMall = 22,
  GameTemplate = 23,
  MEPlus = 24,
  WerewolfKill = 25,
  TinyVideo = 26,
  MGameVoice = 27,
  DianHu = 28,
  ZhuiDu = 29,
  ZhuiYa = 30,
  Findyou = 31,
  Nearby = 33,
  PeopleGame = 34,  // 语音房
  DatingHuabaRepay = 35,
  Baby = 36,
  PeiwanPaidan = 37,
  Baidu = 38,
  GameLive = 39,
  Demo = 40,
  BaiduTieba = 41,
  HaokanShipin = 42,
  QuanminXiaoshipin = 43,
  YYLive = 44,
  <PERSON>ji<PERSON><PERSON> = 45,
  YYChargeCenter = 46,
  Bdgassist = 47,
  PC = 48,
  FanZhiShi = 49,
  BaiduLite = 50,
  BaiduHealth = 51,
  ZfbApplets = 52,
  BaiduXR = 53,
  BaiduBZ = 55,
  BaiduBigPrint = 56,
  Peiwan = 57,
  Yaya = 58,
  DatingYYLiveWithdraw = 20001,
  DatingYYLiveWithdrawV3 = 20002,
  ShengDongWithdraw = 190000,
  ShengLangWithdraw = 190001,
  ZhuiWanGiftbagWithdraw = 300001,
  ZhuiWanActWithdraw = 300002,
  ZhuiWanGunKingWithdraw = 300003,
  ZhuiWanGunKingV2Withdraw = 300004,
  ZhuiWanYYFWithdraw = 3401,
  SDKCourseWithdraw = 3901
}

enum TCurrencyType {
  Virt = 1,
  Real = 2,
  Activity = 3,
  Yb = 4,
  Time = 5,
  Commission = 6,
  Sycee = 7,
  Golden = 8,
  Silver = 9,
  Copper = 10,
  RMB = 11,
  SilverShell = 12,
  Hello_Golden = 13,
  Hello_Diamond = 14,
  Hello_AppleDiamond = 15,
  Hello_RedDiamond = 16,
  SuperPurpleDiamond = 17,
  RedPacket = 18,
  Xh_Golden = 19,
  Xh_Diamond = 20,
  Xh_Ruby = 21,
  Bilin_Whale = 22,
  Bilin_Profit = 23,
  TinyTime_MiBi = 24,
  TinyTime_MiDou = 25,
  TinyTime_Profit = 26,
  TinyTime_EDou = 27,
  YoMall_Salary = 28,
  ME_Midas_Mibi = 29,
  GameTemplate_Diamond = 30,
  GameTemplate_Ruby = 31,
  YYLive_RedDiamond = 32,
  HappyCoin = 33,
  HappyDiamond = 34,
  MGameDiamond = 35,
  MGameBlackCoin = 36,
  AlipayRedPacket = 37,
  AlipayRedPacketFreeze = 38,
  HappyDrill = 39,
  Bilin_Whale_NEW = 40,
  Xh_Diamond_NEW = 41,
  MGameDiamond_NEW = 42,
  MGamePinkDiamond = 43,
  MGameVoicePikoDiamond = 44,
  Peiwan_GoldenShell = 45,
  Zhuidu_Diamond = 46,
  Zhuidu_Ruby = 47,
  ZhuiYa_Diamond = 48,
  ZhuiYa_Ruby = 49,
  Findyou_Gold = 50,
  Findyou_Profit = 51,
  Dating_ChickenTeammate = 54,
  NearbyCoin = 55,
  NearbyProfit = 56,
  ChallengeTicket = 57,
  MakingAddtionCard = 61,
  Zhuiwan_Gold = 62,
  Zhuiwan_SettleGold = 63,
  ShengLang_Diamond = 64,
  Dating_PurpleGoldCoin = 65,
  Vippk_ChickenTeammate = 66,
  Baby_ChickenTeammate = 67,
  GoldenShellTicket = 68, // 语音房金钻
  DatingModou = 90001,    // 交友-魔豆
  ZhuiYin_GoldDiamond = 69,
  Peiwan_YellowDiamond = 70,
  GameLive_Ruby = 71,
  GameLive_Profit = 72,
  GameLive_Activity = 73,
  GameLive_Commission = 74,
  BlueCrystal = 75,
  FZS_Diamond = 76,
  FZS_Profit = 77,
  Dating_ActivityVirt = 78,
  TB_tdou = 100,
  period_Virt = 2000001,
  specific_Virt = 3000001,
  coupon_Virt = 5000001
}

enum UsedChannelType {
  Client = 0,
  Web = 10000,
  IOS = 10001,
  Android = 10002,
  IOS_Cracked = 10003,
  WechatOfficialAccount = 10004,
  IOS_Subscribe = 10005,
  Android_Subscribe = 10006,
  IOS_IM = 10007,
  Android_IM = 10008,
  IOS_h5 = 10009,
  Android_h5 = 10010,
  Web_SDK_ENT = 10020,
  PC_SDK = 10021,
  PC_SDK_ENT = 10022,
  SDK_LIVE_IOS = 10023,
  SDK_LIVE_IOS_ENT = 10024,
  SDK_LIVE_Android = 10025,
  SDK_LIVE_Android_ENT = 10026,
  Web_SDK_GAME = 10030,
  TurnoverWeb = 1,
  DatingCom = 2,
  YLPhone = 3,
  YLServer = 4,
  BaiduTieba = 5,
  FinanceApp = 6,
  DatingAppIOS = 7,
  DatingAppAndroid = 8,
  YyLoveAppIOS = 9,
  YyLoveAppAndroid = 10,
  YyShiTingAppAndroid = 11,
  YyShiTingAppIOS = 12,
  FinanceSC = 13,
  DatingAppIOSCracked = 14,
  YyLoveAppIOSCracked = 15,
  MEXiaomi = 16,
  MEGameStore = 17,
  MEBaiduBrowser = 18,
  MEBilin = 19,
  DatingBlindIOS = 20,
  DatingBlindAndroid = 21,
  VipPeiLiaoAndroid = 22,
  VipPeiLiaoIOS = 23,
  MEMidas = 24,
  MEBaiduPic = 25,
  MEJD = 26,
  MEEmulatorApp = 27,
  MEKuaikan = 28,
  YYLiveIOS = 29,
  YYLiveAndroid = 30,
  FinancePinAnAndroid = 31,
  FinancePinAnIOS = 32,
  YouXiDaTing = 33,
  WolfKillDatingIOS = 34,
  WolfKillBindingIOS = 35,
  WolfKillDatingAndroid = 36,
  WolfKillBindingAndroid = 37,
  VipNianNianIOS = 38,
  VipNianNianAndroid = 39,
  WolfKillPKIOS = 40,
  WolfKillPKAndroid = 41,
  WolfKillImIOS = 42,
  WolfKillImAndroid = 43,
  WolfKillPKGameIOS = 44,
  WolfKillPKGameAndroid = 45,
  WolfkillXiaochengxu = 46,
  WolfkillExternal = 47,
  XunhuanPkIOS = 48,
  XunhuanPkAndroid = 49,
  VipPkMobileLiveIOS = 50,
  VipPkMobileLiveAndroid = 51,
  KaiXinDouVoiceRoomIOS = 52,
  KaiXinDouVoiceRoomAndroid = 53,
  XunhuanKXDIOS = 54,
  XunhuanKXDAndroid = 55,
  XunhuanKXDDatingIOS = 56,
  XunhuanKXDDatingAndroid = 57,
  XunhuanKXDPKIOS = 58,
  XunhuanKXDPKAndroid = 59,
  XunhuanKXDOriginalIOS = 60,
  XunhuanKXDOriginalAndroid = 61,
  VipPkNewLiveIOS = 62,
  VipPkNewLiveAndroid = 63,
  YYLiveXiaomiIOS = 64,
  YYLiveXiaomiAndroid = 65,
  MobileGameVoicePikoIOS = 66,
  MobileGameVoicePikoAndroid = 67,
  XunhuanMatchRoomIOS = 68,
  XunhuanMatchRoomAndroid = 69,
  PeiwanIOS = 70,
  PeiwanAndroid = 71,
  ZhuiDuIOS = 72,
  ZhuiDuAndroid = 73,
  BilinMeQqAndroid = 74,
  BilinMeVideoIOS = 75,
  BilinMeVideoAndroid = 76,
  ZhuiYaIOS = 77,
  ZhuiYaAndroid = 78,
  ZhuiYaIMIOS = 79,
  ZhuiYaIMAndroid = 80,
  XunhuanPC = 81,
  XunhuanDatingPC = 82,
  XunhuanPkPC = 83,
  XunhuanIMIOS = 84,
  XunhuanIMAndroid = 85,
  SDAppIOS = 86,
  SDAppAndroid = 87,
  SDDatingIOS = 88,
  SDDatingAndroid = 89,
  SDPkIOS = 90,
  SDPkAndroid = 91,
  SDMatchRoomIOS = 92,
  SDMatchRoomAndroid = 93,
  SDImIOS = 94,
  SDImAndroid = 95,
  ZhuiYaIOSWechatAccount = 96,
  ZhuiYaAndroidWechatAccount = 97,
  MeIOSWhitelistCharge = 98,
  MeAndroidWhitelistCharge = 99,
  ZhuiKanIOS = 100,
  ZhuiKanAndroid = 101,
  SLAppIOS = 110,
  SLAppAndroid = 111,
  SLDatingIOS = 112,
  SLDatingAndroid = 113,
  SLPkIOS = 114,
  SLPkAndroid = 115,
  SLMatchRoomIOS = 116,
  SLMatchRoomAndroid = 117,
  SLImIOS = 118,
  SLImAndroid = 119,
  ZhuiWanConsumeYb = 120,
  ZhuiWanTransYb = 121,
  H5Recharge = 1871,
  WebOutSideSys = 1872,
  ZhuiYinIOS = 130,
  ZhuiYinAndroid = 131,
  ZhuiYinPC = 132,
  ZhuiWanYYFIOS = 133,
  ZhuiWanYYFAndroid = 134,
  GameLiveIOS = 135,
  GameLiveAndroid = 136,
  PeiwanBaiduIOS = 137,
  PeiwanBaiduAndroid = 138,
  ZhuiWanIOSWechatAccount = 139,
  ZhuiWanAndroidWechatAccount = 140,
  YYWalletIOS = 141,
  YYWalletAndroid = 142,
  BaiduIOS = 143,
  BaiduAndroid = 144,
  BaiduTiebaIOS = 145,
  BaiduTiebaAndroid = 146,
  HaokanShipinIOS = 147,
  HaokanShipinAndroid = 148,
  QuanminXiaoshipinIOS = 149,
  QuanminXiaoshipinAndroid = 150,
  BaiduGameAssistIOS = 151,
  BaiduGameAssistAndroid = 152,
  BaijiahaoIOS = 153,
  BaijiahaoAndroid = 154,
  YYLiveVoiceIOS = 155,
  YYLiveVoiceAndroid = 156,
  YomiJYIOS = 157,
  YomiJYAndroid = 158,
  BaiduProgramIOS = 159,
  BaiduProgramAndroid = 160,
  YYLiveIOSV2 = 161,
  YYLiveAndroidV2 = 162,
  BaiduIOS_SDK_ENT = 163,
  BaiduAndroid_SDK_ENT = 164,
  BaiduTiebaIOS_SDK_ENT = 165,
  BaiduTiebaAndroid_SDK_ENT = 166,
  HaokanShipinIOS_SDK_ENT = 167,
  HaokanShipinAndroid_SDK_ENT = 168,
  QuanminXiaoshipinIOS_SDK_ENT = 169,
  QuanminXiaoshipinAndroid_SDK_ENT = 170,
  YomiYYFIOS = 171,
  YomiYYFAndroid = 172,
  YomiBabyIOS = 173,
  YomiBabyAndroid = 174,
  BaiduIOS_SDK_GAME = 183,
  BaiduAndroid_SDK_GAME = 184,
  BaiduTiebaIOS_SDK_GAME = 185,
  BaiduTiebaAndroid_SDK_GAME = 186,
  HaokanShipinIOS_SDK_GAME = 187,
  HaokanShipinAndroid_SDK_GAME = 188,
  QuanminXiaoshipinIOS_SDK_GAME = 189,
  QuanminXiaoshipinAndroid_SDK_GAME = 190,
  YYFH5 = 191,
  YYFWEB = 192,
  BaiduLiteIOS_SDK_ENT = 193,
  BaiduLiteAndroid_SDK_ENT = 194,
  BaiduLiteIOS_SDK_GAME = 195,
  BaiduLiteAndroid_SDK_GAME = 196,
  YomiYYFIOS_BLACK = 197,
  YomiYYFAndroid_BLACK = 198,
  YYWEBSITE = 199,
  FanzhishiScene1 = 8000301,
  FanzhishiScene2 = 8000302,
  FanzhishiScene3 = 8000307,
  FanzhishiScene4 = 8000308,
  FanzhishiScene5 = 8000321,
  FanzhishiScene6 = 8000322
}

enum TPropsType {
  Activity = 1,
  Premium = 2,
  TipsPack = 3,
  Tutor = 4,
  GiftBag = 5,
  ExternalCurrency = 6,
  VipNiuNiu = 7,
  PropsPieces = 8,
  OneTimeProp = 9,
  Free = 10,
  Meme = 11,
  GameProps = 12,
  Random = 13,
  Box = 14,
  Exclusive = 15,
  Special = 16,
  Turntable = 17
}

enum TAccountOperateType {
  Withdraw = 1,
  Exchange = 2,
  ConsumeProps = 3,
  PropsRevenue = 4,
  BuyVirtOverage = 5,
  BuyVirtOverageFail = 6,
  ActivityAutoInc = 7,
  AutoMonthSettle = 8,
  AccountFreeze = 9,
  AccountUnfreeze = 10,
  ChannelRealToPersonVirt = 11,
  IssueSycee = 12,
  Transfer = 13,
  SystemOper = 14,
  SystemCompensate = 15,
  ExternalModification = 16,
  GiftBagLottery = 17,
  ChargeCurrency = 18,
  ChargeCurrencyPresent = 19,
  ChargeCurrencyDiscount = 20,
  DatingBackupGroup = 21,
  DatingBackupGroupBackFee = 22,
  DatingBackupGroupFinish = 23,
  BuySkin = 24,
  BuySeal = 25,
  BuySealBackFee = 26,
  RedPacketIssue = 27,
  RedPacketCharge = 28,
  RedPacketGrab = 29,
  RedPacketClose = 30,
  NobleOpen = 31,
  NobleRenew = 32,
  NobleUpgrade = 33,
  UPGRADE_PROPS = 34,
  REVERT_PAY = 35,
  WithdrawBack = 36,
  NobleRenewExchangeVirt = 37,
  OfficialIssue = 38,
  PayVipRoom = 39,
  ConsumePropsForOther = 40,
  VirtLottery = 41,
  BuySpoofHanging = 42,
  LuckyTreasures = 43,
  ProductConsume = 44,
  ProductConsumeRevert = 45,
  BuyLotteryChance = 46,
  HatKing = 47,
  GuardOpen = 48,
  GuardRenew = 49,
  ModifyGuardIntimateAccount = 50,
  ModifyHatKingPond = 51,
  HatKingReward = 52,
  PropsExchange = 53,
  ComboBonus = 54,
  ExtraAccountEntry = 55,
  RedPacketExpireBack = 56,
  NobleDowngrade = 57,
  ActRevenueSubsidy = 58,
  ImmediateWithdraw = 59,
  Clear = 60,
  AuctionAward = 61,
  DragonBall = 62,
  NobleRebate = 63,
  ActivityAward = 64,
  ActivityConsume = 65,
  ExchangeFreeHeartToActivity = 66,
  ManualWithdraw = 67,
  Issue = 68,
  BuyChallengeTicket = 69,
  ConsumeChallengeTicket = 70,
  HuabaDraw = 71,
  HuabaRepay = 72,
  OfficialChargeIssue = 73,
  HuabaManualRepay = 74,
  PayExternalMarketPromotion = 75,
  RevenueBonus = 76,
  ChargeAddAdditionCard = 77,
  ConsumeAnchorMotivation = 78,
  AgencyChargeCurrency = 79,
  VipRebate = 80,
  ChickenTeammateExchange = 81,
  ExchangeSmallHeartToActivity = 82,
  ConsumeGoldenShellTicket = 83,
  ViolatePunish = 84,
  Commission = 85,
  TingMgrRevenue = 86,
  CommissionSupply = 87,
  ViolatePunishRebate = 88
}

struct TYyInfo
{
    1: required i64 uid;
    2: required i64 yyId;
    3: string yyName;
    4: i32 usedChannel; // 送礼者渠道号，客户端传0；送礼人信息传入即可
}

exception TServiceException {
  1: i32 code;
  2: string message;
  3: string expand;
}

service TPingService {
  i64 ping(1: i64 seq);
  void ping2();
}