namespace java com.yy.hd.api.thrift.turnover.finance.revenue

include "../../../../../hd_external_turnover_finance_common_api/src/main/thrift/turnover/turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId

service TRevenueServiceV2 {
  /**
   * 统计用户在指定日期范围内的日结收入
   * @param appIds 业务ID列表 必填，2交友，14约战
   * @param uid 用户uid
   * @param startDay 开始日期(精确到毫秒，非日期部分填0)
   * @param endDay 结束日期(精确到毫秒，非日期部分填0)
   * @return <业务ID, 日结收入统计(单位厘)>
   */
  map<i32, double> sumRevenueRecByDayRange(1: list<i32> appIds, 2: i64 uid, 4: i64 startDay, 5: i64 endDay) throws (1: TServiceException ex1);

  /**
     * 统计频道在指定日期范围内的有收入主持数
     * @param appId 业务ID列表 必填，2交友，14约战
     * @param sids 频道id
     * @param startTime 开始日期(精确到毫秒)
     * @param endTime 结束日期(精确到毫秒)
     * @return <业务ID, 日结收入统计(单位厘)>
     */
  map<i64, i32> batchGetRevenueAnchorCountByDay(1: i32 appid, 2: list<i64> sids, 3: i64 startTime, 4: i64 endTime, 5: i64 minIncome, 6: list<i32> srcTypes);

   /**
    * 统计频道在指定日期范围内的日结收入 @李启华
    * @param appid
    * @param sids
    * @param startTime 开始日期(到毫秒，闭区间，>=)
    * @param endTime 结束日期(到毫秒，开区间，<)
    * @param srcTypes 结算类型
    * @param excludeSrcTypes 排除结算类型
    * @return <sid, 日结收入统计(单位厘)>
    */
   map<i64, double> sumSidRevenueRecByDayRange(1: TAppId appid, 2: list<i64> sids, 3: i64 startTime, 4: i64 endTime, 5: list<i32> srcTypes, 6: list<i32> excludeSrcTypes) throws (1: TServiceException ex1);
}
