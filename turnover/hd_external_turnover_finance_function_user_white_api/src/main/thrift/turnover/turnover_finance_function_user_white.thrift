namespace java com.yy.hd.api.thrift.turnover.finance.function.user.white

include "../../../../../hd_external_turnover_finance_common_api/src/main/thrift/turnover/turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException

// TFunctionUserWhiteService 专用结构体
struct TFunctionUserWhiteList {
  1: i64 id;
  2: i32 appid;
  3: string funcName;
  4: i64 startTime;
  5: i64 endTime;
  6: i32 status;
  7: string targetId;
  8: i64 addTime;
  9: string expand;     //数据
  10: i64 operatorUid;  //操作人
}

service TFunctionUserWhiteService{
  //查询白名单
  //functionName 发送白名单取值: "dating_send_real_white_uid", 接收白名单取值: "dating_recv_real_white_uid"
  //appid = 2
  //uidList 查询uid列表
  //yyList  查询yy号列表
  //addTimeFrom  查询加入日期开始,格式yyyymmdd(包含这天)
  //addTimeTo	 查询加入日期结束,格式yyyymmdd(不包含这天)
  list<TFunctionUserWhiteList> getFunctionUserWhiteList(1: string functionName, 2: i32 appid, 3: list<i64> uidList, 4: list<i64> yyList, 5: string addTimeFrom, 6: string addTimeTo);

  //添加白名单
  //targetId 主持uid
  //return 1.成功 其他.失败
  i32 addFunctionUserWhiteList(1: string functionName, 2: i32 appid, 3: i64 targetId, 4: string expand, 5:i64 operatorUid);

  //修改白名单
  //return 1.成功 其他.失败
  i32 updateFunctionUserWhiteList(1: string functionName, 2: i32 appid, 3: i64 targetId, 4: string expand, 5:i64 operatorUid);

  //删除白名单
  //return 1.成功 其他.失败
  i32 deleteFunctionUserWhiteList(1: string functionName, 2: i32 appid, 3: i64 targetId, 4:i64 operatorUid);

  //用户是否在白名单内
  //return map<用户uid, 在否>
  map<i64, bool> isUidInWhiteList(1: string functionName, 2: i32 appid, 3: list<i64> uidList);
}
