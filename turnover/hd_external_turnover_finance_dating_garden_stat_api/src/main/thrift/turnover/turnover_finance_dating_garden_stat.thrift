namespace java com.yy.hd.api.thrift.turnover.finance.dating.garden.stat

include "../../../../../hd_external_turnover_finance_common_api/src/main/thrift/turnover/turnover_finance_common.thrift"

typedef turnover_finance_common.TServiceException TServiceException
typedef turnover_finance_common.TAppId TAppId

// TDatingGardenStatService 专用结构体
struct TDatingGardenDailyStat {
  1: i64 id;
  2: i64 dt; // 统计日期，如：1695571200000表示2023-09-25
  3: i32 appid;
  4: i64 uid; // 庄主UID
  5: i32 weixinTotal; // 包裹礼包微信支付购买数
  6: i32 alipayTotal; // 包裹礼包支付宝购买数
  7: i32 total; // 礼包购买总数（weixinTotal + alipayTotal + weixinTotalVirt + alipayTotalVirt + weixinTotalYb + alipayTotalYb）
  8: i64 createTime;
  9: i32 weixinTotalVirt; //紫水晶券礼包微信支付购买数
  10: i32 alipayTotalVirt; //紫水晶券礼包支付宝购买数
  11: i32 weixinTotalYb; //YB礼包微信支付购买数
  12: i32 alipayTotalYb;//YB礼包支付宝购买数
  13: i32 gardenType;
  14: i32 weixinQrcodeTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  15: i32 weixinWapTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  16: i32 alipayQrcodeTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  17: i32 alipayWapTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  18: i32 alipayH5miniTotalPack; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  19: i32 weixinQrcodeTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  20: i32 weixinWapTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  21: i32 alipayQrcodeTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  22: i32 alipayWapTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  23: i32 alipayH5miniTotalVirt; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  24: i32 weixinQrcodeTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  25: i32 weixinWapTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  26: i32 alipayQrcodeTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  27: i32 alipayWapTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
  28: i32 alipayH5miniTotalYb; //weixin、alipay：支付方式，qrcode、wap、H5mini：扫码方式，pack：包裹礼包，virt：紫水晶礼包，yb：yb礼包
}

struct TDatingGardenDailyStatPageResult {
  1: i32 page; // 当前页号
  2: i32 pagesize; // 每页大小
  3: i32 total; // 总条数
  4: i32 totalPage; // 总页数
  5: list<TDatingGardenDailyStat> contents;
}

service TDatingGardenStatService {
  /**
   * 查询假日庄园流水统计
   * @param appId 2：交友
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @param uid 庄主uid（可选传0）
   * @param page 页号，从1开始
   * @param pagesize 每页大小
   */
  TDatingGardenDailyStatPageResult queryDatingGardenDailyStats(1: TAppId appid, 2: i64 startDate, 3: i64 endDate, 4: i64 uid, 5: i32 page, 6: i32 pagesize, 7: i32 gardenType) throws (1: TServiceException ex1);
}
