namespace java com.yy.hd.api.thrift.turnover.service

include "../../../../../hd_external_turnover_base_api/src/main/thrift/turnover/turnover_common.thrift"
include "../../../../../hd_external_turnover_props_api/src/main/thrift/turnover/turnover_props.thrift"

typedef turnover_common.TServiceException TServiceException
typedef turnover_common.TCurrencyType TCurrencyType
typedef turnover_common.TAppId TAppId
typedef turnover_common.UsedChannelType UsedChannelType
typedef turnover_common.TYyInfo TYyInfo
typedef turnover_props.TChannelAccount TChannelAccount
typedef turnover_props.TPropsMetaAndSinglePricing TPropsMetaAndSinglePricing
typedef turnover_props.TAddPropsAndUseInfo TAddPropsAndUseInfo

struct TUserAccount {
  1: i64 uid;
  2: TCurrencyType currencyType;
  3: i64 amount;
  4: i64 freezed;
  5: i32 appid;
}

struct TExchangeCurrencyResult {
  1: i64 uid;
  2: TAppId appid;
  3: TCurrencyType srcCurrencyType;
  4: TCurrencyType destCurrencyType;
  5: i32 result;
  6: TUserAccount srcAccount;
  7: TUserAccount destAccount;
  8: i64 exchangeAmount;
  9: TChannelAccount srcChannelAccount;
}

service TTurnoverService {
    void ping2();

    /**
     * # 营收 - 外部货币冻结查询
     * ## 枚举
     * ### 渠道枚举

     * 交友（appid = 2）

     * channel | code
     * ---|---
     * pc客户端 | 0
     * web端 | 2
     * 交友app-IOS | 9
     * 交友app-Android| 10
     * 手Y交友IOS | 29
     * 手Y交友Android | 30
     * 追玩-IOS | 77
     * 追玩-Android| 78
     * 追玩-IM-IOS | 79
     * 追玩-IM-Android| 80

     * 约战（appid = 14）

     * channel | code
     * ---|---
     * pc客户端 | 0
     * 视听android端 | 11
     * 视听ios端| 12
     * 手Yios端| 29
     * 手Yandroid端 | 30
     * 手机直播ios| 50
     * 手机直播android | 51
     * 视听PK-IOS| 62
     * 视听PK-Android | 63
     * Web端 | 10000
     * IOS端 | 10001
     * Android端 | 10002

     * ### 外部货币枚举
     * currencyType | code
     * ---|---
     * 交友-魔豆 |90001
     * 交友-派对积分 | 90004
     * 约战-龙珠 | 90005
     * 后续外部系统有需要可以新增
     **/
     bool isExternalUserAccountFrozen(1: i64 uid, 2: i32 appid, 3: i32 currencyType, 4: i32 channelType) throws (1: TServiceException ex1);

   /**
     * 为用户发放金币
     *
     * @param uid 用户的uid
     * @param activityId 活动的id号 (填656)
     * @param currencyType 货币类型 (金币填62)
     * @param amount 发放金币的数量 (每1000个金币=1元)
     * @param appid 业务类型 (追玩填 ZhuiYa 30)
     * @param seqId 由调用方传入的流水号，用来区别唯一的一次调用，多次调用传同一个seqId只会写一次记录
     * @param expand 扩展json字段，可以填 {"usedChannel":xxx}
     */
	i32 issueCurrencyNew(1: i64 uid, 2: i64 activityId, 3: TCurrencyType currencyType, 4: i64 amount, 5: TAppId appid, 6: string seqId, 7: string expand) throws (1: TServiceException ex1);

    /**
     * 获取用户账户余额
     * @param uid 用户的uid
     * @param appid 业务方标识 (填 ZhuiYa,30)
     * @param currencyType 货币类型 (填 Zhuiwan_Gold,62)
     * @return userAccount 用户账户余额( "amount":金币数 )
     */
	TUserAccount getUserAccountByUidAndType(1: i64 uid, 2: TAppId appid, 3: TCurrencyType currencyType);

    /**
     * 清空用户账户
     *
     * @param 同上
     * @return 1.成功  0.失败(帐户不存在或余额已为0)  (其他错误抛异常)
     */
	i32 clearUserAccountByUidAndType(1: i64 uid, 2: TAppId appid, 3: TCurrencyType currencyType) throws (1: TServiceException ex1);

	/**
      * 货币转换(金币转提现货币,1:1转)
      * @param uid 用户的uid
      * @param appid 业务方标识 (填 ZhuiYa,30)
      * @param amount 需要转的金币数 (1000金币=1元)
      * @param srcCurrencyType 源货币类型 (填 Zhuiwan_Gold,62)
      * @param destCurrencyType 目标货币类型 (填 Zhuiwan_SettleGold,63)
      * @param userIp IP地址
      * @param usedChannel 渠道id(知道就填实际值, 不知道填0)
      * @param configId 填0
      * @param expand (可不填)
      * @return TExchangeCurrencyResult (result=1成功,其他失败)
      */
    TExchangeCurrencyResult exchangeUserCurrency(1: i64 uid, 2: TAppId appid, 3: i64 amount, 4: TCurrencyType srcCurrencyType, 5: TCurrencyType destCurrencyType, 6: string userIp, 7: UsedChannelType usedChannel, 8: i64 configId, 9: string expand) throws (1: TServiceException ex1);

    /**
     * 发放活动道具接口
     *
     * @param uid 用户
     * @param propId 道具id
     * @param count 数量
     * @param addSeqId 由调用方传入的流水号，用来区别唯一的一次调用，多次调用传同一个seqId只会写一次记录
     * @param appid 业务id，交友业务为 TAppId.Dating
     * @param addType 发放的类型：1-激活码，2-活动发放，3-商城购买
     * @param expand 扩展字段
     * @param sid 发放道具时所在的顶级频道
     * @param ssid 发放道具时所在的子频道
     * @param activityId 活动id号
     * @return  * 返回码：
     * 				0 失败
     * 				1 成功
     * 				2 超出限额
     * @throws ServiceException
     * 			   错误码：
     *             <ul>
     *             <li>-1: 参数错误</li>
     *             <li>-22: 账户不存在</li>
     * 			   <li>-405: 订单序号已存在</li>
     * 			   <li>-500: 服务端出错</li>
     *             <li>-600: 该活动不存在</li>
     *             </ul>
     */
    i32 addPropsActivity(1:  i64 uid, 2:  i32 propId, 3:  i32 count, 4:  string addSeqId,
    		5:  TAppId appid, 6:  string expand, 7:  i64 sid, 8:  i64 ssid, 9:  i32 addType, 10:  i64 activityId) throws (1: TServiceException ex1);

   /**
   * 发放道具并送出使用接口
   *
   * @param usedYyInfo 送礼人信息
   * @param propId 道具id
   * @param count 数量
   * @param addSeqId 由调用方传入的流水号，用来区别唯一的一次调用，多次调用传同一个seqId只会写一次记录
   * @param appid 业务id
   * @param addType 填2即可
   * @param expand 扩展字段
   * @param sid 发放道具时所在的顶级频道
   * @param ssid 发放道具时所在的子频道
   * @param activityId 活动id号
   * @param recvYyInfo 主持人/分成者信息
   * @param isBroadcastPropsUsedMessage 是否发送广播，填true即可
   * @param clientExpand 客户端透传的expand字段
   * @return  * 返回码：
   * 				0 失败
   * 				1 成功
   * 				2 超出限额
   * @throws ServiceException
   * 			   错误码：
   *             <ul>
   *             <li>-1: 参数错误</li>
   *             <li>-22: 账户不存在</li>
   * 			   <li>-405: 订单序号已存在</li>
   * 			   <li>-500: 服务端出错</li>
   *             <li>-600: 该活动不存在</li>
   *             </ul>
   */
  i32 addPropsActivityAndUsedPropsByYyInfo(1: TYyInfo usedYyInfo, 2: i32 propId, 3: i32 count, 4: string addSeqId, 5: TAppId appid, 6: string expand, 7: i64 sid, 8: i64 ssid, 9: i32 addType, 10: i64 activityId, 11: TYyInfo recvYyInfo, 12: bool isBroadcastPropsUsedMessage, 13: string clientExpand) throws (1: TServiceException ex1);

  /**
   *
   * 批量发放道具并使用道具(桃花签全麦专用)
   * @param listJson 由[{"recvYyInfo":"", "propId":20000, "count":1, "clientExpand":"", "seqId",""}] 组成的json
   * @return 0.失败  1.成功
   */
  i32 batchAddPropsActivityAndUsedPropsByYyInfo(1: string listJson, 2: TYyInfo usedYyInfo, 3: string addSeqId, 4: TAppId appid, 5: string expand, 6: i64 sid, 7: i64 ssid, 8: i32 addType, 9: i64 activityId, 10: bool isBroadcastPropsUsedMessage, 11: string clientExpand) throws (1: TServiceException ex1);


  /**
   * @param seqId 请求流水号，目前该接口设计为可以重试
   * @param usedYyInfo 送礼人信息
   * @param addPropsInfos 送礼相关信息
   * @param clientExpandMap;//送礼人送礼给收礼人，客户端生成的expand,key为收礼人uid
   * @param expand 客户端送多人传的一个通用的expand
   */
   i32 batchAddPropsActivityAndUsedPropsByYyInfoV2(1: TAppId appid, 2: string seqId, 3: TYyInfo usedYyInfo, 4: list<TAddPropsAndUseInfo> addPropsInfos, 5: map<i64,string> clientExpandMap, 6: i64 sid, 7: i64 ssid, 8: i64 activityId, 9: string expand);

    /**
     * 获取业务所有礼物及对应的单个价格接口
     *
     * @param appid 业务id，固定填 TAppId.Dating
     * @param scode 固定填 "datingHaveRevenueNotYYlive"
     * @return  礼物列表
     */
    list<TPropsMetaAndSinglePricing> getSinglePropsPricingInSpecifiedWayWithAllProps(1: TAppId appId, 2: string scode);

}