syntax = "proto3";
import "common.proto";
package com.yy.hd.api.pb.game.common;
option java_outer_classname = "Game";

// 玩法关键信息，上行
message KeyInfo {
  int32 room_type                    = 1; // 房间类型 参考 common.proto 中的 RoomType 枚举 1-交友 2-语音房
  int64 server_time                  = 2; // 服务器当前时间，单位：秒
  int32 game_type                    = 3; // 玩法顶级类型
  int32 status                       = 4; // 游戏状态,0-游戏不存在，当前没有开启游戏, 1-游戏进行中, 2-惩罚中, 3-游戏已结束
  int64 start_time                   = 5; // 本场游戏开始时间，单位：秒
  int64 duration                     = 6; // 本场游戏时长，单位为：秒， <= 0 表示不限制时长
  int64 remain_duration              = 7; // 本场游戏剩余时长,单位：秒 = duration - (server_time - start_time)
  MatchedInfo matched_info           = 8; // 匹配到的对手信息，多人视频乱斗才有
  repeated GuestInfo guest_info      = 9; // 嘉宾列表，嘉宾信息 team 区分队伍
  repeated WeaponInfo weapon_info    = 10; // pk战队武器信息，团战、乱斗类型玩法需要, 区分队伍
  PunishmentResult punishment_result = 11; // 开局惩罚信息
  int64 session_id                   = 12; // 游戏session id

  int64 compere_onoff_time           = 20; // 主持上下线时间戳，单位：秒
  int32 game_result                  = 21; // 玩法pk结果，比如多人视频团战&乱斗： 0-平局 1-team0胜利 2-team1胜利
}

// 嘉宾扩展信息（装扮、声波颜色【语音房特有-432 批量获取UID在后台配置的声波颜色】）
// 语音房：6188 658 获取装扮信息， 6188 3001 更新装扮信息
// 交友房：yyftsprivilege.proto KGetUserAvatarFrameReq 请求， KUserAvatarFrameBroadcast 广播
message GuestExtInfo {
  string url              = 1; // 头像框素材地址
  string svga_url         = 2; // 头像框 svga 特效素材地址
  repeated string colors  = 3; // [语音房特有] 直播间音波颜色
}

// 嘉宾信息（游戏参与嘉宾）
message GuestInfo {
  int32 position                    = 1; // 座位号从 1 开始
  int32 team                        = 2; // 所属队伍，多人视频 不区分队伍， 团战和乱斗区分：1-橙队 2-蓝队
  int64 sid                         = 3; // 所在顶级频道
  int64 ssid                        = 4; // 所在子频道

  int64 uid                         = 5; // 用户uid
  com.yy.hd.api.pb.Sex sex          = 6; // 性别
  string nick                       = 7; // 用户昵称，客户端上行接口时候这里会填充，广播的时候请使用多昵称字段 nickExt
  string nick_ext                   = 8; // 用户多昵称，下行广播才需要
  string avatar                     = 9; // 用户头像,使用业务头像
  string avatar_ext                 = 10; // 多hostName头像 下行广播才需要，map<hostName, avatar>, 客户端取数逻辑：avatar_ext.getOrDefault('curAppHostName', avatar_ext.getOrDefault('generalAvatar', 'APP端本地写死的默认用户头像'))

  bool mvp                          = 11; // 是否是mvp用户，多人视频团战，多人视频乱斗有这个概念
  int64 amount                      = 12;  // 本场累计礼物数值 PC端紫水晶，手Y端红钻
  int32 dead_status                 = 13;  // 1, 死亡告警， 2，真的死了。
  int32 user_tag                    = 14; // 高低分标识:0--无标识, 1--皇冠标识, 2--哭脸标识
  TitleInfo title_info              = 15;  // 头衔信息
  int32 ar_mix_status               = 16; // 0--未定义  1--支持  2--不支持AR特效合到视频流
  string city                       = 17; // 嘉宾所在城市
  string province                   = 18; // 嘉宾所在省份/自治区/直辖市

  repeated ContributorInfo contributor_info = 19; // 送礼榜单

  GuestExtInfo ext_info             = 20; // 嘉宾扩展信息（装扮、声波颜色【语音房特有】）

  int32 ui_pos                      = 21; // 客户端UI展示使用 座位区按ui_pos显示 目前仅多人乱斗需要特殊处理 其他同position一致
  string ui_team_color              = 22; // 客户端UI展示使用（便于客户端扩展复用） 队伍主题颜色，空表示无颜色（普通多人视频），orange-橙队（乱斗：本方频道，团战：橙队），blue-蓝队（乱斗：对方，团战：蓝队），只做ui展示逻辑
}

// 头衔信息，仅移动端使用？
message TitleInfo {
  int64 cap_level   = 1; // 头衔等级
  string cap_ar     = 2; // AR效果
  string cap_rc     = 3; // 头衔资源
}

// 送礼榜
message ContributorInfo {
  int64 uid          = 1; // 贡献用户uid
  string avatar      = 2; // 贡献用户头像
  string avatar_ext  = 3; // 多hostName头像 下行广播才需要，map<hostName, avatar>, 客户端取数逻辑：avatar_ext.getOrDefault('curAppHostName', avatar_ext.getOrDefault('generalAvatar', 'APP端本地写死的默认用户头像'))
  int64 amount       = 4; // 贡献礼物价值
  string nick        = 5; // 昵称
  string nick_ext    = 6; // 用户多昵称，下行广播才需要
  int64  noble_grade = 7; // 交友贵族等级
}

// 对手信息
message MatchedInfo {
  int64 sid           = 1; // 匹配的频道
  int64 ssid          = 2; // 匹配的子频道
  int64 uid           = 3; // 匹配的主持
  int64 asid          = 4; // 当前短位频道
  int32 winning       = 5; // 胜利状态, 0 平   1, 胜, 2 败
  int64 offline_time  = 6; // 主持下线时间戳
  string nick         = 7; // 昵称
  string nick_ext     = 8; // 用户多昵称，下行广播才需要
  string avatar       = 9; // 头像
  string avatar_ext   = 10; // 多hostName头像 下行广播才需要，map<hostName, avatar>, 客户端取数逻辑：avatar_ext.getOrDefault('curAppHostName', avatar_ext.getOrDefault('generalAvatar', 'APP端本地写死的默认用户头像'))
}

// pk战队武器信息，团战、乱斗类型玩法需要
message WeaponInfo {
  int32 team            = 1; // 所属队伍，多人视频 不区分队伍， 团战和乱斗区分：1-橙队 2-蓝队
  int32 level           = 2; // 战队等级
  string icon_type      = 3; // 战队武器
  int64 amount          = 4; // 礼物价值，PC端紫水晶，手Y端红钻
  int64 level_amount    = 5; // 升级到下一级所需数值，满级时为0
  int32 level_percent   = 6; // 升级百分比， 0〜100
  int64 sid             = 7; // 所属sid
  int64 ssid            = 8; // 所属ssid
}

// 惩罚的内容项
message PunishmentInfo {
  string title  = 1;
  string icon   = 2;
  string desc   = 3; // 描述
  sint32 idx    = 4; // 惩罚id, 通过GetPunishmentCfgReq拉取惩罚配置
}

// 惩罚结果信息
message PunishmentResult {
  int32 result                            = 1; // 当前惩罚结果, -1 未设置或转出结果
  repeated PunishmentInfo punishment_info = 2; // 惩罚的信息
  int64 uid                               = 3; // 选择惩罚人的uid (mvp或不存在)
  int64 end_time                          = 4; // 惩罚结束时间戳
  int32 result_idx                        = 5; // 惩罚的idx, 选择或自动选择时有值，为0时不出惩罚效果
  int64 auto_set_punish_time              = 6; // 自动设置惩时间戳
}

// 惩罚配置信息
message PunishmentCfg {
  sint32 idx         = 1; //惩罚id
  string title       = 2;
  string icon        = 3;
  string desc        = 4;
  sint32 effect_type = 5; //类型。 1：ar  2：变声
  string ar_url      = 6; // 移动端 AR svgad动画地址    .svga
  string effect_url  = 7; // 动画地址
  sint32 cd_time     = 8; //播放动画的时间 单位秒
  sint32 sound_type  = 9; //声音类型，1 大叔叫，2 萝莉音，3 搞怪音
  string img_url     = 10; // 静态图地址
  string ar_effect   = 11; // pc AR 视频流 svgad动画资源地址   .7z
  string ar_effect_mob = 12; // 移动端 AR svgad动画地址   .zip
}

// 贡献榜用户信息
message ContributorRankInfo {
  int32 rank    = 1;  // 排名
  int64 uid     = 2;  // 贡献者uid
  int64 amount  = 3;  // 贡献礼物价值
  string avatar = 4;  // 头像
  string avatar_ext = 5; // 多hostName头像 下行广播才需要，map<hostName, avatar>, 客户端取数逻辑：avatar_ext.getOrDefault('curAppHostName', avatar_ext.getOrDefault('generalAvatar', 'APP端本地写死的默认用户头像'))
  string nick   = 6;  // 昵称
  string nick_ext = 7; // 用户多昵称，下行广播才需要
}

// 匹配战队信息
message BattleTeamInfo {
  int64 uid = 1;  // 主持的uid 发起邀请的
  string avatar = 2;  // 主持的头像
  string avatar_ext = 3;  // 主持的头像
  string nick = 4;  // 主持昵称
  string nick_ext = 5;  // 主持多昵称
  int64 charm = 6;
  int64 asid = 7;
  int64 sid = 8;
  int64 ssid = 9;
  int64 end_time = 10; // 结束时间
  int64 imid = 11; // yy号
  int32 compere_tier = 12; // 主持的段位等级
  int32 compere_stars = 13; // 主持段位星星
  repeated BattleTeamGuestInfo team_guest_info = 14; // 主持的战队信息，只显示魅力值最多2个嘉宾
}

// 主持的战队信息，只显示魅力值最多2个嘉宾
message BattleTeamGuestInfo {
  sint64 uid = 1;            //嘉宾的uid
  string avatar = 2;         //嘉宾的头像
  string avatar_ext = 3;     //多hostName头像 下行广播才需要，map<hostName, avatar>, 客户端取数逻辑：avatar_ext.getOrDefault('curAppHostName', avatar_ext.getOrDefault('generalAvatar', 'APP端本地写死的默认用户头像'))
}

// 对战游戏信息
message BattleGameInfo {
  int32 gameType = 1; // 匹配玩法类型
  BattleTeamInfo team1 = 2;
  BattleTeamInfo team2 = 3;
  int32 hot = 4;   // 值为1的时候显示火热特效 双方魅力值大于 10000RMB的时候设置
}

enum DeadStatus {
  DEAD_NONE = 0; // 未定义
  DEAD_WARN = 1; // 死亡告警
  DEAD_REAL = 2; // 真的死了
}

enum Status {
  STATUS_NONE = 0; // 未开始或不存在
  ACT_ING = 1; // 活动进行中
  PUNISH_ING = 2; // 惩罚中
  END = 3; // 活动结束
}

enum WinResult {
  DRAW = 0; // 平局
  ORANGE_Win = 1; // 橙队胜利
  BLUE_WIN = 2; // 蓝队胜利
}

enum UserTag {
  NONE_TAG = 0; // 无标识
  RC = 1; // 皇冠标识
  CRY = 2; // 哭脸标识
}

enum ArMixStatus {
  AR_NONE = 0; // 未定义
  SUPPORT = 1; // 支持
  NOT_SUPPORT = 2; // 不支持AR特效合到视频流
}