syntax = "proto3";
import "common.proto";
package com.yy.hd.api.pb.game.match.common;
option java_outer_classname = "GameMatch";

// 匹配推荐项
message RecommendItem {
  int64  uid       = 1; // 房间主持uid
  int64  imid      = 2;
  string nick      = 3;
  int64  sid       = 4;
  int64  ssid      = 5;
  int32  game_type = 6; // 玩法类型
}

// 邀请信息
message InviteInfo {
  int64 uid               = 1; // 邀请方uid
  int64 sid               = 2; // 请求用户当前所在频道 sid
  int64 ssid              = 3; // 请求用户当前所在频道 ssid
  int32 match_game_type   = 4; // 邀请进行什么玩法匹配
  int64 match_uid         = 5; // 对方uid
  int64 match_sid         = 6; // 对方sid
  int64 match_ssid        = 7; // 对方ssid
  repeated int64 uid_list = 8; // 邀请对战时所选择的嘉宾uid列表(邀请方)
}