syntax = "proto3";
package com.yy.hd.api.pb.room;
option java_outer_classname = "RoomCommon";

import "common.proto";

message RoomInfo {
  int64 sid = 1;        // 频道sid, 长号
  int64 ssid = 2;       // 频道ssid, 顶级频道和sid一样
  int64 asid = 3;       // 频道短号
  string room_name = 4;  // 房间名
  string room_logo = 5;  // 房间图
  int64 room_id = 6; //房间号
  int32 room_op_type = 7; //房间经营属性 1-家族房间 2-ugc房间
  int64 ugc_live_uid = 8; //ugc直播uid
  bool ugc_live_switch = 9; //ugc直播开关
  int32 videoMode = 10; // 视频模式 0-无 1-主持位
}

// 废弃
enum LayoutType {
  Layout_None = 0; // 无

  Layout_Party                       = 30; // 语音房派对
  Layout_Party_Video                 = 3001; // 语音房派对-视频
  Layout_Ktv                         = 31; // 语音房Ktv
  Layout_MiniGame                    = 32; // 语音房小游戏
  Layout_TeamFight                   = 33; // 语音房团战
  Layout_TeamFight_Video             = 3301; // 语音房团战-视频
  Layout_Pk                          = 34; // 语音房跨厅pk
  Layout_Pk_Video                    = 3401; // 语音房跨厅pk-视频
  Layout_UgcGameLive                 = 35; // ugc赛事直播

}

message LayoutConfig {
  int32 game_type = 1;
  int32 video_mode = 2;
  int32 room_op_type = 3;
}

message CompereInfo {
  int64 uid = 1;        // uid
  string nick = 2;      // 昵称
  string avatar = 3;    // 头像
  Sex sex = 4;          // 性别
  int32 pos = 5;        // 主持位置, 默认0
  string nickExt = 6;   // 多昵称, 广播时, 移动端取多昵称里的
  string avatarExt = 7; // 多头像, 广播时, 移动端取多头像里的
  DatingCompereExt dating_ext = 8;
}

message DatingCompereExt {
  int64 status = 1; // 0--座位显示或可用  1--座位不显示或不可用
  int64 sign_ch = 2; // 签约频道
  LBSInfo lbs = 3; // 多人类玩时下发主持城市信息
  LevelInfo levelInfo = 4; // 主持等级
}

message LevelInfo{
  int64 level = 1;                // 主持等级
  int64 current_score = 2;        // 用户当前积分
  int64 total_score = 3;          // 升级需要总积分
  int64 current_growth  = 4;      // 当前成长值
  int64 total_growth = 5;         // 升级需要总的成长值
  string level_desc = 6;          // 主持称号
  string level_icon = 7;          // 主持勋章url
  int32 medal_level = 8;          // 勋章等级
  int64 sum_total_score = 9;      // 累积总积分
  int64 sum_total_growth = 10;   // 累积总成长值
}

message LBSInfo {
  string lbs_city = 1;
  double lat = 2;
  double lng = 3;
  string lbs_province = 4;
}

// 背景信息
message BgInfo {
  string url             = 1; // 背景地址
  string color           = 2; // 背景颜色
  int64  revision        = 3; // 更新的版本号，用于刷新cdn及缓存资源
  int64  theme_id        = 4; // 当前模板主题id
  string gradient_color  = 5; // 渐变颜色
  string rank_color      = 6; // 榜单颜色
  string channel_bg_url  = 7; // 纯色底图（默认背景时有值）
  string game_bg_url     = 8; // 中间背景图（默认背景时有值）
  string gift_area_color = 9; // 礼物区域颜色
}