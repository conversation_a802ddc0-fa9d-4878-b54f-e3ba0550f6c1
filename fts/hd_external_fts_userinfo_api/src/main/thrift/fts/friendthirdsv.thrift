namespace java com.yy.hd.api.thrift.ftsfriend.thirdsv

struct TDatingIntent
{
    1:string mynick, //我是一个
    2:string looking, //我想找
    3:string todo //一起....
}

struct TMakingFriendsIndex
{
  1:i32 godindex, //男女神指数
  2:i32 charmindex, //魅力指数
  3:i32 attract, //吸引指数
  4:i32 present //身价指数
}

enum TReviewStatus
{
    kReviewStatusOk = 0,  //审核ok。
    kReviewStatusPreview = 1, //先发后审
    kReviewStatusPending = 2, //审核中。
    kReviewStatusRejected = 3, //审核不通过。
    kReviewStatusNone = 4 //尚未提交审核。
}

struct TPhotoInfo
{
    1:string url,
    2:TReviewStatus review_status, //当采用先发后审时，未审核完成时，该字段不存在。
    3:i32 photo_width, //图片的宽度
    4:i32 photo_height, //图片的高度
    5:bool hd_photo //是否高清图片
}

struct TLBSInfo
{
    1:optional string city;
    2:optional double lat;
    3:optional double lng;
}

struct TPersonalInfo
{
    1:i32 height, //身高
    2:i32 weight, //体重
    3:string dating_motto, //交友宣言
    4:TDatingIntent dating_intent, //交友意向
    5:list<string> tag, //标签
    6:list<TPhotoInfo> photo, //照片的url
    7:list<string> goodat, //擅长
    8:list<string> interest, //兴趣
    9:string sexual_orientation, //性取向
    10:i64 uid, //uid
    11:TPhotoInfo avatar_info, //用户头像
    12:string nick, //YY的昵称
    13:i32 sex, //YY的性别, 0(female)、1(male)
    14:string birthday, //YY的生日
    15:i32 country, //YY的国家
    16:i32 province, //YY的省
    17:i32 city, //YY的市
    18:string sign, //签名
    19:i32 charm, //魅力值
    20:i32 hi_count, //招呼数
    21:i32 assit, //赞数 用户没有填写资料时.
    22:TMakingFriendsIndex making_friends_index, //交友指数
    23:i32 information_compelete_index, //交友资料完成度 100进制表示
    24:string lbs_city, //lbs定位的城市，动态变化。 为了兼容应当使用新的值
    25:string fake_name,  //用户匿名的名称
    26:string fake_avatar_info, //用户匿名头像
	27:optional TLBSInfo lbs_info,		//寻欢分离资料卡 专用字段
	28:optional i64 latest_login_time,  //最后登录时间[Unix时间戳] 寻欢分离资料卡 专用字段
}

struct TPersonalInfoRet
{
  1: i32 ret, //返回码：0（成功）、1（不存在）、－1（系统错误）。
  2: TPersonalInfo personal_info //用户个人信息。
}

struct PersonalInfo
{
    1:string nick,        //昵称
    2:string avatar,      //头像---hgame4*3头像
    3:i32 online_status,  //在线状态，1在线，0下线
    4:i64 sid,            //频道号
    5:i64 ssid,           //子频道号
    6:i64 count,          //频道人数
}

service FriendThirdSv{
  /**
   * 测试链接
   */
  void ping();


  /**
   * 批量获取用户信息，目前只取昵称。
   * @param uids, 待查询的用户uid。
   * @return uid和用户信息的map。
   */
  map<i64, TPersonalInfo> BatchGetPersonalInfo(1:list<i64> uids);

  /**
   * 批量获取用户信息 接入敏感词版本
   * @param moduleId 1002/1003 敏感词库
   * @param uids, 待查询的用户uid。
   * @return uid和用户信息的map。
   */
  map<i64, TPersonalInfo> BatchGetPersonalInfoSWV(1:i64 moduleId, 2:list<i64> uids);
  
  /**
   * 批量获取用户信息。 缓存版 5秒缓存
   * @param uids, 待查询的用户uid。
   * @return uid和用户信息的map。
   */
  map<i64, TPersonalInfo> BatchGetPersonalInfoWithCache(1:list<i64> uids);

   /**
     * 根据用户传入type字段批量获取用户信息，目前只取昵称。
     * list<string> types 查询的属性字段
     * @param uids, 待查询的用户uid。
     * @return uid和用户信息的map。
  */
  map<i64, TPersonalInfo> BatchGetPersonalInfoByTypes(1:list<i64> uids, 2:list<string> types);

}
