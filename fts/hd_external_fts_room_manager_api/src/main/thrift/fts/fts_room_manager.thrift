namespace java com.yy.hd.api.thrift.fts.roommanager

struct GetAntiPoachingListReq
{

}

struct AntiPoachingInfo
{
    1:i64            sid;
    2:i64            ssid;
    3:bool           isEffects;    // 是否选中玩法特效  true-勾选 false-未勾选
    4:bool           isBroadcast;  // 是否选中玩法广播  true-勾选 false-未勾选
    5:bool           isBillboard;  // 是否选中频道榜单  true-勾选 false-未勾选
}

struct GetAntiPoachingListResp
{
    1:i32                      ret,     // 0--成功  1或其他--错误
    2:string                   msg,     // 出错时的信息
    3:list<AntiPoachingInfo>   retList; // 厅防挖列表
}

service FtsRoomManagerService
{
    void ping();

    // 获取防挖列表(全量)
    GetAntiPoachingListResp GetAntiPoachingList(1:GetAntiPoachingListReq req);
}
