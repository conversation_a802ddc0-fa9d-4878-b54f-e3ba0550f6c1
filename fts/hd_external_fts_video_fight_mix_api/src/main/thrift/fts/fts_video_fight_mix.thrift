include "common.thrift"
namespace cpp fts.videofightmix // fts_video_fight 进程
namespace java com.yy.hd.api.thrift.ftsvideofightmix

// 主要实现新旧多人视频乱斗互通
// s2sname: fts_video_fight_mix_test, fts_video_fight_mix

// pk团队信息
struct PkTeamInfo {
    1:i64 uid;      // 主持uid
    2:i64 sid;      // 频道
    3:i64 ssid;     // 子频道
    4:i32 gameType; // 乱斗匹配前玩法类型
}

// 玩法关键信息
struct KeyInfo {
  1: i32 room_type,                      // 房间类型 参考 common.proto 中的 RoomType 枚举 1-交友 2-语音房
  2: i64 server_time,                    // 服务器当前时间，单位：秒
  3: i32 game_type,                      // 玩法顶级类型
  4: i32 status,                         // 游戏状态,0-游戏不存在，当前没有开启游戏, 1-游戏进行中, 2-惩罚中, 3-游戏已结束
  5: i64 start_time,                     // 本场游戏开始时间，单位：秒
  6: i64 duration,                       // 本场游戏时长，单位为：秒， <= 0 表示不限制时长
  7: i64 remain_duration,                // 本场游戏剩余时长,单位：秒 = duration - (server_time - start_time)
  8: MatchedInfo matched_info,           // 匹配到的对手信息，多人视频乱斗才有
  9: list<GuestInfo> guest_info,         // 嘉宾列表，嘉宾信息 team 区分队伍
  10: list<WeaponInfo> weapon_info,      // pk战队武器信息，团战、乱斗类型玩法需要, 区分队伍
  11: PunishmentResult punishment_result,// 开局惩罚信息
  20: i64 compare_onoff_time,            // 主持上下线时间戳，单位：秒
  21: i32 game_result                    // 玩法pk结果，比如多人视频团战&乱斗： 0-平局 1-team0胜利 2-team1胜利
}

// 嘉宾信息（游戏参与嘉宾）
struct GuestInfo {
  1: i32 position,                      // 座位号从 1 开始
  2: i32 team,                          // 所属队伍，多人视频 不区分队伍，团战和乱斗区分：1-橙队 2-蓝队
  3: i64 sid,                           // 所在顶级频道
  4: i64 ssid,                          // 所在子频道
  5: i64 uid,                           // 用户uid
  6: i32 sex,                           // 性别
  7: string nick,                       // 用户昵称
  8: string nick_ext,                   // 用户多昵称
  9: string avatar,                     // 用户头像
  10: bool mvp,                         // 是否是mvp用户
  11: i64 amount,                       // 本场累计礼物数值
  12: i32 dead_status,                  // 死亡状态
  13: i32 user_tag,                     // 高低分标识
  14: TitleInfo title_info,             // 头衔信息
  15: i32 ar_mix_status,                // AR特效支持状态
  16: string city,                      // 嘉宾所在城市
  17: string province,                  // 嘉宾所在省份/自治区/直辖市
  18: list<ContributorInfo> contributor_info // 送礼榜单
}

// 头衔信息
struct TitleInfo {
  1: i64 cap_level,  // 头衔等级
  2: string cap_ar,  // AR效果
  3: string cap_rc   // 头衔资源
}

// 送礼榜
struct ContributorInfo {
  1: i64 uid,       // 贡献用户uid
  2: string avatar  // 贡献用户头像
}

// 对手信息
struct MatchedInfo {
  1: i64 sid,            // 匹配的频道
  2: i64 ssid,           // 匹配的子频道
  3: i64 uid,            // 匹配的主持
  4: i64 asid,           // 当前短位频道
  5: i32 winning,        // 胜利状态
  6: i64 offline_time,   // 主持下线时间戳
  7: string nick,        // 昵称
  8: string nick_ext,    // 用户多昵称
  9: string avatar       // 头像
}

// pk战队武器信息
struct WeaponInfo {
  1: i32 team,          // 所属队伍
  2: i32 level,         // 战队等级
  3: string icon_type,  // 战队武器
  4: i64 amount,        // 礼物价值
  5: i64 level_amount,  // 升级到下一级所需数值
  6: i32 level_percent  // 升级百分比
  7: i64 sid,           // 所属频道
  8: i64 ssid,          // 所属子频道
}

// 惩罚的内容项
struct PunishmentInfo {
  1: string title,  // 惩罚标题
  2: string icon,   // 惩罚图标
  3: string desc,   // 描述
  4: i32 idx        // 惩罚id
}

// 惩罚结果信息
struct PunishmentResult {
  1: i32 result,                       // 当前惩罚结果
  2: list<PunishmentInfo> punishment_info, // 惩罚的信息
  3: i64 uid,                          // 选择惩罚人的uid
  4: i64 end_time,                     // 惩罚结束时间戳
  5: i32 result_idx,                   // 惩罚的idx
  6: i64 auto_set_punish_time          // 自动设置惩时间戳
}

// 惩罚配置信息
struct PunishmentCfg {
  1: i32 idx,           // 惩罚id
  2: string title,      // 惩罚标题
  3: string icon,       // 惩罚图标
  4: string desc,       // 描述
  5: i32 effect_type,   // 类型
  6: string ar_url,     // 移动端 AR svgad动画地址
  7: string effect_url, // 动画地址
  8: i32 cd_time,       // 播放动画的时间
  9: i32 sound_type,    // 声音类型
  10: string img_url,   // 静态图地址
  11: string ar_effect, // pc AR 视频流 svgad动画资源地址
  12: string ar_effect_m// 移动端 AR svgad动画地址
}

// 贡献榜用户信息
struct ContributorRankInfo {
  1: i32 rank,        // 排名
  2: i64 uid,         // 贡献者uid
  3: i64 amount,      // 贡献礼物价值
  4: string avatar,   // 头像
  5: string nick,     // 昵称
  6: string nick_ext  // 用户多昵称
}

// 主持的战队信息，只显示魅力值最多2个嘉宾
struct VideoFightTeamGuestInfo {
    1:i64 uid;            // 嘉宾的uid
    2:string avatar;      // 嘉宾的头像
}

// 匹配战况
struct VideoFightTeamInfo {
    1:i64 uid;          // 主持的uid
    2:string avatar;    // 主持的头像
    3:string nick;
    4:i64 charm;
    5:i64 asid;
    6:i64 sid;
    7:i64 ssid;
    8:i64 end_time;     // 结束时间
    9:i64 imid;         // yy号
    10:i32 compere_tier;    // 主持的段位等级
    11:i32 compere_stars;   // 主持段位星星
    12:list<VideoFightTeamGuestInfo> team_guest_info;   // 主持的战队信息，只显示魅力值最多2个嘉宾
}

// 实时战况
// 与原乱斗团战展示的规则一致
struct VideoFightActivityInfo {
    1:VideoFightTeamInfo team1;
    2:VideoFightTeamInfo team2;
    3:i32 hot;   // 值为1的时候显示火热特效 双方魅力值大于 10000RMB的时候设置
}

// 匹配成功广播
struct VideoFightMatchBroadcast {
    1:VideoFightTeamInfo team1;
    2:VideoFightTeamInfo team2;
    3:i64 countdown; // 底部栏倒计时
}

service FtsVideoFightMixService
{
    // ping
    void ping();

    // 再来一场 发起邀请后置通知 - hd-game 发起
    // @param sessionId 场次id
    // @param inviter 邀请方频道信息
    // @param invitee 被邀请方频道信息
    void applyStartNewPkNotice(1:string seqId, 2:i64 sessionId, 3:PkTeamInfo inviter, 4:PkTeamInfo invitee)

    // 再来一场 邀请结果处理后置通知 - hd-game 发起
    // @param sessionId 场次id
    // @param inviter 邀请方频道信息
    // @param invitee 被邀请方频道信息
    // @param accept 是否同意再来一场
    // @param countDown 匹配成功后底部栏倒计时（秒）
    void confirmStartNewPkNotice(1:string seqId, 2:i64 sessionId, 3:PkTeamInfo inviter, 4:PkTeamInfo invitee, 5:bool accept, 6:i64 countDown)

    // 发送投降频道广播
    // @param bcSid 要收广播的sid
    // @param bcSsid 要收广播的ssid
    // @param giveUpUid 发起投降的uid
    // @param giveUpSid 发起投降的sid
    // @param giveUpSsid 发起投降的ssid
    void sendGiveUpBroadcast(1:string seqId, 2:i64 sessionId, 3:i64 bcSid, 4:i64 bcSsid 5:i64 giveUpUid, 6:i64 giveUpSid 7:i64 giveUpSsid)

    // 开始乱斗匹配通知
    // @param inviter 邀请方频道&主持
    // @param invitee 对方频道&主持
    // @param inviterGuestUidList 邀请方嘉宾
    // @param inviteeGuestUidList 被邀请方嘉宾
    // @param countDown 匹配成功后底部栏倒计时（秒）
    void startVideoFightNotice(1:string seqId, 2:i64 sessionId, 3:PkTeamInfo inviter, 4:list<i64> inviterGuestUidList, 5:PkTeamInfo invitee, 6:list<i64> inviteeGuestUidList, 7:i64 countDown)

    // 停止乱斗处理结果通知
    // @param inviter 发起停止乱斗的频道主持
    // @param invitee 对方
    void stopVideoFightNotice(1:string seqId, 2:i64 sessionId, 3:PkTeamInfo inviter, 4:PkTeamInfo invitee)

    // 广播跨频道音频uid
    void broadcastCrossChannelVoiceUID(1:string seqId, 2:i64 sessionId, 3:i64 sid, 4:i64 ssid, 5:list<i64> uidList)

    // 广播玩法关键信息
    void broadcastKeyInfo(1:string seqId, 2:i64 sessionId, 3:i64 sid, 4:i64 ssid, 5:KeyInfo keyInfo)

    // 获取旧多人视频乱斗实战状况取列表
    list<VideoFightActivityInfo> getVideoFightActivityList(1:string seqId, 2:i64 uid, 3:i64 sid, 4:i64 ssid, 5:i32 size);

    // 发玩法匹配成功广播通知
    void broadcastVideoFightMatch(1:string seqId, 2:i64 sid, 3:i64 ssid, 4:VideoFightMatchBroadcast notice);

    // 发送胜利结果频道广播
    // @param bcSid 广播sid
    // @param bcSsid 广播ssid
    // @param winningResult 相对于 ssid 的胜利结果
    void sendWinningInfoBroadcast(1:string seqId, 2:i64 sessionId, 3:i64 bcSid, 4:i64 bcSsid, 5:i32 winningResult);

    // 给对方主持发送断开连麦申请单播通知
    void sendDisconnectNotice(1:string seqId, 2:i64 sessionId, 3:i64 bcSid, 4:i64 bcSsid, 5:i64 uid);

    // 给对方主持发送断开连麦单播通知
    void sendDisconnectConfirmNotice(1:string seqId, 2:i64 sessionId, 3:i64 bcSid, 4:i64 bcSsid, 5:i64 uid, 6:bool confirm);

    // 给频道发送断开连麦频道广播
    void sendDisconnectConfirmBroadcast(1:string seqId, 2:i64 sessionId, 3:i64 bcSid, 4:i64 bcSsid);

    // 发送邀请加时通知单播（A向B发起加时，给B发送通知单播，通知B确认）
    // @param duration 加时时长：秒
    void sendAddDurationNotice(1:string seqId, 2:i64 sessionId, 3:i64 bcSid, 4:i64 bcSsid, 5:i64 uid, 6:i32 duration)

    // 发送邀请加时通知单播（A向B发起加时，B收到单播，B进行确认，再给A发送结果单播）
    // @param duration 加时时长：秒
    void sendAddDurationConfirmNotice(1:string seqId, 2:i64 sessionId, 3:i64 bcSid, 4:i64 bcSsid, 5:i64 uid, 6:bool accept, 7:i32 duration)
}
