namespace java com.yy.hd.api.thrift.ftsanticheat

/*
* 交友反作弊
 */

enum ResCode {
    SysErr = -1,
    Ok = 0,
    Exception = 1,
    PermissionDenied = 2 // 没有权限
    AccountNotRealName = 3 // 帐号没有实名
    AccountNotSign = 4 // 帐号没有签约
}

struct TResponse{
    1: ResCode code;
    2: string msg;
}

struct TResponseInt{
    1: i32 code;
    2: string msg;
}

//被锁定的设备的信息
struct DeviceInfo {
	1: i64 uid (go.tag='json:"-" bson:"uid"')                                                   //设备锁定人
	2: string device  (go.tag='json:"id" bson:"_id"')                                              //设备唯一编号
	3: string last_login_ip  (go.tag='json:"last_ip" bson:"last_login_ip"')                      //上次登陆时间
	4: i64 last_login_time (go.tag='json:"last_time" bson:"last_login_time"')                 //上次登陆时间 秒
}

//开播锁定信息
//主持签约1个月内，无法在官方频道以外的非签约频道签约
//当绑定设备时，无法在非绑定的设备上开播
struct LockInfo {
	1: i64 uid   (go.tag='json:"uid" bson:"_id"')          //主持uid
	2: i64 sid   (go.tag='json:"sid" bson:"sid"')          //签约的频道
	3: i64 start  (go.tag='json:"start" bson:"start"')           //锁定开始时间 秒
	4: i64 stop  (go.tag='json:"end" bson:"end"')          //锁定结束时间 秒
	5: i64 contract_time (go.tag='json:"contract_time" bson:"-"')  //签约时间 秒
	6: list<DeviceInfo> devices  (go.tag='json:"devices" bson:"-"')       //绑定的设备
	7: list<i64> sids  (go.tag='json:"sids" bson:"sids"')       //绑定的允许开播的频道
}

struct TGetLockInfoRet{
    1: ResCode code;
    2: list<LockInfo> lock_info_list;
    3: string err_msg;
}

enum Dimension
{
	ALL      = 0x0,     // 默认选取全部维度
	ANTI_SDK = 0x1,     // 实时外挂防刷检测
	PERSONA  = 0x2,     // 离线用户画像
	IP       = 0x4,     // IP画像
	DEVICE   = 0x8,     // 设备画像
	PHONE    = 0x10     // 手机号码画像
	EMAIL    = 0x20     // 邮箱
}

// 用户终端类型
enum TermType
{
	ALL      = 0,
	PC       = 1,
	ANDROID  = 2,
	IOS      = 3,
	WEB      = 4,
	INVALID  = 255
}

struct RiskQueryItem
{
	1: string uid,              // 业务内部系统所用的用户唯一标识
	2: i16 termType = 255,      // 用户终端类型 (请根据TermType选取有效值)
	3: string ip,               // 用户出口IP (选填)
	4: string device,           // 用户设备标识 (选填)
	5: string phone,            // 用户电话号码 (选填)
	6: string email,            // 用户邮箱 (选填)
	//7: AssociatedAccountType assocAcctType = 255, // 用户登陆所使用的第三方登陆平台类型(选填)
	//8: string assocAcctId,      // 第三方账号平台返回的账号唯一标识 (如微信/QQ平台返回的openId,选填)
	9: string extension         // 额外提供的参数以json的形式在此字段提供，目前支持的关键字详情参见文档(选填)
}

struct RiskQueryRspItem
{
	1: string uid,
	2: i32 riskLevel,   // 风险等级，0 - 100
	3: string detail,    // 评分细则和其他信息(json)
	//4: AssociatedAccountType assocAcctType, // 如请求时使用关联账号 assocAcctType/assocAcctId 会填请求时相应的值
	//5: string assocAcctId
}

struct RiskLevelQueryRet
{
	1: i16 code,         // 返回码: 0 表示成功，非0表示不成功；当不成功时 msg 字段携带错误详情
	2: string desc,      // 错误信息(仅在返回码不为0时有效)
	3: list<RiskQueryRspItem> items  // 查询结果
}

struct CheckLiveLimitReq
{
    1:i64       uid,
    2:i64       sid,
    3:i64       ssid,
    4:i32       ip,
    5:i32       platform,
    6:string    systemInfo
    7:i64       gameType,  //玩法类型
    8:i32       onlineType, // 多人视频的连麦类型：0--Video   1--Audio
}

struct VideoDatingConfig {
    1: i64 sid          // 频道
    2: i64 ssid         // 子频道
    3: i32 antiHopping  // 防跳槽
    4: i32 voiceLink    // 音频连线
    5: i64 addTime      // 配置增加时间
    6: i64 updateTime   // 白名单增加时间
    7: i64 opUid        // 最后操作的Uid
}

struct doVideoDatingLiveConfigReq
{
    1: VideoDatingConfig config
    3: i32 opType // 1--增加 2--删除 3--修改
}

struct doVideoDatingLiveConfigResp
{
    1: i32 ret, // 0 -- ok 1--failure
    2: string msg,
}

// 查多个,规则：sid_ssid
struct queryVideoDatingLiveConfigReq
{
    1: list<string> idList //
}

struct queryVideoDatingLiveConfigResp
{
    1: i32 ret,
    2: string msg,
    3: list<VideoDatingConfig> confList;
}

struct pkLimitReq {
    1:i64 uid,      // 待查或待更新的主播uid
    2:i64 sid,      // 频道
    3:i64 ssid,     // 子频道
    4:i64 appID,    // 1--交友 2--宝贝  3--约战  4--语音房
    5:i32 gameType, // 如有就填
    6:string seqID  // 更新次数时用来去重
    7:i64 reqUid    // 谁发起的请求
}

struct pkLimitResp {
    1:i32 ret    //  0--ok  1--PK次数用完
    2:string msg //
}

struct batchCheckPkLimitReq {
     1:i64 appID,           // 1--交友 2--宝贝  3--约战  4--语音房
     2:list<i64> uidList    // key:uid  value: 剩余次数，-1--无限制，0--无次数  大于0--剩余次数
}

struct batchCheckPkLimitResp {
     1:i32 ret    //  0--ok
     2:string msg //
     3:map<i64, i32> pkCountInfo // key:uid  value: 剩余次数，-1--无限制，0--无次数  大于0--剩余次数
}

struct getVideoSidPermissionResp {
    1:i32 ret    //  0--ok
    2:string msg //
    3:map<i64, i32> sidPermission // key: sid value: 0-无视频权限 1-有视频权限
}

struct ChInfo {
    1:i64   sid;
    2:i64   ssid;
}

struct AddGameTypeBlackListReq {
    1:i32           gameType    // 玩法
    2:list<ChInfo>  chList      // 频道信息列表
    3:i64           opUid       // 增加的uid
}

struct AddGameTypeBlackListResp {
    1:i32       ret     //  0--ok
    2:string    msg     //
}

struct RemoveGameTypeBlackListReq {
    1:i32           gameType    // 玩法
    2:list<ChInfo>  chList      // 频道信息列表
}

struct RemoveGameTypeBlackListResp {
    1:i32       ret     //  0--ok
    2:string    msg     //
}

struct GetAllGameTypeBlackListReq {
    1:i32 gameType          // 玩法
}

struct GetAllGameTypeBlackListResp {
    1:i32           ret     //  0--ok
    2:string        msg     //
    3:list<ChInfo>  chList  // 频道信息
}

struct CheckGameTypeBlackListReq {
    1:i32           gameType          // 玩法
    2:list<ChInfo>  chList;
}

struct CheckGameTypeBlackListResp {
    1:i32           ret         //  0--ok
    2:string        msg         //
    3:list<ChInfo>  chList;     // 返回在白频道单的频道列表
}

struct UidWhiteInfo {
    1:i64       whiteListType;   // 白名单类型：1.视频主持黑名单  2.乱斗主持黑名单
    2:i64       uid;
    3:string    remark;         // 备注
    4:i64       opUid;          // 操作的Uid
    5:i64       updateTime      // 更新时间戳
}

struct AddUidWhiteListReq {
    1:list<UidWhiteInfo> uidWhiteList;
}


struct RemoveUidWhiteListReq {
    1:list<UidWhiteInfo> uidWhiteList;
}

struct GetUidWhiteListReq {
    1:i64       whiteListType;   // 白名单类型：1.视频主持黑名单  2.乱斗主持黑名单
}

struct GetUidWhiteListResp {
    1:i32           ret         //  0--ok
    2:string        msg         //
    3:list<UidWhiteInfo> uidWhiteList;
}

struct CheckUidWhiteListReq {
    1:list<UidWhiteInfo> uidWhiteList;
}

struct CheckUidWhiteListResp {
    1:i32           ret         //  0--ok
    2:string        msg         //
    3:list<UidWhiteInfo> uidWhiteList; // 返回在白名单的uid白名单列表
}

enum ChWhiteListType
{
    CrossChannellLive    = 1 // 多频道经营白名单
}

struct ChWhiteInfo {
    1:ChWhiteListType  whiteListType;  // 类型(必填)
    2:ChInfo           ch;             // 频道信息 子频道大于0,必须传顶级频道; 只针对顶级频道,子频道忽略
    3:string           remark;         // 备注
    4:i64              opUid;          // 操作的Uid(必填)
    5:i64              updateTime      // 更新时间戳
}

struct AddChWhiteListReq {
    1:list<ChWhiteInfo> chWhiteList;
}

struct RemoveChWhiteListReq {
    1:ChWhiteListType  whiteListType;
    2:list<ChInfo>     chList;
}

struct GetChWhiteListReq {
    1:ChWhiteListType   whiteListType;
    2:ChInfo            ch;	// 搜索参数
}

struct GetChWhiteListResp {
    1:i32           ret         //  0--ok
    2:string        msg
    3:list<ChWhiteInfo> chWhiteList;
}

struct CheckChWhiteListReq {
    1:ChWhiteListType  whiteListType;
    2:list<ChInfo>     chList;
}

struct CheckChWhiteListResp {
    1:i32           ret         //  0--ok
    2:string        msg
    3:list<ChWhiteInfo> chWhiteList; // 返回在白名单的白名单列表
}

service FtsAntiCheatSV{
    void ping();

    /**
     *   除多人外嘉宾上座权限检查
     *
    */
    TResponse checkJoinActLimit(1:CheckLiveLimitReq req);

    /**
     *   多人系列嘉宾上座权限检查
     *
    */
    TResponse checkMultiVideoLiveLimit(1:CheckLiveLimitReq req);

    /** 主持上麦权限检测 （不是所有鉴权逻辑）
          在模板内点击“我要上麦”，由fts_app进程调用本接口，查询主持的开播权限
          CheckLiveLimitReq
          @param uid 开播主持uid
          @param sid 开播频道
          @param ssid 开播子频道
          @param ip 开播者ip 当前默认从安全中心获取用户的ip，并通过秩序组接口拦截部分目标，上报ip则使用主动上报的ip(目前是ipv4版本)

          @return TResponse - enum ResCode {
                SysErr = -1,  //系统调用相关接口异常 默认允许开播
                Ok = 0,       //开播权限正常
                Exception = 1,//发现异常 默认允许开播
                PermissionDenied = 2 //系统认为主持没有开播权限 不允许开播
            }
           异常情况和不允许开播请打印msg 和uid便于定位
           不允许开播请向用户展示msg
         */
	TResponse CheckLiveLimit(1:CheckLiveLimitReq req);

    /** 获取开播锁定信息
      锁定原因：
        签约不满1月
        开播设备锁定

      @param uid 主持uid
      @param sid 开播频道
      @param skip_num 跳过主持数量
      @param limit_num 最大返回数量， 默认为10
     */
	TGetLockInfoRet GetLockInfo(1: i64 uid, 2: i64 sid, 3: i32 skip_num, 4: i32 limit_num);

	/**
	开播黑名单
	*/
	/**
	    @return TResponseInt code 0 成功，无变动
	    @return TResponseInt code 1 成功，有变动
	    @return TResponseInt code <0 失败
	    @return TResponseInt msg 错误信息
	*/
	TResponseInt AddCreateActivityBlackList(1:i64 uid);
	TResponseInt DelCreateActivityBlackList(1:i64 uid);
	//检查是否在开播黑名单中  1 是  0 不是 <0 异常
	TResponseInt CheckCreateActivityBlackList(1:i64 uid);
	list<i64> ListCreateActivityBlackListAll(); //smembers

	/**
	视频主持白名单
	*/
	/**
	    @return TResponseInt code 0 成功，无变动
	    @return TResponseInt code 1 成功，有变动
	    @return TResponseInt code <0 失败
	    @return TResponseInt msg 错误信息
	*/
	TResponseInt SetVideoPermission(1:i64 uid, 2: i64 sid);
	TResponseInt DelVideoPermission(1:i64 uid);
	//检查是否具有开播权限 和开播频道白名单关联  1 有开播权限  0 无 <0 异常
	TResponseInt CheckVideoPermission(1:i64 uid, 2: i64 sid);
	map<i64,i64> ListVideoPermission(1: list<i64> uid_list); //hmget
	map<i64,i64> ListVideoPermissionAll(); //hgetall

    // 支持查询频道视频权限接口
    getVideoSidPermissionResp GetVideoSidPermission(1:list<i64> sidList);

	/**
	视频主持黑名单

	添加到黑名单会取消白名单
	*/
	/**
	    @return TResponseInt code 0 成功，无变动
	    @return TResponseInt code 1 成功，有变动
	    @return TResponseInt code <0 失败
	    @return TResponseInt msg 错误信息
	*/
	TResponseInt AddVideoBlackList(1:i64 uid);
	TResponseInt DelVideoBlackList(1:i64 uid);
	//检查是否在视频主持黑名单中  1 是  0 不是 <0 异常
	TResponseInt CheckVideoBlackList(1:i64 uid);
	list<i64> ListVideoBlackListAll(); //smembers

    /**
     * riskLevelQuery 安全中心接口转发
     *
	3: i64 dimension,             // 查询维度 见枚举类型 Dimension
	4: list<RequestItem> items    // 支持批量查询，但请求项不得超过50个
     */
	RiskLevelQueryRet riskLevelQuery(1:list<RiskQueryItem> items, 2: i64 dimension);


    // 增加多人配置（是否支持主持收；是否防跳槽；是否允许音频连接）
    // 删除多人配置（是否支持主持收；是否防跳槽；是否允许音频连接）
    // 修改多人配置（是否支持主持收；是否防跳槽；是否允许音频连接）
    doVideoDatingLiveConfigResp doVideoDatingLiveConfig(1:doVideoDatingLiveConfigReq req)

    // 查询多人配置（是否支持主持收；是否防跳槽；是否允许音频连接）
    queryVideoDatingLiveConfigResp queryVideoDatingLiveConfig(1:queryVideoDatingLiveConfigReq req)

    // 检查是否有可用的次数
    pkLimitResp checkPKLimit(1:pkLimitReq req)
    // 更新PK次数
    pkLimitResp updatePkLimitCount(1:pkLimitReq req)
    // 批量查剩余次数
    batchCheckPkLimitResp batchCheckPkLimit(1:batchCheckPkLimitReq req)

    // 增加玩法黑名单
    AddGameTypeBlackListResp AddGameTypeBlackList(1:AddGameTypeBlackListReq req)
    // 删除玩法黑名单
    RemoveGameTypeBlackListResp RemoveGameTypeBlackList(1:RemoveGameTypeBlackListReq req)
    // 查询所有玩法黑名单
    GetAllGameTypeBlackListResp GetAllGameTypeBlackList(1:GetAllGameTypeBlackListReq req)
    // 查询是否在玩法黑名单
    CheckGameTypeBlackListResp CheckGameTypeBlackList(1:CheckGameTypeBlackListReq req)


    // 通用Uid白/黑名单接口
    TResponseInt AddUidWhiteListReq(1:AddUidWhiteListReq req);
    TResponseInt RemoveUidWhiteListReq(1:RemoveUidWhiteListReq req);
    GetUidWhiteListResp GetUidWhiteListReq(1:GetUidWhiteListReq req);
    CheckUidWhiteListResp CheckUidWhiteListReq(1:CheckUidWhiteListReq req);

    // 通用频道白/黑名单接口
    TResponseInt AddChWhiteList(1:AddChWhiteListReq req);
    TResponseInt RemoveChWhiteList(1:RemoveChWhiteListReq req);
    GetChWhiteListResp GetChWhiteList(1:GetChWhiteListReq req);
    CheckChWhiteListResp CheckChWhiteList(1:CheckChWhiteListReq req);
}
