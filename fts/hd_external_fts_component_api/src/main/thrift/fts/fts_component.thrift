namespace java com.yy.hd.api.thrift.ftscomponent

struct TTemplateStateRet
{
    1: i32 code;                     //错误码
    2: string message;               //错误原因
    3: i64 isNewTemplate;            //是否启用新模板 0-否 1-是
}

service TComponentService
{
    /**
      * 测试链接
      */
    void ping();

     /**
     * 查询指定频道是否使用新模板
     * 
     * @param sid 用户sid
     * @return TTemplateStateRet
     */
    TTemplateStateRet queryChannelTemplateState(1:i64 sid 2:i64 ssid, 3:i64 platform, 4:string bizType);

    /**
    * 触发频道内用户重新加载模板UI
    **/
    void triggerReloadTemplate(1:i64 sid, 2:i64 ssid, 3:i64 opUid)
}