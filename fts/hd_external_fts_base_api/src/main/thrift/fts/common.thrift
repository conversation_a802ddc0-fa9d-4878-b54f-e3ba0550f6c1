namespace java com.yy.hd.api.thrift.fts.common

enum Platform
{
    kPlatformPC = 1,
    kPlatformWeb = 2,
    kPlatformAndroid = 3,
    kPlatformIOS = 4,
    kPlatformAndroidPad = 5,
    kPlatformIPAD = 6,
    kPlatformWinPhone = 7,
}

//营收定义的渠道信息
enum ChannelType
{
	PcClient = 0, //客户端
	YYCOM = 1, //官网yy.com
	TIEBA = 5, //贴吧
	XH_ANDROID = 7,//寻欢Android客户端 旧版寻欢也是手机交友
	XH_IOS = 8,//寻欢IOS客户端 旧版寻欢也是手机交友
	JiaoYou_Ios = 9,//交友IOS客户端
	JiaoYou_Android = 10,//交友安卓客户端
    YYLiveIOS = 29,//yylive ios
	YYLiveAndroid = 30,//yylive android
	YYLiveXiaomiIOS = 64,// 手机YY 小米联运，IOS，交友
	YYLiveXiaomiAndroid = 65,//手机YY 小米联运，Android，交友
	ZhuiYaIOS = 77, //追吖(玩)ios
	ZhuiYaAndroid = 78,//追吖(玩)安卓
	ZhuiKanIOS = 100,//追看ios渠道
	ZhuiKanAndroid = 101,//追看安卓渠道
	YomiJYIOS = 157,
    YomiJYAndroid = 158,
	Web = 10000
}

enum Role
{
    kRoleCompere   = 1,
    kRoleGuest     = 2,
    kRoleCandidate = 3,
    kRoleUser      = 4,
    kRoleRichMan   = 21,
}

enum GrabLoveType
{
    kOrdinary = 0, //普通
    kThrowThunder = 1, //甩雷
    kTeamFight= 2, //团战
    kNewThrowThunder = 3, //新甩雷, 已经下线
    kHatKing = 4, //头衔王玩法。
    kCourtWar = 5, //厅战，已经下线
    kCardDating = 6, //曝照
    kChannelFight = 7, //乱斗匹配
    kVideoDating = 8, // 多人视频交友
    // 类型值9 已被开黑约伴使用
    kEmojiDating = 10, // 多人表情
    kSoccer = 11,  // 世界杯玩法
    kBigVideo = 12,  // 大视频相亲玩法
    kBirthday = 13, //生日玩法
    kAuction  = 14, //拍卖玩法
    kDuoFight = 15, //双人团战
}

struct ChannelId
{
    1:i64 sid,
    2:i64 ssid,
}

// 请求来源
struct OriginInfo
{
  1:Platform platform;      // 平台 Android、iOS、PC
  2:string  host_name;      // 宿主 追玩(dreamer)、手Y(yymobile)、PCYY(yypc)
  3:string  host_version;   // 宿主版本 2.9.0、2.9.0-SNAPSHOT、x.x.x
  4:string  business_type;  // 业务 交友(love)、约战（liveworld）、宝贝(mimi)、追玩自建（dreamerself）、追玩PC打通房（dreamerpc)
  5:i32     game_type;      // 玩法 grab love type 0-相亲 2-团战 7-乱斗 8-多人视频
  6:i32     sub_game_type;  // 子玩法
  7:string  hdid;           // 用户唯一标识
  8:i64     room_id;        // 语音房的房间ID
  9:string  channel_source; // app渠道来源
  10:string session_id;     // 会话id  用来标识一次进频道信息 进频道时更新(调度、切换频道，上下滑等)
}

struct PlatformInfo
{
	1:Platform platform,//当前平台。
	2:string systemInfo,////详细平台信息。
	3:ChannelType channel,  //来源渠道。
	4:i32 version; //版本号
    5:OriginInfo origin_info; //请求来源 标识
}

struct ExtInfo
{
    1:i64 uid;
    2:i64 sid;
    3:i64 ssid;
    4:string traceId;
    5:PlatformInfo platform;
    6:string ip; // ipv4
}

struct LBSInfo
{
  1: string lbs_city;
  2: double lat;
  3: double lng;
  4: string lbs_province;
}

//用户身份
enum RoleType
{
  kCompere=1, kGuest=2, kCandicate=3, kUser=4,
}

//神兽相关
struct TAnimalInfo
{
    1:i64 animal_id, //神兽ID
    2:i32 total_blood, //神兽总血量
    3:i32 blood, //神兽总血量
    4:i32 atk, //本次的攻击力
    5:i64 show_time, //显示秒数
	6:i64 delay_show_time, //延长多少秒显示
}

struct RespHeader
{
    1:i32    code;
    2:string msg;
}
