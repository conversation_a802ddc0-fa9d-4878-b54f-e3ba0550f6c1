namespace java com.yy.hd.api.thrift.fts.typeinfo

//贵族开通类型
enum TNobleOpenType
{
  kNobleNone = 0; //未开通
  kNobleOpen = 1; //开通
  kNobleRenewal = 2; //续费
  kNobleUpgrade = 3; //升级
  kNobleDowngrade = 4; //降级
}

enum TNameStatus
{
    kNameStatusOk = 0; //审核通过
    kNameStatusPending = 1; //审核中
    kNameStatusRejected = 2; //审核未通过
}

//贵族积分类型
enum TNobleAddGrowthType
{
  kPcFirstLoginReason  = 1;
  kAppFirstLoginReason = 2;
  kPcFirstGiveReason   = 3;
  kAppFirstGiveReason  = 4;
  kGiveAGiftReason     = 5;
  kRenewReason         = 6;
  kOpenKingReason      = 7;
  kOpenDukeReason      = 8;
  kOpenMarquisReason   = 9;
  kOpenEarlReason      = 10;
  kOpenViscountReason  = 11;
  kOpenBaronReason     = 12;
  kOpenKnightReason    = 13;
  kActivityReason      = 14;
  kCompensateForOldNobleReason      = 15;
  kOpenGuard = 16;
  kRenewGuard = 17;
  kCompensateForGuard = 18;
  KOpenGodReason = 19;
  KPaySeal = 20;
}


struct TExtInfo
{
    1:i64 sid  //顶级频道
    2:i64 ssid //子频道
    3:i32 platform //平台
}

struct TNobleInfo
{
    1:i32 title_id; //贵族称号ID
    2:string title_name; //贵族名称(伯爵、国王等)
    3:i64 compere_uid; //开通主持人信息
    4:string license; //机牌号
    5:string license_name; //机牌名
    6:string name; //名字(仅国王称号)
    7:TNameStatus name_status = TNameStatus.kNameStatusOk; //名字审核状态(默认通过)
    8:TNobleOpenType open_type = TNobleOpenType.kNobleNone; //开通类型(默认未开通)
    9:i64 open_time;  //开通时间
    10:i64 expiration_time; //过期时间
    11:i64 uid; //用户uid
    12:i64 sid; //顶级频道
    13:i64 ssid; //子频道
    14:string name_reason; //国王命名审核原因
    15:i32 rank; //排名，首王相关
    16:i64 enter_animation_id; //入场秀id 分别为1 2 3
    17:string enter_animation_name;//入场秀名字
    18:string enter_animation_pic;//入场秀图片
    19:i64 enter_animation_time;//入场秀展示时间间隔
    20:double score;//积分
    21:double growth;//成长值
    22:TNobleAddGrowthType reason;//成长值加成原因
    23:i32 license_type;   //机牌类型  0普通  1靓号  2靓号标识  3尊号标识
    24:i64 haoqi_level;   //豪气值等级
    25:i64 enter_animation_expire_time; //入场秀过期时间
    26:i32 sit_seat_switch; //无限上座开关 0 打开 1关掉
    27:i32 observer_switch; //上帝视角开关
    28:i32 select_lover_switch; //抢亲开关
    29:i32 rebate_month;        // 返送剩余月数
    30:i64 rebate_expire_time;  // 返送特权失效时间,unix时间戳(s)
    31:string icon90;      // 贵族图标90*90
}

struct TNobleInfoRet
{
    1:i32 ret;  //0 成功 -1 出错
    2:TNobleInfo noble_info; //贵族信息
}

struct TBatchNobleInfoRet
{
    1:i32 ret; //0 成功 -1 出错
    2:map<i64, TNobleInfo> noble_info_map;
}
