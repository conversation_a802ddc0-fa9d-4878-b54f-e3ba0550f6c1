include "typeinfo.thrift"

namespace java com.yy.hd.api.thrift.ftsnoble

service FtsNobleService {
  /**
   * 测试链接
   */
  void ping();

  /**
  * 查询用户贵族信息
  * @param uid 需要查询的用户(过期用户也会返回，需要自己判断过期逻辑)
  * @param ext_info 扩展字段(目前包含频道、子频道、平台信息, 可以传空)
  */
  typeinfo.TNobleInfoRet getNobleInfo(1:i64 uid, 2:typeinfo.TExtInfo ext_info);
  
 /**
  * 查询用户贵族信息及入场秀信息
  * @param uid 需要查询的用户(过期用户也会返回，需要自己判断过期逻辑)
  * @param ext_info 扩展字段(目前包含频道、子频道、平台信息, 可以传空)
  */
  typeinfo.TNobleInfoRet getNobleInfoAndEnterShow(1:i64 uid, 2:typeinfo.TExtInfo ext_info);
  
  /**
   * 批量查询贵族信息
   * @param uids 需要查询的用户列表
   * @param ext_info 扩展字段(目前包含频道、子频道、平台信息, 可以传空)
   * @return TBatchNobleInfoRet
   */
  typeinfo.TBatchNobleInfoRet batchGetNobleInfo(1:list<i64> uids, 2:typeinfo.TExtInfo ext_info);
  
  /**
   * 批量查询贵族信息及入场秀信息
   * @param uids 需要查询的用户列表
   * @param ext_info 扩展字段(目前包含频道、子频道、平台信息, 可以传空)
   * @return TBatchNobleInfoRet
   */
  typeinfo.TBatchNobleInfoRet batchGetNobleWithShow(1:list<i64> uids, 2:typeinfo.TExtInfo ext_info);
}


