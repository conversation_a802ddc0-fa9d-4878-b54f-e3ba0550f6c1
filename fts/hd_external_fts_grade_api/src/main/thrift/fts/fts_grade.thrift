namespace java com.yy.hd.api.thrift.grade

struct CompereLevelInfo
{
    1: i32 level //主持等级
    2: i64 current_score //用户当前积分
    3: i64 total_score //升级需要总积分
    4: i64 current_growth //当前成长值
    5: i64 total_growth //升级需要总的成长值
    6: string level_desc //主持称号
    7: string level_icon //主持勋章url
    8: i32 medal_level //勋章等级
    9:  i64 sum_total_score;  // 累积总积分
    10: i64 sum_total_growth; // 累积总成长值
}

service GradeService{
    /**
     * 批量获取主持等级积分信息
     */
    map<i64, CompereLevelInfo> BatchGetCompereLevelInfo(1:list<i64> uid);
}