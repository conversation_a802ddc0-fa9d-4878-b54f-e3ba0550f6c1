namespace cpp fts.compere.tier
namespace java com.yy.hd.api.thrift.ftscomperetier

// 段位的等级，产品需求，总共8级
enum CompereTier {
    kTier1 = 1;
    kTier2 = 2;
    kTier3 = 3;
    kTier4 = 4;
    kTier5 = 5;
    kTier6 = 6;
    kTier7 = 7;
    kTier8 = 8;
}

struct CompereTierInfo
{
    1: CompereTier tier // 段位的等级
    2: i32 stars        // 对应等级获得的星星
    3: i32 streak       // 连胜场次
}

struct CompereTierInfoRet
{
    1: i32 ret
    2: map<i64, CompereTierInfo> info
}

service FtsCompereTierService
{
    void ping();
    CompereTierInfoRet BatchGetCompereTierInfo(1: list<i64> uids);  // 主持的段位
}