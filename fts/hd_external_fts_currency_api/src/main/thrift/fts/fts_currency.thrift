namespace java com.yy.hd.api.thrift.fts.currency

enum TErrorCode {
    ERR_SUCCESS                         = 0,          //成功
    ERR_INVALID_PARAMETER               = 100,        //无效的参数
    ERR_DUPLICATE_SEQUENCE_ID           = 101,        //流水号已存在
    ERR_UNDEFINED_PRODUCT               = 102,        //未定义的业务类型
    ERR_UNDEFINED_PLATFORM              = 103,        //未定义的平台
    ERR_INVALID_SEQUENCE_ID             = 104,        //无效的流水号
    ERR_NOT_SUPPORT_ERROR               = 105,        //不支持的FtsCurrencyType
    ERR_BUSINESS_ERROR                  = 200,        //业务错误
    ERR_INSUFFICIENT_BALANCE            = 201,        //余额不足
    ERR_RECORD_DETAIL_ERROR             = 202,        //记录流水出错
    ERR_UPDATE_BALANCE_ERROR            = 203,        //更新余额出错
    ERR_RETRY_ERROR                     = 301,        //服务器忙，请重试
    ERR_SERVER_ERROR                    = 900,        //服务端错误
    ERR_UNKNOWN_ERROR                   = 999,        //未知错误

}

//业务类型 1-99 产出途径，>99 消耗途径
enum ProductType {
    Unknown                 = 0,
    PropsCharge             = 1,     //充值直接获得
    PropsExtra              = 2,     //充值额外获得
    DatingLottery           = 3,     //紫水晶抽取道具
    DatingSeal              = 4,     //盖章获得
    PropsGiftEffect         = 5,     //礼物流光获得
    LotteryCrystalWhite     = 6,     //白水晶抽取道具
    PropsExchange           = 7,     //豆荚兑换
    PodActivity             = 8,     //豆荚活动
    PodCustomerAward        = 9,     //豆荚大客户奖励
    BuyVitalityBomb         = 10,    //购买元气弹奖励
    LargeRechargeRefund     = 11,    //大额充值返布料
    OpenSealCrateReward     = 12,    //神秘宝箱奖励布料
    UsePowerTicketReward    = 13,    //投战力票发放布料
    BeanExchangeEnergy      = 14,    //豆子兑换能量
    EssenceExchangeEnergy   = 15,    //精萃兑换能量
    SealRewardEnergy        = 16,    //盖章获得
    FountainWishReward      = 17,    //许愿喷泉发放布料
    ExchangeIncrEnergy      = 18,    //布料兑换能量 发放能量
    SendMagicGift           = 19,    //赠送魔法礼物 随机发放活动布料
    BuyLingqiTicket         = 20,    //购买灵气券发放果实
    SealRewardFruit         = 21,    //盖章获得果实
    PropsGiftEffectEnergy   = 22,    //礼物流光获得能量
    LargeRechargeRefundEnergy = 23,  //大额充值返能量
    PodCustomerAwardEnergy    = 24,  //豆荚大客户奖励发能量
    GroupActivityReward       = 25,  //拼团充值嘉年华奖励
    StayGameReward            = 26,  //停留玩法发放
    WandouShopReward          = 27,  //玩豆商城发放
    LargeRechargeRefundActivityBean = 28,  //大额充值返星核(活动布料)
    PropsGiftEffectActivityBean = 29,  //礼物流光发放星核(活动布料)

    DropIssue                 = 30,  //抢空投发碎片
    DropBundleIssue           = 31,  //物资大战发碎片
    DropWuZiIssue             = 32,  //抢物资发碎片
    LuckDogIssue              = 33,  //幸运小狗发碎片
    SeaIssue                  = 34,  //海底秘境发碎片
    MilkyIssue                = 35,  //银河漫游发碎片
    CupidIssue                = 36,  //丘比特发碎片
    ArkIssue                  = 37,  //诺亚方舟发碎片
    SportsIssue               = 38,  //超能运动会发碎片
    PuzzleIssue               = 39,  //桃花签发碎片
    HatkingIssue              = 40,  //明星衣橱/钢铁统帅发碎片
    HatkingActIssue           = 41,  //帽子活动玩法发碎片
    StarCruiseIssue           = 42,  //星际巡航（交友）发碎片
    DriftBottleIssue          = 43,  //普通漂流瓶（聊天室）发碎片
    HighDriftBottleIssue      = 44,  //高级漂流瓶（聊天室）发碎片
    ClawMachine               = 45,  //抓娃娃 发碎片
    BubbleIssue               = 46,  //泡泡秀发碎片

    PropsGiftEffectActivityFruit = 66,  //礼物流光发放果实


    DatingHatking           = 101,   //单机帽子王消费
    WorldCup18Game          = 102,   //18世界杯玩法参与
    DatingLuckyhat          = 103,   //幸运帽消费
    DatingDragonSlaying     = 104,   //热血屠龙消费
    DatingArk               = 105,   //诺亚方舟消费
    AdventureWorld          = 106,   //探险世界消费
    ExchangeDecrFabric      = 107,   //布料兑换能量 消耗布料
    DatingSport             = 108,   //超能运动会消费
    ExpiredClean            = 109,   //到期清空
    DatingFragShop          = 110,   //装扮商城兑换（交友）
    VoiceFragShop           = 111,   //装扮商城兑换（语音房）
    ClawMachineFabric  =112, //抓娃娃消耗布料
    ClawMachineEnergy  =113, //抓娃娃消耗能量
    GiftDress               = 200,   //礼物装扮
}

//使用平台
enum TPlatform {
    Unknown            = 0,
    PC                 = 1,
    Web                = 2,
    IOS                = 3,
    Android            = 4,
    Harmony            = 11,
}

// 类型
enum MagicBeanType {
    Normal      = 0, // 普通
    Active      = 1, // 活动的
}

enum MagicBeanConsumeType
{
    Mixed       = 0, // 扣费时用，先活动再普通
    Normal      = 1, // 只扣普通
    Active      = 2, // 只扣活动的
}

//货币发放参数
struct TIssueMagicBeanRequest {
    1: i64 uid;                             // 用户uid
    2: i64 anchorUid;                       // 当前主持uid
    3: i64 sid;                             // 当前顶级频道
    4: i64 ssid;                            // 当前子频道
    5: i64 count;                           // 货币数量
    6: ProductType productId;               // 业务类型
    7: string seqId;                        // 流水号，产品订单id
    8: string description;                  // 商品描述，显示在后台流水中
    9: i64 timestamp;                       // 发放时间 unix时间戳 单位秒
    10: string expand;                      // {"platform": 1}
    11: MagicBeanType magicBeanType;        // 类型
}

// 交友货币类型
enum FtsCurrencyType
{
    Unknown      = 0,
    Fabric       = 1, // 布料（普通）
    Energy       = 2, // 能量
    Fruit        = 3, // 果实
    Signal       = 4, // 信号弹
    FabricActive = 5, // 活动布料
    FragLove     = 6, // 装扮碎片（交友）
    FragVoice    = 7, // 装扮碎片（语音房）
}


//货币扣减参数
struct TConsumeMagicBeanRequest {
    1: i64 uid;                                // 用户uid
    2: i64 anchorUid;                          // 当前主持uid
    3: i64 sid;                                // 当前顶级频道
    4: i64 ssid;                               // 当前子频道
    5: i64 count;                              // 扣减数量
    6: ProductType productId;                  // 业务类型
    7: TPlatform platform;                     // 渠道：0、pc端
    8: i64 targetUid;                          // 没有则传0
    9: string seqId;                           // 流水号，产品订单id
    10: string description;                    // 商品描述，显示在后台流水中
    11: i64 timestamp;                         // 消费时间 unix时间戳 单位秒
    12: string expand;                         // 目前用于透传，原样返回
    13: MagicBeanConsumeType consumeType;      // 扣费方式
}

struct MagicBeanInfo
{
    1: MagicBeanType magicBeanType   // 当前的魔豆类型
    2: i64 balance;                  // 用户魔豆数量(当前类型）
    3: i64 consume_count;            // 用户本次扣除的魔豆数量(当前类型）
}

struct TCurrencyServiceRet
{
    1: TErrorCode code;               // 错误码
    2: string message;                // 错误原因
    3: i64 balance;                   // 用户当前魔豆数量
    4: bool is_first_consume;         // 首次消费
    5: list<MagicBeanInfo> magic_bean_list; // 各类型扣除的信息
    6: i64 timestamp; // 最后更新时间
}

struct TMagicBeanIssueCountRet
{
    1: TErrorCode code;          //错误码
    2: string message;           //错误原因 code!=0时返回
    3: map<i64, i64> issue_stat; //各来源数量
}

struct MagicBeanStatsInfo {
	1:string Date              (go.tag = 'bson:"_id"')                ; //日期字符串
	2:i64 IssuedModou          (go.tag = 'bson:"issuedModou"')        ; //魔豆发放量
	3:i64 RemainModou          (go.tag = 'bson:"remainModou"')        ; //魔豆剩余量
	4:i64 SealRewardModou      (go.tag = 'bson:"sealRewardModou"')    ; //盖章魔豆数
	5:i64 GiftRewardModou      (go.tag = 'bson:"giftRewardModou"')    ; //流光魔豆数
	6:i64 LotteryRewardModou   (go.tag = 'bson:"lotteryRewardModou"') ; //抽取道具魔豆数
	7:i64 DoujiaRewardModou    (go.tag = 'bson:"doujiaRewardModou"')  ; //豆荚兑换魔豆数
	8:i64 MagicHatRewardModou  (go.tag = 'bson:"magicHatRewardModou"'); //魔术帽魔豆数
	9:i64 RemainActModou       (go.tag = 'bson:"remainActModou"')     ; //活动魔豆剩余量
	10:i64 CustomerRewardModou (go.tag = 'bson:"customerRewardModou"'); //豆荚大客户奖励魔豆数
	11:i64 FountainRewardModou (go.tag = 'bson:"fountainRewardModou"'); //许愿喷泉发放魔豆数
}

struct TIssueMagicBeanDetailRet
{
    1: TErrorCode code;          //错误码
    2: string message;           //错误原因 code!=0时返回
    3: list<TIssueMagicBeanRequest> record_list; //
}

struct MagicBeanDetailInfo {
    1: i64 uid;                               // 用户uid
    2: i64 count;                             // 变化数量(普通魔豆）
    3: i32 productId;                         // 业务类型
    4: i32 eventType;                         // 事件类型 1-产出 2-消费
    5: string productName;                    // 业务名称
    6: string seqId;                          // 流水号，产品订单id
    7: string description;                    // 商品描述，显示在后台流水中
    8: i64 timestamp;                         // 流水生成时间 unix时间戳 单位秒
    9: i64 actCount;                          // 变化数量（活动魔豆）
}

struct TMagicBeanFlowInfoRet
{
     1: TErrorCode code;          //错误码
     2: string message;           //错误原因 code!=0时返回
     3: list<MagicBeanDetailInfo>    record_list;
}

struct TIssueEnergyReq
{
    1: i64 uid;                                // 用户uid
    2: i64 anchorUid;                          // 当前主持uid
    3: i64 sid;                                // 当前顶级频道
    4: i64 ssid;                               // 当前子频道
    5: i64 count;                              // 扣减数量
    6: ProductType productId;                  // 业务类型
    7: TPlatform platform;                     // 渠道：0、pc端
    8: i64 targetUid;                          // 没有则传0
    9: string seqId;                           // 流水号，产品订单id
    10: string description;                    // 商品描述，显示在后台流水中
    11: i64 timestamp;                         // 消费时间 unix时间戳 单位秒
    12: string expand;                         // 目前用于透传，原样返回
}

struct TEnergyRet
{
    1: TErrorCode code;               // 错误码
    2: string message;                // 错误原因
    3: i64 balance;                   // 用户当前能量
    4: i64 timestamp;                 // 最后更新时间
}

// 扣减参数
struct TConsumeEnergyReq {
    1: i64 uid;                                // 用户uid
    2: i64 anchorUid;                          // 当前主持uid
    3: i64 sid;                                // 当前顶级频道
    4: i64 ssid;                               // 当前子频道
    5: i64 count;                              // 扣减数量
    6: ProductType productId;                  // 业务类型
    7: TPlatform platform;                     // 渠道：0、pc端
    8: i64 targetUid;                          // 没有则传0
    9: string seqId;                           // 流水号，产品订单id
    10: string description;                    // 商品描述，显示在后台流水中
    11: i64 timestamp;                         // 消费时间 unix时间戳 单位秒
    12: string expand;                         // 目前用于透传，原样返回
}

struct TReverseEnergyReq
{
     1: i64 uid;                                // 用户uid
     2: string seqId;                           // 流水号，产品订单id
}

struct EnergyDetailInfo
{
    1: i64 uid;                               // 用户uid
    2: i64 count;                             // 变化数量
    3: i32 productId;                         // 业务类型
    4: i32 eventType;                         // 事件类型 1-产出 2-消费
    5: string productName;                    // 业务名称
    6: string seqId;                          // 流水号，产品订单id
    7: string description;                    // 商品描述，显示在后台流水中
    8: i64 timestamp;                         // 流水生成时间 unix时间戳 单位秒
}

struct TEnergyFlowInfoRet
{
     1: TErrorCode code;          //错误码
     2: string message;           //错误原因 code!=0时返回
     3: list<EnergyDetailInfo>    record_list;
}

struct TIssueEnergyDetailRet
{
    1: TErrorCode code;                     //错误码
    2: string message;                      //错误原因 code!=0时返回
    3: list<TIssueEnergyReq> record_list;   //结果列表
}

struct EnergyStatsInfo {
	1:string Date               (go.tag = 'bson:"_id"');                //日期字符串
	2:i64 IssuedEnergy          (go.tag = 'bson:"issuedEnergy"');       //能量发放量
	3:i64 RemainEnergy          (go.tag = 'bson:"remainEnergy"');       //能量剩余量
	4:i64 SealRewardEnergy      (go.tag = 'bson:"sealRewardEnergy"');   //盖章能量数
	5:i64 BeanRewardEnergy      (go.tag = 'bson:"beanRewardEnergy"');   //玉石兑换能量数
	6:i64 EssenceRewardEnergy   (go.tag = 'bson:"essenceRewardEnergy"');//精粹兑换能量数
	7:i64 CustomerRewardEnergy  (go.tag = 'bson:"customerRewardEnergy"'); //豆荚大客户奖励能量数
	8:i64 GiftEffectRewardEnergy (go.tag = 'bson:"giftEffectRewardEnergy"')    ; //流光能量数
}

// 账户余额信息
struct AcctBalanceInfo {
    1:string AcctID;   // 账单id 一个用户下可能有多个账单
    2:i32 ItemID;      // 见FtsCurrencyType：1-布料 2-能量
    3:i32 Balance;     // 当前余额
    4:i32 NeverExpire; // 是否永久礼物 0-否 1-是
    5:i64 EffectTime;  // 有效期礼物开始时间: (永久礼物不填) unix时间戳 精确到秒
    6:i64 ExpireTime;  // 有效期礼物到期时间: (永久礼物不填) unix时间戳 精确到秒
}

// 账户余额查询返回
struct TAcctBalanceRet {
    1:TErrorCode code;                   // 错误码
    2:string message;                    // 错误原因 code!=0时返回
    3:list<AcctBalanceInfo> balanceList; // 余额列表
    4:i64 timestamp;                     // 时间戳 精确到毫秒
}

// 果实部分协议

struct TIssueFruitReq
{
    1: i64 uid;                                // 用户uid
    2: i64 anchorUid;                          // 当前主持uid
    3: i64 sid;                                // 当前顶级频道
    4: i64 ssid;                               // 当前子频道
    5: i64 count;                              // 扣减数量
    6: ProductType productId;                  // 业务类型
    7: TPlatform platform;                     // 渠道：0、pc端
    8: i64 targetUid;                          // 没有则传0
    9: string seqId;                           // 流水号，产品订单id
    10: string description;                    // 商品描述，显示在后台流水中
    11: i64 timestamp;                         // 消费时间 unix时间戳 单位秒
    12: string expand;                         // 目前用于透传，原样返回
}

struct TFruitRet
{
    1: TErrorCode code;               // 错误码
    2: string message;                // 错误原因
    3: i64 balance;                   // 用户当前能量
    4: i64 timestamp;                 // 最后更新时间
}

// 果实扣减参数
struct TConsumeFruitReq {
    1: i64 uid;                                // 用户uid
    2: i64 anchorUid;                          // 当前主持uid
    3: i64 sid;                                // 当前顶级频道
    4: i64 ssid;                               // 当前子频道
    5: i64 count;                              // 扣减数量
    6: ProductType productId;                  // 业务类型
    7: TPlatform platform;                     // 渠道：0、pc端
    8: i64 targetUid;                          // 没有则传0
    9: string seqId;                           // 流水号，产品订单id
    10: string description;                    // 商品描述，显示在后台流水中
    11: i64 timestamp;                         // 消费时间 unix时间戳 单位秒
    12: string expand;                         // 目前用于透传，原样返回
}

struct TReverseFruitReq
{
     1: i64 uid;                                // 用户uid
     2: string seqId;                           // 流水号，产品订单id
}

struct FruitDetailInfo
{
    1: i64 uid;                               // 用户uid
    2: i64 count;                             // 变化数量
    3: i32 productId;                         // 业务类型
    4: i32 eventType;                         // 事件类型 1-产出 2-消费
    5: string productName;                    // 业务名称
    6: string seqId;                          // 流水号，产品订单id
    7: string description;                    // 商品描述，显示在后台流水中
    8: i64 timestamp;                         // 流水生成时间 unix时间戳 单位秒
}

struct TFruitFlowInfoRet
{
     1: TErrorCode code;          //错误码
     2: string message;           //错误原因 code!=0时返回
     3: list<FruitDetailInfo>    record_list;
}

struct TIssueFruitDetailRet
{
    1: TErrorCode code;                     //错误码
    2: string message;                      //错误原因 code!=0时返回
    3: list<TIssueFruitReq> record_list;   //结果列表
}

struct FruitStatsInfo {
	1:string Date                   (go.tag = 'bson:"_id"');                    // 日期字符串
	2:i64 IssuedFruit               (go.tag = 'bson:"issuedFruit"');            // 果实发放量
	3:i64 RemainFruit               (go.tag = 'bson:"remainFruit"');            // 果实剩余量
	4:i64 SealRewardFruit           (go.tag = 'bson:"sealRewardFruit"');        // 盖章果实数
	5:i64 AuraVoucherRewardFruit    (go.tag = 'bson:"auraVoucherRewardFruit"'); // 送灵气券果实数
}

// 装扮碎片协议
struct TIssueFragReq
{
    1: i64 uid;                                // 用户uid
    2: i64 sid;                                // 当前顶级频道
    3: i64 ssid;                               // 当前子频道
    4: i64 count;                              // 扣减数量
    5: ProductType productId;                  // 业务类型
    6: TPlatform platform;                     // 渠道：0、pc端
    7: string seqId;                           // 流水号，产品订单id
    8: string description;                     // 商品描述，显示在后台流水中
    9: i64 timestamp;                          // 消费时间 unix时间戳 单位秒
    10: string expand;                         // 目前用于透传，原样返回
    11: i64 fragType;                          // 货币类型 FtsCurrencyType
    12: i64 appId;                             // 对应营收props.Appid 交友-2 语音房-34
}

struct TFragRet
{
    1: TErrorCode code;               // 错误码
    2: string message;                // 错误原因
    3: i64 balance;                   // 用户当前能量
    4: i64 timestamp;                 // 最后更新时间
}

// 碎片扣减参数
struct TConsumeFragReq {
    1: i64 uid;                                // 用户uid
    2: i64 sid;                                // 当前顶级频道
    3: i64 ssid;                               // 当前子频道
    4: i64 count;                              // 扣减数量
    5: ProductType productId;                  // 业务类型
    6: TPlatform platform;                     // 渠道：0、pc端
    7: string seqId;                           // 流水号，产品订单id
    8: string description;                     // 商品描述，显示在后台流水中
    9: i64 timestamp;                          // 消费时间 unix时间戳 单位秒
    10: string expand;                         // 目前用于透传，原样返回
    11: i64 fragType;                          // 货币类型 FtsCurrencyType中碎片
    12: i64 appId;                             // 对应营收props.Appid 交友-2 语音房-34
}

struct TReverseFragReq
{
     1: i64 uid;                                // 用户uid
     2: string seqId;                           // 流水号，产品订单id
     3: i64 fragType;                           // 货币类型 FtsCurrencyType中碎片
     4: i64 appId;                              // 对应营收props.Appid 交友-2 语音房-34
}

struct FragDetailInfo
{
    1: i64 uid;                               // 用户uid
    2: i64 count;                             // 变化数量
    3: i32 productId;                         // 业务类型
    4: i32 eventType;                         // 事件类型 1-产出 2-消费
    5: string productName;                    // 业务名称
    6: string seqId;                          // 流水号，产品订单id
    7: string description;                    // 商品描述，显示在后台流水中
    8: i64 timestamp;                         // 流水生成时间 unix时间戳 单位秒
}

struct TFragFlowInfoRet
{
     1: TErrorCode code;          //错误码
     2: string message;           //错误原因 code!=0时返回
     3: list<FragDetailInfo>    record_list;
}

struct TIssueFragDetailRet
{
    1: TErrorCode code;                     //错误码
    2: string message;                      //错误原因 code!=0时返回
    3: list<FragDetailInfo> record_list;    //结果列表
}

struct FragStatsInfo {
	1:string Date                   (go.tag = 'bson:"_id"');                    // 日期字符串
	2:i64 IssuedFrag               (go.tag = 'bson:"issuedFrag"');            // 果实发放量
	3:i64 RemainFrag               (go.tag = 'bson:"remainFrag"');            // 果实剩余量
}

service TFtsCurrencyService
{
    /**
      * 测试链接
      */
    void ping();

     /**
     * 发放魔豆接口
     *
     * @param request
     * @return TCurrencyServiceRet code：错误码；message：错误原因
     */
    TCurrencyServiceRet issueDatingMagicBean(1:TIssueMagicBeanRequest request);

     /**
     * 消费魔豆接口
     *
     * @param request
     * @return TCurrencyServiceRet code：错误码；message：错误原因
     */
    TCurrencyServiceRet consumeDatingMagicBean(1:TConsumeMagicBeanRequest request);

     /**
     * 查询用户魔豆接口
     *
     * @param uid 用户uid
     * @param expand 扩展字段
     * @return TUserMagicBeanData
     */
    TCurrencyServiceRet queryUserMagicBeanBalance(1:i64 uid, 2:string expand);

     /**
     * 查询用户魔豆剩余总量接口
     *
     * @return TCurrencyServiceRet
     */
    TCurrencyServiceRet queryAllMagicBeanBalance();

     /**
     * 查询各来源魔豆金额接口
     *
     * @return TMagicBeanIssueCountRet
     */
    TMagicBeanIssueCountRet queryMagicBeanIssueBySource(1:i64 timestamp);


     /**
     * 查询每日魔豆统计信息
     *
     * @param startTime 开始日期 unix时间戳
     * @param endTime 结束日期 unix时间戳
     * @return list<MagicBeanStatsInfo>
     */
    list<MagicBeanStatsInfo> getMagicBeanStatsInfo(1:i64 startTime, 2:i64 endTime);

     /**
     * 魔豆返回，冲正接口
     *
     * @param request
     * @return TCurrencyServiceRet code：错误码；message：错误原因
     */
    TCurrencyServiceRet reverseDatingMagicBean(1:TIssueMagicBeanRequest request);

     /*
     * 获取魔豆发放流水
     * @para productType 见ProductType
     * @para limitCount 最近的记录条数
     * @return
     *
     */
     TIssueMagicBeanDetailRet queryMagicBeanIssuedDetail(1:i64 uid, 2:i32 productType, 3:i32 limitCount, 4:i64 timeStart, 5:i64 timeEnd);

     /*
     * 获取最近7天魔豆发放和消费流水
     * @return
     *
     */
     TMagicBeanFlowInfoRet queryLast7DaysMagicBeanDetail(1:i64 uid);


    // 发放能量
    TEnergyRet issueEnergy(1:TIssueEnergyReq req);

    // 消费能量
    TEnergyRet consumeEnergy(1:TConsumeEnergyReq req);

    // 查询用户剩余能量
    TEnergyRet queryEnergy(1:i64 uid);

    // 消费冲正
    TEnergyRet reverseEnergy(1:TReverseEnergyReq req);

    // 获取最近7天能量发放和消费流水
    TEnergyFlowInfoRet queryLast7DaysEnergyDetail(1:i64 uid);


    // 获取能量发放流水
    // @param productType 见ProductType
    // @param limitCount 最近的记录条数
    // @return
    TIssueEnergyDetailRet queryEnergyIssuedDetail(1:i64 uid, 2:i32 productType, 3:i32 limitCount, 4:i64 timeStart, 5:i64 timeEnd);


    // 查询每日魔豆统计信息
    // @param startTime 开始日期 unix时间戳
    // @param endTime 结束日期 unix时间戳
    // @return list<MagicBeanStatsInfo>
    list<EnergyStatsInfo> getEnergyStatsInfo(1:i64 startTime, 2:i64 endTime);

    // 查询用户账本余额（布料、能量）
    // @param itemList 见FtsCurrencyType
    // @return
    TAcctBalanceRet queryAcctBalance(1:i64 uid, 2:list<i32> itemList);


    // 发放果实
    TFruitRet issueFruit(1:TIssueFruitReq req);

    // 消费果实
    TFruitRet consumeFruit(1:TConsumeFruitReq req);

    // 查询用户剩余果实
    TFruitRet queryFruit(1:i64 uid);

    // 果实消费冲正
    TFruitRet reverseFruit(1:TReverseFruitReq req);

    // 获取最近7天果实发放和消费流水
    TFruitFlowInfoRet queryLast7DaysFruitDetail(1:i64 uid);


    // 获取果实发放流水
    // @param productType 见ProductType
    // @param limitCount 最近的记录条数
    // @return
    TIssueFruitDetailRet queryFruitIssuedDetail(1:i64 uid, 2:i32 productType, 3:i32 limitCount, 4:i64 timeStart, 5:i64 timeEnd);


    // 查询每日果实统计信息
    // @param startTime 开始日期 unix时间戳
    // @param endTime 结束日期 unix时间戳
    // @return list<MagicBeanStatsInfo>
    list<FruitStatsInfo> getFruitStatsInfo(1:i64 startTime, 2:i64 endTime);

    // 发放碎片
    TFragRet issueFrag(1:TIssueFragReq req);

    // 消费碎片
    TFragRet consumeFrag(1:TConsumeFragReq req);

    // 查询用户剩余碎片
    TFragRet queryFrag(1:i64 uid, 2:i64 fragType);

    // 碎片消费冲正
    TFragRet reverseFrag(1:TReverseFragReq req);

    // 获取碎片发放流水
    // @param fragType 碎片类型
    // @param limitCount 最近的记录条数
    // @param fragType 碎片类型
    // @return
    TIssueFragDetailRet queryFragIssuedDetail(1:i64 uid, 2:i32 productType, 3:i64 fragType,
    4:i32 limitCount, 5:i64 timeStart, 6:i64 timeEnd);


//    // 获取最近7天碎片发放和消费流水
//    TFragFlowInfoRet queryLast7DaysFragDetail(1:i64 uid, 2:i64 fragType);
//
//    // 查询每日碎片统计信息
//    // @param startTime 开始日期 unix时间戳
//    // @param endTime 结束日期 unix时间戳
//    // @param fragType 碎片类型
//    // @return list<FragStatsInfo>
//    list<FragStatsInfo> getFragStatsInfo(1:i64 startTime, 2:i64 endTime, 3:i64 fragType,);
}
