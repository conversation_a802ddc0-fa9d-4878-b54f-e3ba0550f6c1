namespace java com.yy.hd.api.thrift.game.match.extension
/**
* 邀请匹配服务扩展协议, 由业务生态实现
* 1. 支持是否接受邀请匹配状态查询和设置
* 2. 支持推荐列表
* 3. 支持获取邀请匹配对手信息
* 4. 支持匹配业务校验（能不能匹配）
**/

struct RespHeader {
    1:i32    code;
    2:string msg;
}

// 匹配推荐项
struct RecommendItem {
  1:i64 uid; // 房间主持uid
  2:i64 imid;
  3:string nick;
  4:i64 sid;
  5:i64 ssid;
  6:i32 game_type; // 玩法类型
}

// 检查是否能进行pk
struct CheckPkLimitReq {
    1:i64 uid; // 操作者uid
    2:i64 sid; // 操作者当前所在sid
    3:i64 ssid; // 操作者当前所在ssid
    4:PkTeamInfo inviterTeam; // 邀请人队伍信息
    5:PkTeamInfo inviteeTeam; // 被邀请人队伍信息
    6:i32 matchMode;          // 邀请进行什么玩法匹配（旧多人视频乱斗：19, 新多人视频乱斗: 27）
}

// 对手信息（发送邀请单播的时候，需要把这个信息带上）
struct OpponentInfo {
    1:i64 uid; // 对手uid
    2:string avatar; // 对手头像
    3:string nick;   // 对手昵称
    4:i64 charm; // 对手魅力值
    5:i64 asid;
    6:i64 sid;
    7:i64 ssid;
    8:i64 endTime; // 结束时间，秒
    9:i64 imid; // 对手yy号
    10:i32 compereTier;
    11:i32 compereStars;
    12:list<OpponentGuest> teamGuestInfo; // 对手嘉宾信息
    13:string extend; // 其他扩展信息
}

// 对手嘉宾信息
struct OpponentGuest {
    1:i64 uid; // 对手uid
    2:string avatar; // 对手头像
}

struct OpponentInfoRsp {
    1:RespHeader header;
    2:OpponentInfo opponentInfo; // 对手信息
}

// pk团队信息
struct PkTeamInfo {
    1:i64 uid;
    2:i64 sid;
    3:i64 ssid;
    4:list<i64> guestUidList; // 选择的 嘉宾列表
    5:i32 gameType; // 当前玩法类型
}

service HdGameMatchExtensionService {
    // ping
    void ping();

    // 查询uid是否接受匹配邀请状态 false-不接受 true-接受
    bool getRecvMatchInviteStatus(1:i64 uid)

    // 设置uid是否接受匹配邀请状态 false-不接受 true-接受
    RespHeader setRecvMatchInviteStatus(1:i64 uid, 2:bool status)

    // 获取推荐列表
    list<RecommendItem> getRecommendList(1:i64 uid, 2:i64 sid, 3:i64 ssid);

    // 获取 对手信息（新服务发送邀请单播的时候，需要把这个信息带上）
    OpponentInfoRsp getOpponentInfo(1:i64 uid, 2:i64 sid, 3:i64 ssid, 4:list<i64> guestUids)

    // 检查是否可以进行pk
    RespHeader checkPkLimit(1:CheckPkLimitReq req)

    // 发业务旧版本邀请单播
    RespHeader sendMatchInviteNotice(1:i64 inviteeUid, 2:i32 matchMode, 3:i64 inviterUid 4:i64 inviterSid, 5:i64 inviterSsid, 6:list<i64> inviterGuestUids);

    // 发业务旧邀请结果单播
    // matchMode: 邀请进行什么玩法匹配（旧多人视频乱斗：19, 新多人视频乱斗: 27）
    // accept: 是否接受邀请
    RespHeader sendMatchInviteConfirmNotice(1:i64 uid, 2:i32 matchMode, 3:bool accept);
}