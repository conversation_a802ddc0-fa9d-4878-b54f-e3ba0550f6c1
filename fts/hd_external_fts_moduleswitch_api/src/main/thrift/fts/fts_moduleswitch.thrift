include "../../../../../hd_external_fts_base_api/src/main/thrift/fts/common.thrift"
namespace java com.yy.hd.api.thrift.fts.moduleswitch

/**
 * WARNING 接口包含控制开关，请不要直接对外公布接口
 * 说明：
 *
 * 配置内容：boss-单机头衔王-交友模块开启状态管理
 * http://jyboss.yy.com/n/21/module_stat           //正式
 * http://jyboss-test.yy.com/n/22/module_stat      //测试
 *
 * s2sname:
 * fts_module_switch
 * fts_module_switch_test
 *
 * 备注： 这里的H5版本配置对应boss后台页面的"PC模板玩法白名单"而不是"H5模板灰度白名单"
*/

struct ModuleSwitchInfoRet
{
    1:i64 ret;    //返回：0，ok   1  error
    2:i64 status; //状态：0，功能正常开启  1 暂停   2 升级   3 停止   4 模块不存在
    3:string msg; //提示消息
}

struct ModuleSwitchInfo
{
    1:i64 moduleId,  //模块ID
    2:string moduleName, //模块名
    3:i64 status,        //模块状态：0，功能正常开启  1 暂停   2 升级   3 停止   4 模块不存在
    4:string msg,        //提示消息
    5:i64 startTime,     // status为0时有效， 功能正常开启时 开始的时间
    6:i64 endTime,        // status为0时有效， 功能正常开启时，结束的时间
    7:string platform,    //平台
    8:string channel,      //渠道
    9:string synopsis,     //功能简介
    10:i32 disable, //是否停用 0-否 1-是
}

struct ModuleSwitchListRet
{
    1:i64 ret;    //返回：0，ok   1  error
    2:string msg;
    3:list<ModuleSwitchInfo> datas;
}

struct ModuleSwitchDetailInfoRet
{
      1:i64               ret;    //返回：0，ok   1  error
      2:string            msg;    //提示消息
      3:ModuleSwitchInfo  info;   //详细信息
}


struct H5GameVerConf
{
    1:i64 sid; // 顶级频道
    2:i32 gameType; // 玩法
    3:string h5Version; // 某玩法的h5版本
    4:i64 uid; // 进行操作的uid
    5:string remark; // 备注;版本说明
    6:i64 timestamp; // 更新时间戳
}

struct H5GameVerConfRet
{
    1:i32 ret, // 0--ok 1--错误
    2:string msg, // 如果有错误时的提示
}

struct GetAllH5GameVerConfRet
{
    1:i32 ret, // 0--ok 1--错误
    2:string msg, // 如果有错误时的提示
    3:list<H5GameVerConf> gameVerConfs
}

// 数据更新历史
struct UpdateHistory
{
    1:string col     (go.tag = 'json:"col" bson:"col"')
    2:i64    opUid   (go.tag = 'json:"opUid" bson:"opUid"')
    3:string tag      (go.tag = 'json:"tag" bson:"tag"')
    4:string before  (go.tag = 'json:"before" bson:"before"')
    5:string params  (go.tag = 'json:"params" bson:"params"')
    6:string after   (go.tag = 'json:"after" bson:"after"')
    7:i64    timestamp   (go.tag = 'json:"timestamp" bson:"timestamp"')
    8:string resErr   (go.tag = 'json:"resErr" bson:"resErr"')
}

struct GetH5GameUpdateHistoryRet {
   1:i32 ret,
   2:string msg,
   3:list<UpdateHistory> history
}

struct channelInfo {
    1:i64 sid,
    2:i64 ssid,
}

struct CanaryChannelListRet {
   1:i32 ret,
   2:string msg,
   3:list<channelInfo> channelList,
}

struct CanaryChannelMapRet {
   1:i32 ret,
   2:string msg,
   3:map<i64, list<channelInfo>> channelMap,
}

service FtsModuleSwitchService
{
    //ping
    void ping();

    /**
    * 获取模块开关的状态，用于其他服务查询
    * module定义：1，紫水晶抽取道具  2，单机头衔王  3，手机app贵族  4，交友app头衔王  5，扭蛋抽取道具   6，幸运帽   7，星星相伴
    * 8 白水晶抽取道具  9 交友app盖章  10 扭蛋券转赠 11 贵族卡转赠
    * 请在boss 后台中查找对应记录 jyboss.yy.com/ 当前位于“单机帽子王”--“交友模块开关管理”栏目下
    **/
    ModuleSwitchInfoRet GetModuleSwitchInfo(1:i64 moduleId);

    /**
     * 获取模块开关的详细信息，用于更新时告警
     **/
    ModuleSwitchDetailInfoRet GetModuleSwitchDetailInfoById(1:i64 moduleId);

    /**
    * 获取所有模块列表
    *
    **/
    ModuleSwitchListRet GetModuleSwitchList();

    /**
    * 根据id获取模块列表
    *
    **/
    ModuleSwitchListRet GetModuleSwitchListWithIds(1:list<i64> moduleIds);

    /**
    * 设置模块开关的状态
    *
    * @param 1 moduleSwitchInfo 待设置的参数
    * @return 0 ok   1 err
    **/
    i64 SetModuleSwitchInfo(1:ModuleSwitchInfo moduleSwitchInfo)

    /**
    * 删除模块开关的
    *
    * @param 1 moduleId 待删除的Id参数
    * @return 0 ok   1 err
    **/
    i64 DelModuleSwitchInfo(1:i64 moduleId)

    // /**
    // * 按指定的平台、渠道获取模块列表
    // * @param filter: "platfrom" - 按平台 "channel" - 按渠道 "all" - 所有
    // **/
    // ModuleSwitchListRet GetModuleSwitchListByFilter(1:string filter, 2:list<i32> ids);


     /**
      *批量更新某个玩法的h5版本配置
      *
      */
     H5GameVerConfRet UpdateH5GameVerConf(1:list<H5GameVerConf> gameVerConfs)

     /**
      * 批量删除某个玩法的h5版本配置
      *
      **/
      H5GameVerConfRet DelH5GameVerConf(1:list<H5GameVerConf> gameVerConfs)

     /**
      * 拉取所有玩法的h5版本配置
      *
      **/
      GetAllH5GameVerConfRet GetAllH5GameVerConf()


     /**
      *批量更新某个玩法的h5版本配置，支持全web化的PC版本
      *
      */
     H5GameVerConfRet UpdateH5GameVerConf2(1:list<H5GameVerConf> gameVerConfs)

     /**
      * 批量删除某个玩法的h5版本配置，支持全web化的PC版本
      *
      **/
      H5GameVerConfRet DelH5GameVerConf2(1:list<H5GameVerConf> gameVerConfs)

     /**
      * 拉取所有玩法的h5版本配置，支持全web化的PC版本
      *
      **/
      GetAllH5GameVerConfRet GetAllH5GameVerConf2()

      /**
       * 查询h5版本配置的更新记录
       * @param maxRow-返回最新的多少条记录
      **/
      GetH5GameUpdateHistoryRet GetH5GameUpdateHistory(1:i32 maxRow);

     /**
      * 获取模块的灰度开关详细信息
      * @param moduleId-交友boss/开关管理/灰度测试开关/功能ID sid:顶级频道id ssid:子频道id
      **/
      ModuleSwitchDetailInfoRet GetCanarySwitchInfoById(1:i64 moduleId, 2:i64 sid, 3:i64 ssid);

     /**
      * 获取模块的灰度频道列表
      * @param moduleId-交友boss/开关管理/灰度测试开关/功能ID
      **/
      CanaryChannelListRet GetCanarySwitchChannelList(1:i64 moduleId);

     /**
      * 批量获取模块的灰度频道列表
      * @param moduleIds-交友boss/开关管理/灰度测试开关/功能ID列表
      **/
      CanaryChannelMapRet BatchGetCanarySwitchChannelList(1:list<i64> moduleIds);
}
