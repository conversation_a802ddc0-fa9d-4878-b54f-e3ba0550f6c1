# webdb_service.thrift -- Thrift interfaces definition for webdb_sinfo

namespace java com.yy.hd.external.thrift.webdb

struct result_list
{
    1:i32 errcode;
    2:list<string> retlist;
}


//================================== sa(service agent) params struct start ====================================//
struct AuthorizeMsg {
	1:string AuthUser="",
	2:string AuthKey="",
	3:map<string, string> keyValue,
}

struct StringList {
	1:list<string> strList,
}

/**
 * @param     rescode            返回码
 * @param     keyValue           保留字段
 * @param     keyIndex           columns中各字段在dataSet中对应的索引
 * @param     dataset            内层StringList表示一行查询结果,外层list表示多行查询结果
 */
struct SaResponseSet {
	1:i32 rescode,
	2:map<string, string> keyValue,
	3:map<string, i32> keyIndex,
	4:list<StringList> dataSet
}

//================================ sa(service agent) sinfo params start ================================//
/**
 * 通过顶级频道id查询频道信息
 * @param     appkey          客户端的标识
 * @param     sids            顶级频道id列表(列表最大长度为500)
 * @param     type             0 - 顶级频道id列表为频道长号
 *                             1 - 顶级频道id列表为频道短号
 *                             2 - 顶级频道id列表包含频道长号与频道短号
 * @param     columns         需要查询的字段集合，可以查询如下字段
*                               - "sid", 频道长号
*                               - "name", 频道名称
*                               - "isp", 频道isp
*                               - "area", 区域
*                               - "province", 省份
*                               - "city", 城市
*                               - "blimit", 限制只有会员能够进入
*                               - "passwd", 频道密码
*                               - "bpub", 是否开放搜索
*                               - "create_time", 创建时间
*                               - "style", 麦序
*                               - "microtime", 麦序时间
*                               - "typestr", 类型描述
*                               - "type", 类型
*                               - "is_limit_txt", 是否限制文字聊天速度
*                               - "txt_limittime", 限制文字聊天速度每句间隔秒数
*                               - "ownerid", 所有者用户uid
*                               - "logo_index", 频道logo类型
*                               - "apply_jifen", 入会积分
*                               - "apply_announce", 入会声明
*                               - "lang"：语言
*                               - "template_id", 频道模板
*                               - "credit", 频道信用
*                               - "apptype", 频道类型
*                               - "anony_limit", 频道匿名用户最大人数限制
*                               - "logo_url", 频道logo url
*                               - "sinfo_jifen", 频道积分
*                               - "asid", 频道短号
*                               - "user_limit", 频道人数限制
*                               - "bulletin", 公告内容 (对于公告内容,不允许批量查询)
*                               - "bulletstamp", 公告时间戳
*                               - "jiedai_sid", 接待频道号
* @SaResponseSet             顶级频道信息结果集，如果没有查到结果则dataSet为空
*/
struct SaRequestSession {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:list<string> sids,
	4:i32 type,
	5:list<string> columns,
}

/**
 * 通过顶级频道id和子频道id查询子频道信息
 * @param     appkey          客户端的标识
 * @param     tidSids         顶级频道id和子频道id的列表(列表最大长度为500)，列表中的每行记录包含两个元素：
 *            第一个元素      顶级频道ID (频道长号)
 *            第二个元素      子频道ID
 * @param     columns         需要查询的字段集合，可以查询如下字段
 *                              - "tid": 顶级频道id
 *                              - "sid": 频道id
 *                              - "pid": 父频道id
 *                              - "name": 频道名字
 *                              - "maxman" 子频道人数限制
 *                              - "passwd": 频道密码
 *                              - "create_time": 频道创建时间
 *                              - "style", 麦序模式
 *                              - "microtime", 麦序时间
 *                              - "is_limit_txt", 是否限制文字聊天速度
 *                              - "txt_limittime", 限制文字聊天速度每句间隔秒数
 *                              - "sort", 排序序号
 *                              - "charge", 收费频道类型
 *                              - "template_id", 频道模板
 *                              - "is_passwd_set", 子频道是否有设置密码
 *                              - "is_guest_access_limit", 子频道是否有限制游客进入
 *                              - "bulletin", 公告内容 (对于公告内容,不允许批量查询)
 *                              - "bulletstamp", 公告时间戳
 * @SaResponseSet               子频道信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestSubsess {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:list<StringList> tidSids,
	4:list<string> columns,
}


/**
 * 通过uid查询用户信息(如果webdb查询不到,会从udb同步用户信息)
 * @param      appkey          客户端的标识
 * @param      uid             用户uid
 * @param      columns         需要查询的字段集合，可以查询如下字段
 *                               - "id", 用户uid
 *                               - "yyno", YY号
 *                               - "nick", 昵称
 *                               - "sex", 性别
 *                               - "birthday", 生日
 *                               - "area", 地区
 *                               - "province", 省份
 *                               - "city", 城市
 *                               - "sign", 签名
 *                               - "intro", 个人说明
 *                               - "jifen", 个人积分,按分钟计算
 *                               - "register_time", 注册时间
 *                               - "passport", 通行证
 *                               - "account", 邮箱
 *                               - "custom_logo", 用户个人普通自定义头像url(60*60)
 *                               - "hdlogo"，个人高清头像图片url(640*640)
 *                               - "session_card"，频道基础名片图片url
 *                               - "logo_index"，个人系统头像类型。0为自定义头像，即为hdlogo；其他值为系统定义头像
 *                               - "hd_logo_60", 个人高清自定义头像url(60*60)
 *                               - "hd_logo_100", 个人高清自定义头像url(100*100)
 *                               - "hd_logo_144", 个人高清自定义头像url(144*144)
 *                               - "hd_logo_640", 个人高清自定义头像url(640*640)
 *                               - "baidu_nick",  百度昵称
 *                               - "tieba_nick",  贴吧昵称
 * @param     hostId           1:YY系所有APP 2:百度好看视频 3:手百 4:全民小视频 5:百度贴吧
 * @param     hasNickExt       为true时回包中会带回nickExt信息，否则不带回nickExt信息
 * @saResponseSetWithNickExt    用户信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestUserWithNickExt {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:string uid,
    4:i32 hostId,
    5:bool hasNickExt,
    6:list<string> columns,
}

/**
 * 通过uid查询用户信息(批量)
 * @param     appkey           客户端的标识
 * @param     uids             用户uid列表(列表最大长度为500)
 * @param     columns          需要查询的字段集合，批量接口只支持以下字段
 *                               - "id", 用户uid
 *                               - "yyno", YY号
 *                               - "nick", 用户昵称
 *                               - "sex", 性别
 *                               - "birthday", 生日
 *                               - "area", 地区
 *                               - "province", 省份
 *                               - "city", 城市
 *                               - "sign", 签名
 *                               - "intro", 个人说明
 *                               - "jifen", 个人积分,按分钟计算
 *                               - "register_time", 注册时间
 *                               - "passport", 通行证
 *                               - "account", 邮箱
 *                               - "custom_logo", 用户个人普通自定义头像url(60*60)
 *                               - "hdlogo"，个人高清头像图片url(640*640)
 *                               - "session_card"，频道基础名片图片url
 *                               - "logo_index"，个人系统头像类型。0为自定义头像，即为hdlogo；其他值为系统定义头像
 *                               - "hd_logo_60", 个人高清自定义头像url(60*60)
 *                               - "hd_logo_100", 个人高清自定义头像url(100*100)
 *                               - "hd_logo_144", 个人高清自定义头像url(144*144)
 *                               - "hd_logo_640", 个人高清自定义头像url(640*640)
 *                               - "baidu_nick",  百度昵称
 *                               - "tieba_nick",  贴吧昵称
 * @param     hostId           1:YY系所有APP 2:百度好看视频 3:手百 4:全民小视频 5:百度贴吧
 * @param     hasNickExt       为true时回包中会带回nickExt信息，否则不带回nickExt信息
 * @SaResponseSetWithNickExt   用户信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestBatchUserWithNickExt {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:list<string> uids,
    4:i32 hostId,
    5:bool hasNickExt,
    6:list<string> columns,
}

/**
 * @param     rescode            返回码
 * @param     keyValue           保留字段
 * @param     keyIndex           columns中各字段在dataSet中对应的索引
 * @param     dataset            内层StringList表示一行查询结果,外层list表示多行查询结果
 */
struct SaResponseSetWithNickExt {
	1:i32 rescode,
	2:map<string, string> keyValue,
	3:map<string, i32> keyIndex,
	4:list<StringList> dataSet,
	5:string nickExt,
}

/**
 * 通过yy号查询用户的uid
 * @param     appkey           客户端的标识
 * @param     imids            用户yy号(又称imid)列表(列表最大长度为500)
 * @SaResponse                 keyValue格式为<yy号，uid>的映射，映射中只包含能查询到对应uid的元素,
 *                             如果参数给出的全部yy号都不是有效yy，则keyValue为空
 */
struct SaRequestUid {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:list<string> imids,
}

/**
 * @param     rescode            返回码
 * @param     keyValue           保留字段
 */
struct SaResponse {
	1:i32 rescode,
	2:map<string, string> keyValue,
}

/**
 * 通过uid查询用户信息(批量)
 * @param     appkey           客户端的标识
 * @param     uids             用户uid列表(列表最大长度为500)
 * @param     columns          需要查询的字段集合，批量接口只支持以下字段
 *                               - "id", 用户uid
 *                               - "yyno", YY号
 *                               - "nick", 用户昵称
 *                               - "sex", 性别
 *                               - "birthday", 生日
 *                               - "area", 地区
 *                               - "province", 省份
 *                               - "city", 城市
 *                               - "sign", 签名
 *                               - "intro", 个人说明
 *                               - "jifen", 个人积分,按分钟计算
 *                               - "register_time", 注册时间
 *                               - "passport", 通行证
 *                               - "account", 邮箱
 *                               - "custom_logo", 用户个人普通自定义头像url(60*60)
 *                               - "hdlogo"，个人高清头像图片url(640*640)
 *                               - "session_card"，频道基础名片图片url
 *                               - "logo_index"，个人系统头像类型。0为自定义头像，即为hdlogo；其他值为系统定义头像
 *                               - "hd_logo_60", 个人高清自定义头像url(60*60)
 *                               - "hd_logo_100", 个人高清自定义头像url(100*100)
 *                               - "hd_logo_144", 个人高清自定义头像url(144*144)
 *                               - "hd_logo_640", 个人高清自定义头像url(640*640)
 * @SaResponseSet              用户信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestBatchUser {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:list<string> uids,
    4:list<string> columns,
}

//================================ sa(service agent) sinfo params end ================================//

// 基本服务接口
service webdb_base_service
{
    /**
     * 查询服务是否存活
     */
    void ping(),

    /**
     * 查询服务版本号，此接口已废弃
     */
    string version(),
}

service webdb_sinfo_service extends webdb_base_service {
    SaResponseSet sa_get_session_info(1:SaRequestSession request)
    SaResponseSet  sa_get_subsess_info(1:SaRequestSubsess request)
    SaResponseSetWithNickExt sa_get_user_info_with_nickext(1:SaRequestUserWithNickExt request)
    SaResponseSetWithNickExt sa_batch_get_user_info_with_nickext(1:SaRequestBatchUserWithNickExt request)
    SaResponse    sa_get_uid_by_imid(1:SaRequestUid request),
    SaResponseSet sa_batch_get_user_info(1:SaRequestBatchUser request),
}