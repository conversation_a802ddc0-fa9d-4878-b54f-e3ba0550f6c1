namespace java com.yy.hd.external.thrift.chanif
// chanif.thrift 申请入口：统一SA服务 -> 频道对外接口服务

/*通用结构体 开始*/
struct AuthorizeMsg
{
	1:string AppKey="", //接入鉴权用户名，自助接入平台系统分配
	2:string AppSecret="", //接入鉴权密码，自助接入平台系统分配
	3:map<string,string> keyvalue, //扩展
}

struct getChannelInfoReq
{
    1:AuthorizeMsg authmsg,//鉴权信息,必选
    2:i64 topsid, //顶级频道号
	3:i64 subsid, //子频道号
    4:list<i64> keys, //频道信息属性。常用的属性值请参见上文，如需详细信息请咨询王书孝。
}

struct getChannelInfoRes
{
    1:i32 rescode, //调用结果码,默认为0,必选
    2:map<string,string> keyvalue, //扩展,必选
    3:i32 retcode, //通用返回码
    4:string retstr, //通用返回提示信息
    5:map<i64, string> props, //频道信息属性
}

struct CommonRes
{
    1:i32 rescode, //调用结果码，默认为0，必选
    2:map<string,string> keyvalue, //扩展，必选
    3:i32 retcode, //通用返回码
    4:string retstr, //通用返回提示信息
}
struct updateChannelInfoReq
{
    1:AuthorizeMsg authmsg,//鉴权信息,必选
    2:i64 topsid, //顶级频道号
	3:i64 subsid, //子频道号
    4:map<i64, string> props, //频道信息属性。常用的属性值请参见上文，如需详细信息请咨询王书孝。
    5:i64 admin, //管理员uid
}

service ChannelService
{
    getChannelInfoRes chanif_getChannelInfo(1:getChannelInfoReq req) //获取频道信息
    CommonRes chanif_updateChannelInfo(1:updateChannelInfoReq req) //更新频道信息
}