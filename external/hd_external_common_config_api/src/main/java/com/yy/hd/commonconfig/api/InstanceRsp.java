package com.yy.hd.commonconfig.api;

import com.yy.ent.commons.protopack.marshal.BeanMarshal;
import com.yy.ent.commons.protopack.util.String32;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class InstanceRsp extends BeanMarshal {

    private Integer page;

    private Integer size;

    private Long total;

    private Integer status;

    private String32 data;

    private Map<String, String> extendInfo = new HashMap<>();

}
