package com.yy.hd.commonconfig.vo;

import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR> 2021/11/24
 */
@Data
public class ConfigWrapper<T> {

    private String id;
    private T current;
    private String def;
    private Map<String,String> extend;
    private String lastOperator;
    private Date createTime;
    private Date updateTime;
    private Integer version;

}
