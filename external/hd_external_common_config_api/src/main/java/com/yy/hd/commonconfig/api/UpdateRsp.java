package com.yy.hd.commonconfig.api;

import com.yy.ent.commons.protopack.marshal.BeanMarshal;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> 2021/11/23
 */
@Getter
@Setter
public class UpdateRsp extends Bean<PERSON>ars<PERSON> {

    /**
     * 回传requestId
     */
    private String requestId;
    /**
     * 0 == 成功
     */
    private int status = -1;
    private Map<String, String> extendInfo = new HashMap<>();

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Map<String, String> getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(Map<String, String> extendInfo) {
        this.extendInfo = extendInfo;
    }
}
