package com.yy.hd.commonconfig.api;

import com.yy.ent.commons.protopack.marshal.BeanMarshal;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR> 2021/11/23
 */
@Getter
@Setter
public class UpdateReq extends BeanMarshal {

    private String requestId = UUID.randomUUID().toString();
    private String biz;
    private String def;
    private int type = 2;
    /**
     * extendInfo放入需要更新的字段和值, id字段必须
     */
    private Map<String , String> extendInfo = new HashMap<>();

    public UpdateReq(String biz, String def) {
        this.biz = biz;
        this.def = def;
    }

    public UpdateReq update(String field, String value) {
        extendInfo.put(field, value);
        return this;
    }

    public String getRequestId() {
        return requestId;
    }

    public int getType() {
        return type;
    }

    public String getBiz() {
        return biz;
    }

    public String getDef() {
        return def;
    }

    public Map<String, String> getExtendInfo() {
        return extendInfo;
    }

}
