package com.yy.hd.commonconfig.api;

import org.apache.dubbo.common.annotation.Yrpc;

/**
 * <AUTHOR>
 * @since 2024/4/8 18:07
 */
public interface CommonConfigApi {

    @Yrpc(reqUri = YsAdminURIs.INSTANCE_REQ, resUri = YsAdminURIs.INSTANCE_RSP)
    InstanceRsp getConfig(InstanceReq req);

    @Yrpc(reqUri = YsAdminURIs.UPDATE_REQ, resUri = YsAdminURIs.UPDATE_RSP)
    UpdateRsp updateConfig(UpdateReq req);

}
