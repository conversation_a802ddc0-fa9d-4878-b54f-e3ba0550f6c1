namespace java com.yy.hd.external.thrift.cul

//鉴权字段
struct AuthorizeMsg
{
    1: string AuthUser;
    2: string AuthKey;
    3: map<string,string>keyvalue;
}

//++++++++++++++++++++++++++++++用户所在频道+++++++++++++++++++++++++++++++++++++++//
/*
notice:web多开后用户可以进多个频道，非web非开放
* @ topsid    : 频道号
* @ subsid    : 子频道号
* @ timestamp : 进频道时间戳：不保证一致
*/
struct UserChannelInfo{
    1: i64 topsid;
    2: i64 subsid;
    3: i64 timestamp;
}
/*
* 功能:用户所在频道请求
* @ uids  : 用户列表；数量不能超过1000
*/
struct UserChanReq{
    1: set<i64> uids;
    2:  string context;
    3:  map<string,string> extents;
}

/*
* 功能:用户所在频道接口请求
* @ authMsg : sa服务鉴权字段
* @ req     : 用户所在频道请求
*/
struct UserChannelReq
{
    1: AuthorizeMsg authMsg;
    2: UserChanReq req;
}

/*
* 功能:用户所在频道响应
* @ rescode: 返回码
* @ uid2ChannelInfos : uid->用户所在频道集合
*/
struct UserChanRes{
    1: i32 resCode;
    2: map<i64,list<UserChannelInfo>> uid2ChannelInfos;
    3: string context;
    4: map<string,string> extents;
}

/*
* 功能:用户所在频道接口响应
* @ rescode  : sa服务返回码
* @ keyvalue : sa服务扩展字段
* @ res      : 用户所在频道响应
*/
struct UserChannelRes
{
    1: i32 rescode;
    2: map<string,string>keyvalue;
    3: UserChanRes res;
}

service CULService{
    UserChannelRes             queryUserChannel(1: UserChannelReq req);                         // 查询用户所在频道
}