syntax = "proto3";
package com.yy.kaihei.api.pb.cul;
option java_outer_classname = "Cul";

// 进出频道
message ChannelOnlineEvent{
  uint64 groupId    = 1;
  string serverId   = 2;
  uint64 timestamp  = 3;
  uint64 seqId      = 4;                       //同频道下递增
  uint64 topsid     = 5;                       //顶级频道号
  map<uint64, ChannelInfo> chanInfo   = 6;     //子频道id->信息
  map<uint64, UserInfo>    joinUsers  = 7;     //进频道 uid->用户信息，下同
  map<uint64, UserInfo>    moveUsers  = 8;     //跳转频道
  map<uint64, UserInfo>    leaveUsers = 9;     //出频道
  map<string, string>      extends    = 10;
}
//pb编码后塞入UserInfo.StrProp[U_ROLE]
message Roles{
  map<uint32, uint32> roles = 1;   //频道id->马甲值
}
message UserInfo{
  enum UserInfoKey {
    U_UNKONW  = 0;
    U_TOPSID  = 1;            //顶级频道号
    U_SUBSID  = 2;            //子频道号、目的频道号（跳转频道时）
    U_JIFEN   = 3;            //积分
    U_SRCSID  = 4;            //源频道（跳转频道）
    U_TERMINAL  = 5;            //终端类型
    U_UID       = 6;            //uid
    U_ISTEMP    = 7;            //匿名用户:1
    U_TIMESTAMP = 8;            //事件发生时的时间戳
    U_APPKEY    = 9;            //app标识，99999代表是机器人
    U_IP        = 10;           //ip

    // str prop
    U_ROLE = 102;          //马甲
    U_NICK = 103;          //nick
    U_BIZSOURCE = 104;     // 进频道来源信息,byteProp属性 https://docs.google.com/spreadsheets/d/1dulf7OyTkS6SlthbRCfIVIPFaxLgH0TkzvgO1Qav3xM/edit#gid=1942503775
    U_BIZ_CLITYPE = 105;   // udb定义的客户端类型,如需要了解具体定义请找周乐航
    U_IPV6 = 106;          //ipv6
  }
  map<uint32, uint64> intProp = 1;     //key -> int属性
  map<uint32, bytes> byteProp = 2;     //内层包、string
}
message ChannelInfo
{
  enum ChannelInfoKey {
    C_UNKONW = 0;
    C_TEMPLATE_ID = 1;      // 模版id
  }
  map<uint32, uint64> intProp = 1;
  map<uint32, bytes> byteProp = 2; // 内层包、string
}

//全量麦序列表
message MaixuList{
  repeated uint64 userlist = 1;
}

message ChannelMaixuEvent{
  uint64 timestamp  = 1; // pub的时间戳
  uint64 seqId      = 2; // 序号，递增
  uint64 topsid     = 3; // 频道号
  map<uint64, MaixuList> subsid2maixu = 4; // 子频道号对应麦序列表
  map<string, string> extends         = 5;
}
