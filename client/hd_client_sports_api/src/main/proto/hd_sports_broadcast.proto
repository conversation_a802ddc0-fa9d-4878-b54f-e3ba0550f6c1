syntax = "proto3";
import "uri.proto";     // 这个定义在项目目录下: https://git.yy.com/server/hudong/hd_api/-/tree/master/hd_client_base_api/src/main/proto/uri.proto
import "hd_sports_common.proto";
package com.yy.hd.api.pb.sports.broadcast;
option java_outer_classname = "BroadcastPb";

// mobserv 协议管理地址： https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/M-wK0zh99p/OYDSNC-7Jl/Ry9QCe1Pl9nabU
// 正式环境 15012 https://music-entmob-mgr.yy.com/admin/mobInfo
// 测试环境 15013 https://test-music-wudang-entmob-mgr.yy.com/admin/mobInfo
// 专门申请了一个 8288 用于广播通道， [1000 ~ 2000) 规划给 超能运动会

// 冲刺结果广播
message SprintResultBroadcast {
  option (uri.max) = 8288;
  option (uri.min) = 1000;

  repeated int64 animal_list = 1; // 动物冲刺排名
}

// 上一场助威冠军广播
message RoundChampionBroadcast {
  option (uri.max) = 8288;
  option (uri.min) = 1001;

  int64 uid            = 1;
  string nick          = 2;
  repeated com.yy.hd.api.pb.sports.common.PropsInfo props_list = 3;
  int64 target_uid     = 4; // 收礼人（发到包裹时不填）
  string target_nick   = 5; // 收礼人（发到包裹时不填）
  int64 timestamp      = 6;
  string nick_ext      = 7; // 多昵称 客户端自己适配
}

// 近10场运动会记录记录
message SportsTrendBroadcast {
  option (uri.max) = 8288;
  option (uri.min) = 1002;

  repeated com.yy.hd.api.pb.sports.common.SprintInfo trend_list = 1;
  string     nick_ext   = 2; // 多昵称 客户端做适配
}

// 动物升级广播
message AnimalLevelupBroadcast {
  option (uri.max) = 8288;
  option (uri.min) = 1003;

  repeated com.yy.hd.api.pb.sports.common.TrackInfo track_list      = 1;
}

// 结果公布 用户助威成功-通知（发放道具 奖励道具列表）
message RoundCheerRewardNotice {
  option (uri.max) = 8288;
  option (uri.min) = 1004;

  int64 uid                = 1;
  repeated com.yy.hd.api.pb.sports.common.PropsInfo props_list     = 2;
  int64 target_uid         = 3; // 收礼人（发到包裹时不填）
  string target_nick       = 4; // 收礼人（发到包裹时不填）
  int64 send_status        = 5; // 礼物送出状态 0-未发放 1-发包裹 2-送收礼人
  string nick_ext          = 6; // 多昵称 客户端做适配
}

// 结果公布 用户助威失败 通知
message RoundCheerFailedNotice {
  option (uri.max) = 8288;
  option (uri.min) = 1005;

  int64 uid               = 1;
  int64 outlay            = 2; // 花费 果实数
  repeated com.yy.hd.api.pb.sports.common.PropsInfo props_list    = 3; // 助威失败也可能发碎片
}

// 结果公布 成功助威排行榜
message RoundWinnerRankBroadcast {
  option (uri.max) = 8288;
  option (uri.min) = 1006;

  int64 animal_id         = 1; // 冠军动物id
  repeated com.yy.hd.api.pb.sports.common.RewardInfo top_list     = 2;
  int64 timestamp         = 3;
  string nick_ext         = 4; // 多昵称 客户端做适配
}

// 用户 活动果实变更 通知
message BalanceRefreshNotice {
  option (uri.max) = 8288;
  option (uri.min) = 1007;

  int64 uid               = 1;
  int64 balance           = 2; // 活动果实余额
  int64 timestamp         = 3;
}