syntax = "proto3";
import "http.proto";    // 这个定义在项目目录下: https://git.yy.com/server/hudong/hd_api/-/tree/master/hd_client_base_api/src/main/proto/http.proto
import "hd_sports_common.proto";
package com.yy.hd.api.pb.sports.web;
option java_outer_classname = "WebPb";

// 面向客户端的 http 接口协议定义
// 请客户端携带完整的请求头信息，服务端需要识别 : https://docs.google.com/spreadsheets/d/***************************-NWY_Sd5PX7h3baTo/edit?gid=56487091#gid=56487091
// x-fts-host-name、x-fts-platform、x-fts-business-type、x-fts-host-version、x-fts-game-type、x-fts-hdid

message ResponseHeader {
  sint32 resp_code = 1; // 0 成功
  string resp_msg  = 2;
}

// 获取初始化信息
message GetSportsInitInfoReq {
  option (http.url) = '/web/api/get_init_info';
  option (http.method) = GET;
  int64 sid   = 1; // required 用户当前所在sid
  int64 ssid  = 2; // required 用户当前所在ssid
}
message GetSportsInitInfoResp {
  ResponseHeader response         = 1;
  repeated com.yy.hd.api.pb.sports.common.TrackInfo track_list   = 2; // 各赛道上动物id和当前等级
  com.yy.hd.api.pb.sports.common.PlayStatus play_status          = 3; // 当前玩法状态
  repeated com.yy.hd.api.pb.sports.common.StageInfo stage_list   = 4; // 当前及以后阶段倒计时
  repeated int64 animal_list      = 5; // 游戏第3阶段，本轮动物冲刺排名
  int64 balance                   = 6; // 账户余额 果实数
  int64 timestamp                 = 7; // 当前服务器时间(秒) unix时间戳
  string my_nick                  = 8; // 当前用户昵称
  string my_avatar                = 9; // 当前用户头像
}

// 获取规则页信息，确保规则页展示的数据和后台一致
message GetRuleInfoReq {
  option (http.url) = '/web/api/get_rule_info';
  option (http.method) = GET;

  int64 sid   = 1; // required 用户当前所在sid
  int64 ssid  = 2; // required 用户当前所在ssid
}
message GetRuleInfoResp {
  ResponseHeader response = 1;
  repeated com.yy.hd.api.pb.sports.common.RulePrizeInfo  prize_list        = 2; // 礼物列表信息
  repeated com.yy.hd.api.pb.sports.common.RuleCandidateInfo candidate_list = 3; // 候选动物列表信息
}

// 助力频道主持 购买灵气券 赠送果实
message HelpReq {
  option (http.url) = '/web/api/help';
  option (http.method) = POST;

  int64 sid         = 1; // required 用户当前所在sid
  int64 ssid        = 2; // required 用户当前所在ssid
  int64 help_count  = 3; // required 助力的数量
  int64 help_from   = 4; // required 助力来源 1-助力页 2-玩法主页助威
}
message HelpResp {
  ResponseHeader response       = 1;
  int64 fruit                   = 2; // 获得的果实数量
  int64 help_value              = 3; // 助力值
  int64 help_count              = 4; // 助力个数
}

// 助力墙 被助力对象的助力值榜单(top20)
message HelpValueRankReq {
  option (http.url) = '/web/api/help_value_rank';
  option (http.method) = GET;

  int64 sid         = 1; // required 用户当前所在sid
  int64 ssid        = 2; // required 用户当前所在ssid
}
message HelpValueRankResp {
  ResponseHeader response             = 1;
  repeated com.yy.hd.api.pb.sports.common.RankItem top_list = 2; // 榜单列表
  com.yy.hd.api.pb.sports.common.RankItem compere_rank      = 3; // 当前主持榜单信息
}

// 获取用户本场助威信息,用户打开页面时候调用
message GetOwnRoundCheerInfoReq {
  option (http.url) = '/web/api/get_own_round_cheer_info';
  option (http.method) = GET;

  int64 sid   = 1; // required 用户当前所在sid
  int64 ssid  = 2; // required 用户当前所在ssid
}
message GetOwnRoundCheerInfoResp {
  ResponseHeader response       = 1;
  repeated com.yy.hd.api.pb.sports.common.TrackInfo track_list = 2; // 选择的动物列表及果实数目
  int64 outlay                  = 3; // 总花费 果实数
  int64 balance                 = 4; // 账户余额 果实数
  com.yy.hd.api.pb.sports.common.CheerExtraInfo extra_info     = 5; // 当前用户助威相关参数
  int64 round_start             = 6; // 当前轮次id
}

// 用户选择动物并助威请求
message ChooseAndCheerAnimalReq {
  option (http.url) = '/web/api/choose_and_cheer_animal';
  option (http.method) = POST;

  int64 animal_id           = 1; // 选择的动物id
  int64 outlay              = 2; // 花费 果实数
  com.yy.hd.api.pb.sports.common.CheerExtraInfo extra_info = 3; // 送礼相关参数
  int64 sid   = 4; // required 用户当前所在sid
  int64 ssid  = 5; // required 用户当前所在ssid
}
message ChooseAndCheerAnimalResp {
  ResponseHeader response   = 1;
  int64 outlay              = 2; // 总花费 果实数
  int64 balance             = 3; // 账户余额
}

// 获取用户助威记录信息
message GetOwnCheerHistoryReq {
  option (http.url) = '/web/api/get_own_cheer_history';
  option (http.method) = GET;

  int64 sid   = 1; // required 用户当前所在sid
  int64 ssid  = 2; // required 用户当前所在ssid
}

message GetOwnCheerHistoryResp {
  ResponseHeader response  = 1;
  repeated com.yy.hd.api.pb.sports.common.CheerInfo cheer_list     = 2;
}

// 获取上一场助威冠军
message GetRoundChampionInfoReq {
  option (http.url) = '/web/api/get_round_champion_info';
  option (http.method) = GET;

  int64 sid   = 1; // required 用户当前所在sid
  int64 ssid  = 2; // required 用户当前所在ssid
}

message GetRoundChampionInfoResp {
  ResponseHeader response       = 1;
  int64 uid                     = 2; // 获奖最多用户
  string nick                   = 3;
  repeated com.yy.hd.api.pb.sports.common.PropsInfo props_list = 4;
  int64 target_uid              = 5; // 收礼人（发到包裹时不填）
  string target_nick            = 6; // 收礼人（发到包裹时不填）
  int64 timestamp               = 7;
}

// 获取本场助威结果
message GetRoundCheerResultReq {
  option (http.url) = '/web/api/get_round_cheer_result';
  option (http.method) = GET;

  int64 sid   = 1; // required 用户当前所在sid
  int64 ssid  = 2; // required 用户当前所在ssid
}
message GetRoundCheerResultResp {
  ResponseHeader response  = 1;
  com.yy.hd.api.pb.sports.common.CheerInfo cheer_info     = 2;
}

// 获取近10场运动会夺冠记录
message GetSportsTrendRecordReq {
  option (http.url) = '/web/api/get_sports_trend_record';
  option (http.method) = GET;

  int64 sid   = 1; // required 用户当前所在sid
  int64 ssid  = 2; // required 用户当前所在ssid
}
message GetSportsTrendRecordResp {
  ResponseHeader response  = 1;
  repeated com.yy.hd.api.pb.sports.common.SprintInfo trend_list = 2;
}

// 成功助威排行
message GetRoundWinnerRankReq {
  option (http.url) = '/web/api/get_round_winner_rank';
  option (http.method) = GET;

  int64 sid   = 1; // required 用户当前所在sid
  int64 ssid  = 2; // required 用户当前所在ssid
}
message GetRoundWinnerRankResp {
  ResponseHeader response       = 1;
  int64 animal_id               = 2; // 冠军动物id
  repeated com.yy.hd.api.pb.sports.common.RewardInfo top_list  = 3;
  int64 timestamp               = 4;
}

// 获取近12小时30条 记录
message GetUserBigAwardRecordReq {
  option (http.url) = '/web/api/get_user_big_award_record';
  option (http.method) = GET;

  int64 sid   = 1; // required 用户当前所在sid
  int64 ssid  = 2; // required 用户当前所在ssid
}

message GetUserBigAwardRecordResp {
  ResponseHeader response           = 1;
  repeated com.yy.hd.api.pb.sports.common.RewardInfo reward_list   = 2;
}