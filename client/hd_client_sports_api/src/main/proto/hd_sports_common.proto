syntax = "proto3";
package com.yy.hd.api.pb.sports.common;
option java_outer_classname = "CommonPb";

enum PlayStatus {
  kPlayStatusNone     = 0; // 无效状态 开关关闭时返回
  kPlayStatusReady    = 1; // 参与期（冲刺前）-允许助威
  kPlayStatusSprint   = 2; // 锁定期（开始冲刺）
  kPlayStatusAnnounce = 3; // 公布结果期（决出冠军）
}

// 规则页面奖品信息
message RulePrizeInfo {
  string name       = 1; // 礼物名称
  string icon       = 2; // 礼物图标
}

// 规则页的候选选手信息
message RuleCandidateInfo {
  string name       = 1; // 动物名称
  string icon       = 2; // 动物图标
  string award_desc = 3; // 奖励描述
  float probability = 4; // 胜利概率, 比如 48.25123 就表示 48.25% 客户端 自己决定展示几位数
}

// 赛道信息
message TrackInfo {
  int64 id     = 1; // 动物ID（赛道ID） 1-6
  int64 level  = 2; // 当前动物等级
  int64 offset = 3; // 动物的初始位置
  int64 outlay = 4; // 当前用户花费 果实数
}

message StageInfo {
  PlayStatus play_status = 1;
  int64 remain_duration  = 2; // 剩余倒计时
  int64 total_duration   = 3; // 持续总时长
  int64 deadline         = 4; // 该阶段的截止时间戳
}

message CheerExtraInfo {
  int64 compere_uid = 1; // 当前直播间在麦主持uid
  int64 target_uid  = 2; // 当前选中uid
  int64 profit_uid  = 3; // 受益人uid
  int64 game_type   = 4; // 当前直播间玩法类型
  string expand     = 5; // required 客户端透传参数, 必须的
  string target_nick = 6; // 当前选中用户uid对应昵称
}

message CheerInfo {
  repeated int64 track_list = 1; // 选择的赛道列表
  int64 outlay              = 2; // 花费 果实数
  int64 animal_id           = 3; // 本轮冠军动物id
  repeated PropsInfo props_list      = 4;
  int64 target_uid          = 5; // 收礼人（发到包裹时不填）
  string target_nick        = 6; // 收礼人（发到包裹时不填）
  int64 send_status         = 7; // 礼物送出状态 0-未发放 1-发包裹 2-送收礼人
  int64 timestamp           = 8;
}

message PropsInfo {
  int64 props_id          = 1;
  string props_name       = 2;
  int64 props_count       = 3;
  int64 props_type        = 4; // 道具类型  0 营收道具 1 宝物合成道具 2-装扮碎片
  string props_icon       = 5; // 合成道具图标返回
}

// 冲刺信息
message SprintInfo {
  int64 animal_id      = 1; // 冠军动物id
  int64 timestamp      = 2;
  int64 champion_uid   = 3;
  string champion_nick = 4;
  int64 gain_amethyst  = 5; // 获得的紫水晶
}

message RewardInfo {
  int64 uid                     = 1;
  string nick                   = 2;
  repeated PropsInfo props_list = 3;
  int64 round_start             = 4;
}

// 榜单项结构
message RankItem {
  int64 uid       = 1; // uid
  string nick     = 2; // 昵称
  string avatar   = 3; // 头像
  int32 rank      = 4; // 排名
  int64 value     = 5; // 榜单数值
}
