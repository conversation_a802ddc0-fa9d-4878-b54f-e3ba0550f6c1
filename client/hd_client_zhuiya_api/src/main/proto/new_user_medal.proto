syntax = "proto3";
import "http.proto";

package com.yy.hd.api.pb.zhuiya.new_user_medal;
option java_outer_classname = "NewUserMedalWeb";

// 弹窗信息, 进频道n秒后, 待确认停留玩法无领奖弹窗后, 请求此接口
message PopupInfoReq {
  option (http.url) = '/web/newUserMedal/internal/popupInfo';
  option (http.method) = GET;
}
message PopupInfoRsp {
  int32 result = 1;
  string message = 2;
  PopupInfo data = 3;
}
message PopupInfo {
  bool popup = 1; // 是否弹窗
}

// 勋章进度, 进频道n秒后, 待确认无引导卡片后, 请求此接口
message LevelInfoReq {
  option (http.url) = '/web/newUserMedal/internal/levelInfo';
  option (http.method) = GET;
  bool checkExpire = 1; // 检查倒计时提醒
}
message LevelInfoRsp {
  int32 result = 1;
  string message = 2;
  LevelInfo data = 3;
}
message LevelInfo {
  bool showTip = 1; // 展示倒计时提醒, checkExpire=true时才会检查
  int32 level = 2; // 当前等级
  string expireTime = 3; // 过期时间
  int64 current = 4; // 当前进度
  int64 target = 5; // 任务目标
  repeated TaskInfo taskInfoList = 6; // 任务列表
}
message TaskInfo {
  int32 level = 1; // 等级
  string taskName = 2; // 任务名称
  string medalName = 3; // 勋章名称
  string medalUrl = 4; // 勋章小图
  string medalBigUrl = 5; // 勋章大图
  string description = 6; // 描述
  string buttonText = 7; // 按钮文案
  string jumpUrl = 8; // 跳转链接
}