syntax = "proto3";
import "common.proto";
import "http.proto";
import "game/game_common.proto";
package com.yy.hd.api.pb.game.web;
option java_outer_classname = "GameWeb";
/**
【上行请求】玩法通用协议，统一多人视频、多人视频pk、多人视频团战，客户端根据玩法类型来选择功能组件
功能列表：
1. 进房-获取玩法关键信息
2. 开始游戏 - 多人视频|多人视频pk 有此操作，多人视频乱斗是匹配成功主动开启的
3. 结束游戏 - 主持都有这个操作
4. 再来一场 - 多人视频乱斗的时候，这个才有，需要进行同意|拒绝操作
5. 主持上下座 - 这里不支持，客户端 调用房间服务 服务实现
6. 嘉宾加入&退出游戏，调用座位服务实现
8. 主持将嘉宾踢出游戏
9. 加时 - 乱斗需要进行同意|拒绝操作
10. 上报AR特效是否支持合并到视频流 - 本质上是上报嘉宾附加信息
11. 发起投降 - 目前仅仅多人视频乱斗有
12. 断开连麦请求 - 可能有同意|拒绝流程（目前仅多人视频乱斗有）
13. 添加|删除|获取跨频道音频UID - 仅乱斗有？
14. 获取&设置惩罚效果配置信息
15. 获取送礼贡献榜单
16. 获取对战实况 - 就是当前有哪些频道在pk中

自定义headers：https://docs.google.com/spreadsheets/d/***************************-NWY_Sd5PX7h3baTo/edit#gid=56487091
*/

// 进房-获取玩法关键信息
message GetKeyInfoReq {
  option (http.url) = '/api/game/channel/getKeyInfo';
  option (http.method) = GET;
  int64 sid = 1;
  int64 ssid = 2;
}
message GetKeyInfoRsp {
  com.yy.hd.api.pb.Result result = 1;
  com.yy.hd.api.pb.game.common.KeyInfo key_info = 2; // 玩法关键信息
}

// 开始游戏 多人视频|多人视频pk 有此操作，多人视频乱斗是匹配成功主动开启的
message StartGameReq {
  option (http.url) = '/api/game/channel/startGame';
  option (http.method) = POST;
  int64 sid = 1;
  int64 ssid = 2;
  string extend = 3; // 扩展信息
}
message StartGameRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 结束游戏
message StopGameReq {
  option (http.url) = '/api/game/channel/stopGame';
  option (http.method) = POST;
  int64 sid = 1;
  int64 ssid = 2;
}
message StopGameRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 邀请再来一场pk, 乱斗需要走 同意|拒绝流程（会发送 StartNewGameNotice 单播通知对方）
message StartNewGameReq {
  option (http.url) = '/api/game/channel/startNewGame';
  option (http.method) = POST;
  int64 sid = 1;
  int64 ssid = 2;
}
message StartNewGameRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 同意|拒绝 再来一场pk 目前乱斗需要, 会发给对方一个 StartNewGameConfirmNotice 单播
message StartNewGameConfirmReq {
  option (http.url) = '/api/game/channel/startNewGameConfirm';
  option (http.method) = POST;
  int64 sid = 1;
  int64 ssid = 2;
  bool confirm = 3; // true为同意, false为拒绝
}
message StartNewGameConfirmRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 加时请求 (目前乱斗需要对方同意，其他不需要，邀请单播：AddDurationReqNotice)
message AddDurationReq {
  option (http.url) = '/api/game/channel/addDuration';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
  int64 duration = 3; // 加时时长,秒为单位
}
message AddDurationRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 同意|拒绝 加时（仅乱斗需要,处理后客户端对方会收到 AddDurationReqConfirmNotice）
message AddDurationConfirmReq {
  option (http.url) = '/api/game/channel/addDurationConfirm';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
  bool confirm = 3; // true为同意, false为拒绝
}
message AddDurationConfirmRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 上报嘉宾是否支持 AR特效合并到视频流
message ReportArMixStatusReq {
  option (http.url) = '/api/game/channel/reportArMixStatus';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
  int32 ar_mix_status = 3; // 0--未定义  1--支持  2--不支持AR特效合到视频流
}
message ReportArMixStatusRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 发起投降 - 目前仅仅多人视频乱斗有（头像后会发布一个频道广播 GiveUpBroadcast）
message GiveUpReq {
  option (http.url) = '/api/game/channel/giveUp';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
}
message GiveUpRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 断开连麦请求 - 可能有同意|拒绝流程（目前仅多人视频乱斗有，会发确认通知：DisconnectReqNotice）
message RequestDisconnectReq {
  option (http.url) = '/api/game/channel/requestDisconnect';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
}
message RequestDisconnectRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 断开连麦确认结果 - 可能有同意|拒绝流程（目前仅多人视频乱斗有，会发结果通知：DisconnectConfirmNotice）
message ConfirmDisconnectReq {
  option (http.url) = '/api/game/channel/confirmDisconnect';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
  bool confirm = 3; // true为同意, false为拒绝
}
message ConfirmDisconnectRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 获取跨频道音频uid列表(目前仅乱斗有需要)
message GetVoiceUidReq {
  option (http.url) = '/api/game/channel/getVoiceUid';
  option (http.method) = GET;
  int64 sid   = 1;
  int64 ssid  = 2;
}
message GetVoiceUidRsp {
  com.yy.hd.api.pb.Result result = 1;
  repeated int64 uid_list = 2;
}

// 添加跨频道音频uid请求(目前仅乱斗有需要)
message AddVoiceUidReq {
  option (http.url) = '/api/game/channel/addVoiceUid';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
  int64 uid   = 3; // 要添加的uid
}
message AddVoiceUidRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 删除跨频道音频uid请求(目前仅乱斗有需要)
message DeleteVoiceUidReq {
  option (http.url) = '/api/game/channel/deleteVoiceUid';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
  int64 uid   = 3; // 要删除的uid
}
message DeleteVoiceUidRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 删除自己跨频道音频uid请求(目前仅乱斗有需要)
message DeleteMyVoiceUidReq {
  option (http.url) = '/api/game/channel/deleteMyVoiceUid';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
}

message DeleteMyVoiceUidRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 获取惩罚配置信息（目前仅多人视频团战&乱斗有）
message GetPunishmentCfgReq {
  option (http.url) = '/api/game/channel/getPunishmentCfg';
  option (http.method) = GET;
  int64 sid   = 1;
  int64 ssid  = 2;
}
message GetPunishmentCfgRsp {
  com.yy.hd.api.pb.Result result = 1;
  repeated com.yy.hd.api.pb.game.common.PunishmentCfg punishment_cfg_list = 2; // 惩罚配置列表
}

// 设置惩罚配置效果信息（目前仅多人视频团战&乱斗有）
message SetPunishmentReq {
  option (http.url) = '/api/game/channel/setPunishment';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
  int32 result = 3; // 惩罚的列表的位置， 从0开始的， 非惩罚的idx
}
message SetPunishmentRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 获取送礼贡献榜单
message GetContributorRankListReq {
  option (http.url) = '/api/game/channel/getContributorRankList';
  option (http.method) = GET;
  int64 sid = 1;
  int64 ssid = 2;
  int64 guest_uid = 3;  // 嘉宾uid
  int32 rank_type = 4;  // 榜单类型，0--正榜（按贡献数值倒序） 1--负榜（按贡献数值升序）
}
message GetContributorRankListRsp{
  com.yy.hd.api.pb.Result result = 1;
  repeated com.yy.hd.api.pb.game.common.ContributorRankInfo rank_list = 2; // 榜单列表
}

// 获取对战实况 - 就是当前有哪些频道在pk中
message GetBattleGameReq {
  option (http.url) = '/api/game/channel/getBattleGame';
  option (http.method) = GET;
  int64 sid = 1;
  int64 ssid = 2;
}
message GetBattleGameRsp {
  com.yy.hd.api.pb.Result result = 1;
  repeated com.yy.hd.api.pb.game.common.BattleGameInfo game_info_list = 2;
}

// 拉取动画配置资源 - 乱斗
message GetAnimCfgReq {
  option (http.url) = '/api/game/channel/getAnimCfg';
  option (http.method) = GET;
}
message GetAnimCfgResp {
  com.yy.hd.api.pb.Result result = 1;
  string animMatch = 2;
  string animWin = 3;
  string animFail = 4;
}