syntax = "proto3";
import "uri.proto";
import "game/game_common.proto";
package com.yy.hd.api.pb.game.broadcast;
option java_outer_classname = "GameBc";

// 玩法关键信息关播
message KeyInfoBroadcast {
  option (uri.max) = 8188;
  option (uri.min) = 1000;
  com.yy.hd.api.pb.game.common.KeyInfo key_info = 1; // 玩法关键信息
}

// 邀请再来一场 通知对方弹窗,是否确认再来一次
message StartNewGameNotice {
  option (uri.max) = 8188;
  option (uri.min) = 1001;
  int64 inviter_uid = 1; // 邀请人uid
}

// 邀请再来一场结果通知
message StartNewGameConfirmNotice {
  option (uri.max) = 8188;
  option (uri.min) = 1002;
  int64 confirm_uid = 1; // 确认uid
  bool confirm = 2; // true为同意, false为拒绝
}

// 申请加时单播, 通知对方确认，乱斗加时需要走这个流程
message AddDurationReqNotice {
  option (uri.max) = 8188;
  option (uri.min) = 1003;
  int64 req_uid   = 1; // 申请人uid
  int64 duration  = 2; // 申请加时多少，单位：秒
}

// 申请加时结果通知单播, 通知对方确认加时处理结果，乱斗加时需要走这个流程
message AddDurationReqConfirmNotice {
  option (uri.max) = 8188;
  option (uri.min) = 1004;
  int64 confirm_uid   = 1; // 确认人uid
  bool confirm = 2; // true为同意, false为拒绝
}

// 发起投降后的通知(目前仅 多人视频乱斗有)
message GiveUpBroadcast {
  option (uri.max) = 8188;
  option (uri.min) = 1005;
  int64 uid = 1;                    // 投降的UID
  int64 sid = 2;                    // 投降的频道
  int64 ssid = 3;                   // 投降的子频道
  int64 mvp_uid = 4;                // mvp uid
  int32 punish_select_duration = 5; // 用户自选惩罚的时间(单位秒)
  bool show_punish = 6;             // 是否展示惩罚选择框 true-展示 false-不展示
  int32 winTeam    = 7;             // 胜利队伍
}

// 断开连麦请求 通知单播
message DisconnectReqNotice {
  option (uri.max) = 8188;
  option (uri.min) = 1006;
}

// 断开连麦请求 处理结果通知
message DisconnectConfirmNotice {
  option (uri.max) = 8188;
  option (uri.min) = 1007;
  int64 confirm_uid   = 1; // 确认人uid
  bool confirm = 2; // true为同意, false为拒绝
}

// 同步声音的uid变化广播
message ChannelVoiceUidBroadcast {
  option (uri.max) = 8188;
  option (uri.min) = 1008;
  repeated int64 uids = 1; // uid列表
}

// 倒计时广播
message CountdownBroadcast {
  option (uri.max) = 8188;
  option (uri.min) = 1009;
  int32 countdown = 1;  // 倒计时秒数
  int64 everybodyLoveUid = 2; // 笑脸
  string everybodyLoveAnimation = 3; // 笑脸资源
  int64 nobodyLoveUid = 4; // 哭
  string nobodyLoveAnimation = 5; // 哭资源
}

// 胜负结果广播(多人团战 多人乱斗)
message WinningInfoBroadcast {
  option (uri.max) = 8188;
  option (uri.min) = 1010;
  int32 winningResult = 1;        // 胜负结果 1-胜 2-负 0-平
  int32 team = 2;                 // 胜利队伍 用于团战同一房间渲染胜负
  int32 punishSelectDuration = 3; // 用户自选惩罚的时间(单位秒); 为0无需展示, 大于0展示对应时间
  int64 mvpUid = 4;               // mvp uid
  bool showPunish = 5;            // 是否展示惩罚选择框 true-展示 false-不展示
}

// 跨厅pk匹配玩法广播（比如多人视频乱斗）
message CrossPkMatchBroadcast {
  option (uri.max) = 8188;
  option (uri.min) = 1011;
  com.yy.hd.api.pb.game.common.BattleTeamInfo team1 = 1; // team1 = 1;
  com.yy.hd.api.pb.game.common.BattleTeamInfo team2 = 2;
  int64 count_down = 3; // 倒计时, 目前10秒,0秒
}

// 复活嘉宾座位广播
message SeatReviveBroadcast {
  option (uri.max) = 8188;
  option (uri.min) = 1012;
  int64 pos = 1; // 复活的嘉宾座位
}

// 消息通知广播 用于乱斗提醒"本场乱斗送礼加时已达4次..."
message MessageNotifyBroadcast
{
  option (uri.max) = 8188;
  option (uri.min) = 1013;
  string  msg = 1;  // 通知消息
}