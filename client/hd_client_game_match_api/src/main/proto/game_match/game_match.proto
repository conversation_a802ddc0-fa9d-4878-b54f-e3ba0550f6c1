syntax = "proto3";
import "common.proto";
import "http.proto";
import "game_match/game_match_common.proto";
package com.yy.hd.api.pb.game.match.web;
option java_outer_classname = "GameMatchWeb";

/**
功能列表一览：
1. 获取匹配推荐列表
2. 邀请匹配
3. 接受|拒绝匹配
4. 查询|设置是否接受匹配
*/

// 获取匹配推荐列表 - 会根据当前频道状态来进行推荐
message GetRecommendReq {
  option (http.url) = '/api/gameMatch/channel/getRecommend';
  option (http.method) = GET;
  int64 sid = 1;
  int64 ssid = 2;
}

message GetRecommendRsp {
  com.yy.hd.api.pb.Result result = 1;
  repeated com.yy.hd.api.pb.game.match.common.RecommendItem item_list = 2; // 推荐列表
}

// 邀请匹配
message InviteMatchReq {
  option (http.url) = '/api/gameMatch/channel/inviteMatch';
  option (http.method) = POST;
  int64   sid               = 1; // 请求用户当前所在频道 sid
  int64   ssid              = 2; // 请求用户当前所在频道 ssid
  int64   imid              = 3; // 目标主持yy号
  int32   game_type         = 4; // 邀请进行什么玩法匹配
  repeated int64 uid_list   = 5; // 邀请对战时所选择的嘉宾uid列表(邀请方)
}

message InviteMatchRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 邀请匹配确认请求 同意|拒绝
message InviteMatchConfirmReq {
  option (http.url) = '/api/gameMatch/channel/inviteMatchConfirm';
  option (http.method) = POST;
  com.yy.hd.api.pb.game.match.common.InviteInfo    invite_info = 1;
  repeated int64 uid_list = 2; // 接受时的嘉宾列表  多人视频乱斗 最多3人
  int32    confirm        = 3; // 0-拒绝 1-同意
}
message InviteMatchConfirmRsp {
  com.yy.hd.api.pb.Result result = 1;
}

// 查询是否接受匹配邀请
message GetRecvMatchInviteStatusReq {
  option (http.url) = '/api/gameMatch/channel/getRecvMatchInviteStatus';
  option (http.method) = GET;
  int64 sid       = 1; // 请求用户当前所在频道 sid
  int64 ssid      = 2; // 请求用户当前所在频道 ssid
}

message GetRecvMatchInviteStatusRsp {
  com.yy.hd.api.pb.Result result = 1;
  int32 status = 2; // 0 接受邀请   1 不接受邀请
}

// 设置是否接受匹配邀请
message SetRecvMatchInviteStatusReq {
  option (http.url) = '/api/gameMatch/channel/setRecvMatchInviteStatus';
  option (http.method) = POST;
  int64 sid           = 1; // 请求用户当前所在频道 sid
  int64 ssid          = 2; // 请求用户当前所在频道 ssid
  int32 status        = 3; // 0 接受邀请   1 不接受邀请
}
message SetRecvMatchInviteStatusRsp {
  com.yy.hd.api.pb.Result result = 1;
}
