syntax = "proto3";
import "common.proto";
import "uri.proto";
import "game_match/game_match_common.proto";
package com.yy.hd.api.pb.game.match.broadcast;
option java_outer_classname = "GameMatchBc";

// 主持的战队信息，只显示魅力值最多2个嘉宾
message FightTeamGuestInfo
{
  sint64 uid = 1;            //嘉宾的uid
  string avatar = 2;         //嘉宾的头像
}

// 匹配战况
message FightTeamInfo
{
  sint64 uid = 1;            //主持的uid
  string avatar = 2;         //主持的头像
  string nick = 3;
  sint64 charm = 4;
  sint64 asid = 5;
  sint64 sid = 6;
  sint64 ssid = 7;
  sint64 end_time = 8;        //结束时间
  sint64 imid = 9;            //yy号
  sint32 compere_tier = 10;   //主持的段位等级
  sint32 compere_stars = 11;   //主持段位星星
  repeated FightTeamGuestInfo team_guest_info = 12; //主持的战队信息，只显示魅力值最多2个嘉宾
}

// 邀请匹配通知单播
message InviteMatchReqNotice {
  option (uri.max) = 8188;
  option (uri.min) = 2000;
  int32 game_type     = 1; // 玩法类型
  com.yy.hd.api.pb.game.match.common.InviteInfo    invite_info = 2;
  FightTeamInfo                           team_info = 3;
}

// 邀请匹配结果通知
message InviteMatchConfirmNotice {
  option (uri.max) = 8188;
  option (uri.min) = 2001;
  int32 result        = 1; // 确认接收邀请结果, 1为同意, 0为拒绝, 2 对方确认超时
  int32 game_type     = 2; // 玩法类型
}

// 跨频道PK公屏广播
message CrossPkPublicScreenBroadcast {
  option (uri.max) = 8188;
  option (uri.min) = 2002;
  sint64 sid           = 1; // 来源频道
  sint64 ssid          = 2; // 来源子频道
  sint64 uid           = 3; // 来源用户ID
  sint64 public_time   = 4; // 发布时间：毫秒
  string content       = 5; // 公屏广播内容,json格式, 这个和 fts_public_screen.proto 中的 ChannelFightPublicScreenBroadcast 一致
}