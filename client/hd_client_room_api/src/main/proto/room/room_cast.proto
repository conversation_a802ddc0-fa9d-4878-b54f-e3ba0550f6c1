syntax = "proto3";
package com.yy.hd.api.pb.room;
option java_outer_classname = "RoomCast";

import "uri.proto";
import "common.proto";
import "room/room_common.proto";

// 房间信息变更广播
message RoomInfoUpdateCast {
  option (uri.max) = 8088;
  option (uri.min) = 8001;
  RoomType room_type = 1;         // 房间类型, 交友, 语音房
  GameType game_type = 2;         // 新玩法类型
  GameType old_game_type = 3;     // 旧玩法类型
  RoomInfo room_info = 4;         // 房间基础信息
  CompereInfo compere_info = 5;   // 主持信息
  CompereInfo compere_right = 6;  // 右侧主持信息
  BgInfo bg_new = 7;              // 新背景信息
  BgInfo bg_default = 8;          // 新默认背景信息
  int32 big_video = 9;            // 1-为开启大视频模式
  int64 duration = 10;            // 游戏时长, 秒
  int64 activity_expire_time = 11;
  LayoutType layoutType = 12;     // 布局类型, 废弃
  string layoutConfig = 13;       // 布局配置
  int64 seq = 20;                 // 序列号, 以大的为准, 玩法类型/基础信息/主持信息的变更广播是分开的
}

// 嘉宾上座单播给主持
message GuestOnlineUnicast {
  option (uri.max) = 8088;
  option (uri.min) = 8002;
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 频道ssid
  int64 uid = 3;              // 嘉宾uid
  int32 pos = 4;              // 嘉宾位置
  GameType game_type = 5;     // 当前玩法
}