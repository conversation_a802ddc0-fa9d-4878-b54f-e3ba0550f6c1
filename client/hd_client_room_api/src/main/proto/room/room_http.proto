syntax = "proto3";
package com.yy.hd.api.pb.room;
option java_outer_classname = "RoomHttp";

import "http.proto";
import "common.proto";
import "room/room_common.proto";

// 查询房间信息
message RoomInfoReq {
  option (http.url) = '/api/room/roomInfo';
  option (http.method) = GET;
  int64 sid = 1;                // 频道sid, 支持短号
  int64 ssid = 2;               // 频道ssid, 传0时代表顶级频道
  int32 template_id = 3;        // 频道模板id, 传进频道信令返回的模板id
}
message RoomInfoResp {
  Result result = 1;
  RoomType room_type = 2;         // 房间类型, 交友, 语音房
  GameType game_type = 3;         // 玩法类型, 相亲, 乱斗等
  RoomInfo room_info = 4;         // 房间基础信息
  CompereInfo compere_info = 5;   // 主持信息
  CompereInfo compere_right = 6;  // 右侧主持信息
  BgInfo bg_new = 7;              // 新背景信息
  BgInfo bg_default = 8;          // 新默认背景信息
  int32 big_video = 9;            // 1-为开启大视频模式
  int64 duration = 10;            // 游戏时长, 秒
  int64 activity_expire_time = 11;
  LayoutType layoutType = 12;     // 布局类型, 废弃
  string layoutConfig = 13;       // 布局配置
  int64 seq = 20;                 // 序列号, 以大的为准, 玩法类型/基础信息/主持信息的变更广播是分开的
}

// 切换玩法
message SetGameTypeReq {
  option (http.url) = '/api/room/gameType/set';
  option (http.method) = POST;
  int64 sid = 1;                    // 频道sid
  int64 ssid = 2;                   // 频道ssid
  GameType game_type = 3;           // 玩法类型

  repeated int32 seat_sex_list = 4; // 相亲座位性别
  int64 duration = 5;               // 持续时间, 秒
  int32 theme_id = 6;               // 主题id
}
message SetGameTypeResp {
  Result result = 1;
}

// 设置视频模式
message SetVideoModeReq {
  option (http.url) = '/api/room/videoMode/set';
  option (http.method) = POST;
  int64 sid = 1;                    // 频道sid
  int64 ssid = 2;                   // 频道ssid
  int32 videoMode = 3;              // 视频模式 0-无 1-主持位
}
message SetVideoModeResp {
  Result result = 1;
}

// 视频开播检查
message StartVideoCheckReq {
  option (http.url) = '/api/room/video/check';
  option (http.method) = GET;
  int64 sid = 1;                    // 频道sid
  int64 ssid = 2;                   // 频道ssid
}
message StartVideoCheckResp {
  Result result = 1;
}

// 上主持位
message OnCompereReq {
  option (http.url) = '/api/room/compere/online';
  option (http.method) = POST;
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 频道ssid
  int64 duration = 3;         // 持续时间，秒 (无用)
}
message OnCompereResp{
  Result result = 1;
}

// 下主持位
message OffCompereReq {
  option (http.url) = '/api/room/compere/offline';
  option (http.method) = POST;
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 频道ssid
}
message OffCompereResp {
  Result result = 1;
}

// 上主持位检查
message OnCompereCheckReq {
  option (http.url) = '/api/room/compere/check';
  option (http.method) = GET;
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 频道ssid
}
message OnCompereCheckResp {
  Result result = 1;
}

enum RoomAuth {
  Auth_None = 0; // 无身份
  Auth_Contract = 1; // 签约主持
  Auth_RoomOw = 2; // 房主
}

// 查询用户房间身份
message GetRoomAuthReq {
  option (http.url) = '/api/room/auth/get';
  option (http.method) = GET;
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 频道ssid
}

message GetRoomAuthResp {
  Result result = 1;
  RoomAuth auth = 2;  // 房间身份
}

// 上嘉宾位检查
message OnGuestCheckReq {
  option (http.url) = '/api/room/guest/check';
  option (http.method) = POST;
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 频道ssid
  int32 pos = 3;              // 位置, 从0开始, -1代表不选
  string systemInfo = 4;      // systemInfo (移动端传)
}
message OnGuestCheckResp{
  Result result = 1;
}

// 上嘉宾位
message OnGuestReq {
  option (http.url) = '/api/room/guest/online';
  option (http.method) = POST;
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 频道ssid
  int32 pos = 3;              // 位置, 从0开始, -1代表不选
  int32 onlineType = 4;       // 0-视频 1-音频
  string systemInfo = 5;      // systemInfo (移动端传)
}
message OnGuestResp{
  Result result = 1;
}

// 下嘉宾位
message OffGuestReq {
  option (http.url) = '/api/room/guest/offline';
  option (http.method) = POST;
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 频道ssid
}
message OffGuestResp {
  Result result = 1;
}

// 主持踢嘉宾
message KickGuestReq {
  option (http.url) = '/api/room/guest/kick';
  option (http.method) = POST;
  int64 sid   = 1;
  int64 ssid  = 2;
  int64 uid   = 3;            // 被踢uid
}
message KickGuestResp {
  Result result = 1;
  int32 pos = 2;
}

// 新多人视频配置
message DatingNewVideoConfigReq {
  option (http.url) = '/api/room/dating/videoConfig';
  option (http.method) = GET;
  int64 sid = 1;
  int64 ssid = 2;
}
message DatingNewVideoConfigResp {
  Result result = 1;
  int32 status = 2;             // 多人入口状态 1-可见 0-不可见
  bool can_audio_online = 3;    // 是否允许音频连线
}
