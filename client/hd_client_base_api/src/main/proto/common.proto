syntax = "proto3";
package com.yy.hd.api.pb;
option java_outer_classname = "Common";

/**
 * 通用响应码
 */
enum RetCode {
  SUCCESS                   = 0; // 成功
  INVALID_INPUT             = 400; // 参数有误
  UNAUTHORIZED              = 401; // 未登录
  SYSTEM_ERROR              = 500; // 系统异常
}

/**
 * 通用返回结果类
 */
message Result {
  int32   code    = 1; // 0-成功, 其他异常
  string  message = 2; // 一般为失败提示
}

message ChannelId {
  int64 sid = 1;
  int64 ssid = 2;
}

enum Sex {
  FEMALE = 0;         // 女
  MALE = 1;           // 男
}

enum Role {
  ROLE_NONE = 0;                // 无角色
  COMPERE = 1;                  // 主持
  GUEST = 2;                    // 嘉宾
  CANDIDATE = 3;                // 候选
  USER = 4;                     // 用户
}

// 房间玩法
enum GameType {
  GAME_Dating                       = 0;  // 相亲
  GAME_TeamFight                    = 2;  // 团战
  GAME_ChannelFight                 = 7;  // 乱斗匹配
  GAME_VideoDating                  = 8;  // 多人视频交友
  GAME_VideoPK                      = 18; // 多人视频Pk
  GAME_VideoFightMatch              = 19; // 多人视频乱斗
  GAME_ZhuiWan                      = 21; // 代表追玩，不是真玩法
  GAME_InteractiveContent           = 22; // 交友互动内容，未上线
  GAME_DuoTeamFight                 = 23; // 双人团战新
  GAME_VerticalScreen               = 24; // 竖屏玩法 弹幕游戏
  GAME_VideoDatingNew               = 25; // 新多人视频交友
  GAME_VideoPKNew                   = 26; // 新多人视频Pk
  GAME_VideoFightMatchNew           = 27; // 新多人视频乱斗

  VOICE_Party                       = 30; // 语音房派对
  VOICE_Ktv                         = 31; // 语音房Ktv
  VOICE_MiniGame                    = 32; // 语音房小游戏
  VOICE_TeamFight                   = 33; // 语音房团战
  VOICE_Pk                          = 34; // 语音房跨厅pk
  VOICE_UgcGameLive                 = 35; // ugc赛事直播


  GAME_NONE                         = 999; // 无玩法
}

// 业务生态
enum RoomType {
  ROOM_NONE = 0;
  ROOM_DATING = 1;   // 交友
  ROOM_ZHUIYA = 2;   // 语音房
  ROOM_BASE = 3;     // 基础频道
}

enum Platform {
  PLATFORM_NONE = 0;
  PLATFORM_PC = 1;
  PLATFORM_WEB = 2;
  PLATFORM_ANDROID = 3;
  PLATFORM_IOS = 4;
  PLATFORM_ANDROID_PAD = 5;
  PLATFORM_IPAD = 6;
  PLATFORM_WIN_PHONE = 7;
  PLATFORM_HARMONY = 11;
}

// 客户端信息
message ClientInfo {
  string hostName = 1;
  string version = 2;
  string hdid = 3;
  string pkg = 4;
  Platform platform = 5; // 参考 Platform
  string ip = 6;
}