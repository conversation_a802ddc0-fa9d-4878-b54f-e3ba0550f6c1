syntax = "proto3";
package com.yy.hd.api.pb.room;

import "event.proto";

// 玩法变更事件
message GameTypeUpdateEvent {
  option (event.topic)        = 'hd_room_game_type_update';
  string seqId                = 1; // 唯一序列号
  int64 timestamp             = 2; // 事件时间戳(毫秒)
  int64 sid                   = 3; // 频道sid
  int64 ssid                  = 4; // 子频道ssid
  int32 roomType              = 5; // 房间类型 1-交友 2-语音房
  int64 opUid                 = 6; // 操作人uid
  int64 compereUid            = 7; // 主持uid
  int64 duration              = 8; // 持续时间, 秒
  int32 gameType              = 9; // 新玩法类型
  int32 oldGameType           = 10;// 旧玩法类型
  repeated int64 guestUids    = 11;// 嘉宾列表
}

// 主持变更事件
message CompereUpdateEvent {
  option (event.topic)        = 'hd_room_compere_update';
  string seqId                = 1; // 唯一序列号
  int64 timestamp             = 2; // 事件时间戳(毫秒)
  int64 sid                   = 3; // 频道sid
  int64 ssid                  = 4; // 子频道ssid
  int32 roomType              = 5; // 房间类型 1-交友 2-语音房
  int64 uid                   = 6; // 主持uid
  bool online                 = 7; // 是否上麦
  int32 gameType              = 8; // 当前玩法类型
  int32 pos                   = 9; // 主持位置
  int64 sitTime               = 10;// 上座时间
  OffSeatReason reason        = 13;// 下座原因
}

// 嘉宾变更事件
message GuestUpdateEvent {
  option (event.topic)        = 'hd_room_guest_update';
  string seqId                = 1; // 唯一序列号
  int64 timestamp             = 2; // 事件时间戳(毫秒)
  int64 sid                   = 3; // 频道sid
  int64 ssid                  = 4; // 子频道ssid
  int32 roomType              = 5; // 房间类型 1-交友 2-语音房
  int64 compereUid            = 6; // 主持uid
  int64 uid                   = 7; // 嘉宾uid
  bool online                 = 8; // 是否上麦
  int32 gameType              = 9; // 当前玩法类型
  int32 pos                   = 10;// 嘉宾位置
  int64 sitTime               = 11;// 上座时间
  int32 seat_scene            = 12;// 0-用户上座 1-乱斗开始
  OffSeatReason reason        = 13;// 下座原因
}

enum OffSeatReason {
  NONE = 0;                 // ...
  QUIT = 1;                 // 主动下座
  GAME_TYPE_CHANGE = 2;     // 切换玩法
  KICK_OUT = 3;             // 踢下座
  ESCAPE_TARGET_ROOM = 4;   // 强制下座
  LEAVE_ROOM = 5;           // 离开房间
}
