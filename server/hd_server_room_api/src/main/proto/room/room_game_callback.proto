syntax = "proto3";
package com.yy.hd.api.pb.room;

import "common.proto";

message SetGameTypeCheckReq {
  RoomType roomType = 1;      // 房间类型
  GameType gameType = 2;      // 玩法类型
  int64 compereUid = 3;       // 主持uid
  int64 sid = 4;              // 频道sid
  int64 ssid = 5;             // 频道ssid
  GameType oldGameType = 6;   // 旧玩法类型
  int64 duration = 7;
  int64 oldDuration = 8;
  ClientInfo clientInfo = 9;
}

message SetGameTypeCheckResp {
  Result result = 1;
}

message OnGuestCheckReq {
  RoomType roomType = 1;  // 房间类型
  GameType gameType = 2;  // 玩法类型
  int64 compereUid = 3;   // 主持uid
  int64 sid = 4;          // 频道sid
  int64 ssid = 5;         // 频道ssid
  int64 uid = 6;          // 上座uid
  int32 pos = 7;          // 位置, 从0开始, -1代表不选
  ClientInfo clientInfo = 8;
  string systemInfo = 9;
}
message OnGuestCheckResp {
  Result result = 1;
  int32 pos = 2;          // 返回指定位置
}

message KickGuestCheckReq {
  RoomType roomType = 1;  // 房间类型
  GameType gameType = 2;  // 玩法类型
  int64 compereUid = 3;   // 主持uid
  int64 sid = 4;          // 频道sid
  int64 ssid = 5;         // 频道ssid
  int64 uid = 6;          // 被踢uid
  int32 pos = 7;          // 位置
  ClientInfo clientInfo = 8;
}
message KickGuestCheckResp {
  Result result = 1;
}

// 玩法服务实现
service RoomGameCallbackYrpc {

  // 设置玩法检查
  rpc setGameTypeCheck (SetGameTypeCheckReq) returns (SetGameTypeCheckResp);

  // 上嘉宾位检查
  rpc onGuestCheck (OnGuestCheckReq) returns (OnGuestCheckResp);

  // 踢嘉宾位检查
  rpc kickGuestCheck (KickGuestCheckReq) returns (KickGuestCheckResp);

}