syntax = "proto3";
package com.yy.hd.api.pb.room;
option java_outer_classname = "RoomYrpc";

import "common.proto";
import "room/room_common.proto";

message RoomKeyInfoReq {
  int64 sid = 1;          // 顶级频道, 不支持短号
  int64 ssid = 2;         // 子频道
  int64 uid = 3;          // 用户uid, 尽量传
}
message RoomKeyInfoResp {
  Result result = 1;
  RoomType roomType = 2;  // 房间类型
  GameType gameType = 3;  // 玩法类型
  int64 compereUid = 4;   // 主持uid
  int64 duration = 5;     // 玩法时长
}

message CompereKeyInfoReq {
  int64 uid = 1;            // 根据uid查询主持信息
}
message CompereKeyInfoResp {
  Result result = 1;
  int64 sid = 2;            // 频道sid
  int64 ssid = 3;           // 频道ssid
  RoomType roomType = 4;    // 房间类型
  GameType gameType = 5;    // 玩法类型
}

message SyncGameTypeReq {
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 子频道
  int64 uid = 3;              // 主持uid
  GameType gameType = 4;      // 玩法类型
  GameType oldGameType = 5;   // 旧玩法类型
  int64 duration = 6;         // 时长
}
message SyncGameTypeResp {
  Result result = 1;
}

message SyncCompereReq {
  int64 sid = 1;          // 频道sid
  int64 ssid = 2;         // 子频道
  int64 uid = 3;          // 主持uid
  GameType gameType = 4;  // 玩法类型
  bool online = 5;        // 上下麦
  RoomType roomType = 6;  // 房间类型
  int64 duration = 7;     // 时长
}
message SyncCompereResp {
  Result result = 1;
}

message GetSeatInfoReq {
  int64 sid = 1;
  int64 ssid = 2;
  repeated int64 uid = 3;
  int32 role = 4;
}
message GetSeatInfoResp {
  Result result = 1;
  repeated SeatKeyInfo seatInfo = 2;
}
message SeatKeyInfo {
  int64 uid = 1;                    // 用户uid
  int32 pos = 2;                    // 用户座位号
  int32 role = 3;                   // 角色
}

enum SeatScene {
  NORMAL = 0;
  CROSS_PK_START = 1;
}

message OnGuestReq {
  int64 sid = 1;              // 频道sid
  int64 ssid = 2;             // 频道ssid
  int64 uid = 3;              // 用户uid
  repeated int32 pos = 4;     // 位置, 从0开始, 可提供多个尝试
  int32 oldRole = 5;          // 原角色
  SeatScene scene = 6;
}
message OnGuestResp {
  Result result = 1;
  int32 pos = 2;              // 上座成功的位置
}

message BatchOnGuestReq {
  int64 sid = 1;                // 频道sid
  int64 ssid = 2;               // 频道ssid
  map<int64, int32> uidPos = 3; // 用户-位置map
  SeatScene scene = 4;
}
message BatchOnGuestResp {
  Result result = 1;
  map<int64, int32> uidPos = 2; // 上座成功的用户-位置map
}

message BatchOffGuestReq {
  int64 sid = 1;                // 频道sid
  int64 ssid = 2;               // 频道ssid
  repeated int64 uid = 3;       // uid列表
  bool clearAll = 4;            // 清空所有嘉宾
  SeatScene scene = 5;          // 下座场景
  repeated int64 ignoreRoleChangeUid = 6; // 要忽略发布roleChange事件的uid列表（兼容旧版本乱斗匹配，旧版本选择嘉宾去乱斗，匹配成功后旧版本PC模板切换玩法过程中，收到了角色变成用户的话就会导致视频不会转发到对方频道，因此这个场景下，这里可以忽略特定uid发roleChange事件 @林永保）
}
message BatchOffGuestResp {
  Result result = 1;
  map<int64, int32> uidPos = 2;     // 下座成功的用户-位置map
  map<int64, int64> uidSitTime = 3; // 下座成功的用户-上座时间map
}

message QueryChannelWhitelistReq {
  int64 sid = 1;
  int64 ssid = 2;
}
message QueryChannelWhitelistResp {
  Result result = 1;
  bool inWhitelist = 2;
  bool audioOnline = 3;
}

// 主持上座请求
message CompereOnlineReq {
  int64 uid = 1;  // 上座主持uid
  int64 seat = 2; // 上座位置 0-左主持（主主持位） 1-有主持（暂无）
  int64 sid = 3;  // 频道sid
  int64 ssid = 4; // 频道ssid
  int64 gameType = 5; // 玩法类型
  int64 duration = 6; // 时长，秒，好像没啥用？？
  int64 smallIp = 7; // 小端ip
}
message CompereOnlineResp{
  Result result = 1;
}

// 修正玩法类型请求
message FixGameTypeReq {
  int64 uid = 1; // 操作人uid
  int64 sid = 2; // sid
  int64 ssid = 3; // 子频道
  bool forceTriggerReloadTemplate = 4; // 身份强制触发频道内用户重新加载模板
}

// 交友新多人视频白名单配置
message DatingVideoConfig {
  int64 sid = 1;     // 顶级频道，必须>0
  int64 ssid = 2;    // 子频道，可以为0
  bool canAudio = 3; // 是否允许音频连线
}

// 添加交友新多人视频白名单
message AddDatingVideoConfigReq {
  int64 uid     = 1; // 操作人
  bool override = 2; // 是否覆盖原来的配置，默认是不覆盖
  repeated DatingVideoConfig config = 3; // 要添加的名单列表
}

// 切换玩法
message SetGameTypeReq {
  int64 sid = 1;                    // 频道sid
  int64 ssid = 2;                   // 频道ssid
  GameType game_type = 3;           // 玩法类型

  repeated int32 seat_sex_list = 4; // 相亲座位性别
  int64 duration = 5;               // 持续时间, 秒
  int32 theme_id = 6;               // 主题id
  int64 uid = 7;                    // 操作人uid
  int64 compereUid = 8;             // 主持uid
}
message SetGameTypeResp {
  Result result = 1;
}

message BatchRoomGameTypeReq {
  repeated ChannelId channelId = 1;
}

message BatchRoomGameTypeResp {
  Result result = 1;
  repeated RoomGameType roomGameType = 2;
}

message RoomGameType {
  int64 sid = 1;
  int64 ssid = 2;
  GameType gameType = 3;
  LayoutType layoutType = 4; // 废弃
  int32 videoMode = 5;
}

message BroadcastRoomInfoReq {
  int64 sid = 1;
  int64 ssid = 2;
}

// 获取交友多人视频白名单
message GetDatingVideoConfigReq {

}

message GetDatingVideoConfigResp {
  repeated DatingVideoConfig config = 1;
}

// 互动房间服务
service RoomYrpcApi {

  // 查询房间关键信息
  rpc roomKeyInfo (RoomKeyInfoReq) returns (RoomKeyInfoResp);

  // 查询主持所在频道
  rpc compereKeyInfo (CompereKeyInfoReq) returns (CompereKeyInfoResp);

  // 玩法上座 (不执行玩法检查)
  rpc OnGuest (OnGuestReq) returns (OnGuestResp);

  // 批量玩法上座 (不执行玩法检查)
  rpc BatchOnGuest (BatchOnGuestReq) returns (BatchOnGuestResp);

  // 批量玩法下座 (不执行玩法检查)
  rpc BatchOffGuest (BatchOffGuestReq) returns (BatchOffGuestResp);

  // 批量查询房间gameType
  rpc BatchRoomGameType (BatchRoomGameTypeReq) returns (BatchRoomGameTypeResp);

}

// 交友兼容接口
service RoomFtsYrpcApi {
  // 同步玩法类型 (fts_game_center) 走旧服务切换玩法时同步到新服务
  rpc syncGameType (SyncGameTypeReq) returns (SyncGameTypeResp);
  // 同步主持信息 (fts_game_center) 旧玩法走旧服务上下麦
  rpc syncCompere (SyncCompereReq) returns (SyncCompereResp);
  // 查询座位区信息 (fts_cross_pk)
  rpc GetSeatInfo (GetSeatInfoReq) returns (GetSeatInfoResp);
  // 主持上座请求 (fts_game_center 检查是新玩法后直接路由过来的，pc端领取奖励底层会调用，改动太大了，服务端路由兼容)
  rpc compereOnline (CompereOnlineReq) returns (CompereOnlineResp);
  // 修正玩法类型
  rpc fixGameType(FixGameTypeReq) returns (Result);
  // 广播房间信息
  rpc broadcastRoomInfo(BroadcastRoomInfoReq) returns (Result);
}

// 交友新多人视频白名单管理
service DatingVideoConfigYrpcApi {
  // 添加新多人视频白名单
  rpc addDatingVideoConfig(AddDatingVideoConfigReq) returns (Result);

  // 获取所有的新多人视频白名单
  rpc getDatingVideoConfig(GetDatingVideoConfigReq) returns (GetDatingVideoConfigResp);
}

// s2s: hd_room_yrpc