namespace java com.yy.hd.api.thrift.room

struct Result {
    1:i32 code              // 0-成功, 其他失败
    2:string message        // 一般为失败提示
}

enum Platform {
    kPlatformPC = 1,
    kPlatformWeb = 2,
    kPlatformAndroid = 3,
    kPlatformIOS = 4,
}

struct ClientInfo {
    1:Platform platform
    2:string version
    3:string hdid
    4:string ip
    5:string host_name          // x-fts-host-name
    6:string systemInfo
    7:string trace_id
}

struct GetKeyInfoReq {
    1:i64 sid
    2:i64 ssid
    3:i64 uid
}

struct BgInfo {
    1:string url;           // bg的前缀 背景地址
    2:string color;         // bg color 背景颜色
    3:string rankColor;     // 榜单颜色
    4:string gameBg;        // 中间背景图（默认背景时有值）
    5:string giftAreaColor; // 礼物区域颜色
    6:i64    revision;      // 更新的版本号，用于刷新cdn及缓存资源
    7:i64    theme_id;      // 当前模板主题id
    8:string gradientColor; // 渐变颜色
    9:string channelBgUrl;  // 纯色底图（默认背景时有值）
}

struct LBSInfo
{
  1: string lbs_city;
  2: double lat;
  3: double lng;
  4: string lbs_province;
}

struct CompereInfo
{
    1:i64 uid;
    2:i64 status; // 0--座位显示或可用  1--座位不显示或不可用
    3:i64 sign_ch; // 签约频道
    4:LBSInfo lbs; // 多人类玩时下发主持城市信息
}

struct GameCenterKeyInfo
{
   1:i32 game_type; // 玩法id
   2:i64 activity_expire_time; // 活动过期的时间戳, 过期,活动被自动清理
   3:CompereInfo compere_info; // 主要主持,(双人团战时,左侧)
   4:CompereInfo compere_right; // 右侧主持
   5:i32        big_video; // 1--为开启大视频模式
   6:BgInfo     bg_info; // 背景信息
   7:BgInfo     bg_new; // 新背景信息
   8:BgInfo     bg_default; // 新默认背景信息
}

struct H5Config
{
    1:i32    game_type; // 枚举类型不灵活，不使用枚举类型做为玩法的定义
    2:string h5_url;    // url
    3:string version;   // 待加载的版本
    4:i64    timestamp; // 版本配置的时间戳
    5:string version2;  // 待加载的版本（全web化）
}

struct DatingKeyInfo {
    1:GameCenterKeyInfo key_info; // 关键信息
    2:list<H5Config> h5_config; // // h5相关配置，不在白名单频道的不返回
}

struct GetKeyInfoResp
{
    1:Result resp;
    2:DatingKeyInfo datingKeyInfo
}

struct DatingCompereExt {
    1:i64 seat                  // 座位号 0-左 1-右
    2:i64 duration              // 时长, 秒
}

struct CompereOnlineReq {
    1:i64 sid
    2:i64 ssid
    3:i64 uid
    4:i32 gameType
    5:bool online               // 上下
    6:DatingCompereExt datingExt
}

struct GuestOnlineReq {
    1:i64 sid
    2:i64 ssid
    3:i64 uid
    4:i32 gameType
    5:bool online               // 上下
    6:i32 pos                   // 座位号
}

struct DatingGameTypeExt {
    1:i64 duration              // 时长，单位秒
    2:i32 theme_id              // 主题
    3:list<i32> seat_sex_list   // 座位性别
}

struct SetGameTypeReq {
    1:i64 sid
    2:i64 ssid
    3:i64 uid
    4:i32 gameType
    5:DatingGameTypeExt datingExt
}

struct RoleChangeReq {
    1:i64 sid
    2:i64 ssid
    3:i64 uid
    4:i32 gameType
    5:i32 preRole
    6:i32 role
    7:i64 timestamp
    8:i32 sex
}

/* 业务回调接口
 * 1. 客户端进入频道，查询玩法类型和房间基本信息，如果 hd-room 查不到的话，那么 hd-room 就会调用到这里 GetKeyInfoReq 进行查询玩法类型/主持
 * 2. 玩法切换:
 *    2.1 hd-room 切换到旧玩法，hd-room 调用 SetPlay 进行切换(直接路由到旧玩法服务)
 *    2.2 hd-room 切换到新玩法，调用 fts_game_center.checkSetGameType 业务检查，hd-room 处理完成后，调用 fts_game_center.syncGameType 同步数据（这里不做设置检查，也会发旧玩法切换广播和kafka）
 * 3. 主持:
 *    3.1 hd-room 主持上下座（cul，定时任务）如果是旧玩法则拦截，调用 fts_game_center.checkCompereOnline 进行业务上座检查， 同步调用 fts_game_center syncCompere（直接执行上下座，也会发一个旧上座事件）
 *    3.2 fts_game_center 主持上下座 如果是新玩法则拦截（cul，定时任务），同步调用 hd-room.syncCompere 上下座
 **/
service RoomCallbackService {

    // 查询房间关键信息
    GetKeyInfoResp GetKeyInfo(1:GetKeyInfoReq req, 2:ClientInfo ext)

    // 切换玩法
    Result SetPlay(1:SetGameTypeReq req, 2:ClientInfo ext)

    // 切换玩法业务检查
    Result CheckSetGameType(1:SetGameTypeReq req, 2:ClientInfo clientInfo)

    // 同步玩法
    Result SyncPlay(1:SetGameTypeReq req, 2:ClientInfo ext)

    // 主持上麦业务检查
    Result CheckCompereOnline(1:CompereOnlineReq req, 2:ClientInfo clientInfo)

    // 同步主持
    Result SyncCompere(1:CompereOnlineReq req, 2:ClientInfo clientInfo)

    // 角色变更通知
    Result RoleChange(1:RoleChangeReq req, 2:ClientInfo clientInfo)

    // 修复玩法类型接口（新旧多人切换期间，用于hd-room 进行玩法修正） PS：该接口直接操作redis，不会发任何事件和广播
    Result fixGameType(1:SetGameTypeReq req)

    // 修复主持UID接口，PS：该接口直接操作redis，不会发任何事件和广播
    Result fixCompereUID(1:CompereOnlineReq req)

}
