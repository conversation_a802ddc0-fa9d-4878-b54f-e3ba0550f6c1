package com.yy.hd.api.yrpc.room;

import com.yy.hd.api.pb.room.RoomGameCallback;
import org.apache.dubbo.common.annotation.Yrpc;

/**
 * 各玩法服务实现
 *
 * <AUTHOR> 2024/12/4
 */
public interface RoomGameCallbackYrpcApi {

    /**
     * 设置玩法回调
     */
    @Yrpc(functionName = "setGameTypeCheck")
    RoomGameCallback.SetGameTypeCheckResp setGameTypeCheck(RoomGameCallback.SetGameTypeCheckReq req);

    /**
     * 嘉宾上座回调
     */
    @Yrpc(functionName = "onGuestCheck")
    RoomGameCallback.OnGuestCheckResp onGuestCheck(RoomGameCallback.OnGuestCheckReq req);

    /**
     * 踢嘉宾回调
     */
    @Yrpc(functionName = "kickGuestCheck")
    RoomGameCallback.KickGuestCheckResp kickGuestCheck(RoomGameCallback.KickGuestCheckReq req);

}
