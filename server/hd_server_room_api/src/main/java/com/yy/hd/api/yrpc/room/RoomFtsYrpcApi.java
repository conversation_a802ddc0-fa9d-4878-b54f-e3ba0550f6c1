package com.yy.hd.api.yrpc.room;

import com.yy.hd.api.pb.Common;
import com.yy.hd.api.pb.room.RoomYrpc;
import org.apache.dubbo.common.annotation.Yrpc;

/**
 * 交友兼容过渡
 *
 * <AUTHOR> 2024/12/10
 */
public interface RoomFtsYrpcApi {

    /**
     * fts_game_center切换玩法同步到新服务
     */
    @Yrpc(functionName = "syncGameType")
    RoomYrpc.SyncGameTypeResp syncGameType(RoomYrpc.SyncGameTypeReq req);

    /**
     * fts_game_center主持上座同步到新服务
     */
    @Yrpc(functionName = "syncCompere")
    RoomYrpc.SyncCompereResp syncCompere(RoomYrpc.SyncCompereReq req);

    /**
     * 批量查询座位信息
     */
    @Yrpc(functionName = "GetSeatInfo")
    RoomYrpc.GetSeatInfoResp GetSeatInfo(RoomYrpc.GetSeatInfoReq req);

    /**
     * 主持上座请求 (fts_game_center 检查是新玩法后直接路由过来的，pc端领取奖励底层会调用，改动太大了，服务端路由兼容)
     */
    @Yrpc(functionName = "compereOnline")
    RoomYrpc.CompereOnlineResp compereOnline(RoomYrpc.CompereOnlineReq req);

    /**
     * 修正玩法类型
     */
    @Yrpc(functionName = "fixGameType")
    Common.Result fixGameType(RoomYrpc.FixGameTypeReq req);

    /**
     * 在线频道 - 广播房间信息
     */
    @Yrpc(functionName = "broadcastRoomInfo")
    Common.Result broadcastRoomInfo(RoomYrpc.BroadcastRoomInfoReq req);

}
