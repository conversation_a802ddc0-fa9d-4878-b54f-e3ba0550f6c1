package com.yy.hd.api.yrpc.room;

import com.yy.hd.api.pb.Common;
import com.yy.hd.api.pb.room.RoomYrpc;
import org.apache.dubbo.common.annotation.Yrpc;

/**
 * s2s: hd_room_yrpc
 *
 * <AUTHOR> 2024/11/22
 */
public interface RoomYrpcApi {

    /**
     * 房间关键信息 (玩法, 主持)
     */
    @Yrpc(functionName = "roomKeyInfo")
    RoomYrpc.RoomKeyInfoResp roomKeyInfo(RoomYrpc.RoomKeyInfoReq req);

    /**
     * 主持关键信息 (频道)
     */
    @Yrpc(functionName = "compereKeyInfo")
    RoomYrpc.CompereKeyInfoResp compereKeyInfo(RoomYrpc.CompereKeyInfoReq req);

    /**
     * 嘉宾上座 (无检查)
     */
    @Yrpc(functionName = "OnGuest")
    RoomYrpc.OnGuestResp OnGuest(RoomYrpc.OnGuestReq req);

    /**
     * 批量嘉宾上座 (无检查)
     */
    @Yrpc(functionName = "BatchOnGuest")
    RoomYrpc.BatchOnGuestResp BatchOnGuest(RoomYrpc.BatchOnGuestReq req);

    /**
     * 批量嘉宾下座 (无检查)
     */
    @Yrpc(functionName = "BatchOffGuest")
    RoomYrpc.BatchOffGuestResp BatchOffGuest(RoomYrpc.BatchOffGuestReq req);

    /**
     * 设置 gameType
     */
    @Yrpc(functionName = "setGameType")
    default RoomYrpc.SetGameTypeResp setGameType(RoomYrpc.SetGameTypeReq req) {
        return RoomYrpc.SetGameTypeResp.newBuilder().build();
    }

    /**
     * 批量查询房间gameType
     */
    @Yrpc(functionName = "BatchRoomGameType")
    RoomYrpc.BatchRoomGameTypeResp BatchRoomGameType(RoomYrpc.BatchRoomGameTypeReq req);

}
