package com.yy.hd.api.yrpc.room;

import com.yy.hd.api.pb.Common;
import com.yy.hd.api.pb.room.RoomYrpc;
import org.apache.dubbo.common.annotation.Yrpc;

/**
 * 交友新多人视频白名单管理
 * <AUTHOR>
 * @since 2025/2/21 17:47
 */
public interface DatingVideoConfigYrpcApi {

    /**
     * 添加新多人视频白名单
     */
    @Yrpc(functionName = "addDatingVideoConfig")
    Common.Result addDatingVideoConfig(RoomYrpc.AddDatingVideoConfigReq req);

    /**
     * 添加新多人视频白名单
     */
    @Yrpc(functionName = "getDatingVideoConfig")
    RoomYrpc.GetDatingVideoConfigResp getDatingVideoConfig(RoomYrpc.GetDatingVideoConfigReq req);
}
