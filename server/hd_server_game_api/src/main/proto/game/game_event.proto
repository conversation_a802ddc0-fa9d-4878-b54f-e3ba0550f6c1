syntax = "proto3";
package com.yy.hd.api.pb.room;

import "event.proto";

// 玩法活动结束事件
message GameActivityEndEvent {
  option (event.topic)              = 'hd_game_activity_end';
  string seqId                      = 1; // 唯一序列号
  int64 timestamp                   = 2; // 事件时间戳(毫秒)
  int64 sessionId                   = 3; // 活动session id
  int32 roomType                    = 4; // 房间类型 1-交友 2-语音房
  int32 gameType                    = 5; // 新玩法类型
}
