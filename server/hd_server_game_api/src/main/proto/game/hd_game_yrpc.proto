syntax = "proto3";
package com.yy.hd.api.pb.game;
option java_outer_classname = "GamePb";

import "common.proto";
import "game/game_common.proto";

// 推荐状态信息
message RecommendStatusInfo {
  int64 sid = 1;
  int64 ssid = 2;
  int32 status = 3; // 游戏状态
  int64 charm = 4; // 魅力值
}

message BatchGetRecommendStatusInfoReq {
  int32 roomType = 1;
  repeated ChannelId channelList = 2;
}

message BatchGetRecommendStatusInfoRsp {
  Result result = 1;
  repeated RecommendStatusInfo infoList = 2;
}

message PkTeamInfo {
  int64 uid        = 1;
  int64 sid        = 2;
  int64 ssid       = 3;
  int32 gameType   = 4; // 当前玩法类型
  repeated int64 guestList  = 5; // 迁移的嘉宾uid列表
}

// 开启跨厅pk
message StartCrossPkReq {
  int64      uid         = 1;
  int64      sid         = 2;
  int64      ssid        = 3;
  PkTeamInfo inviterTeam = 4; // 邀请人队伍信息
  PkTeamInfo inviteeTeam = 5; // 被邀请人队伍信息
  int32      matchMode   = 6; // 邀请进行什么玩法匹配（旧多人视频乱斗：19, 新多人视频乱斗: 27）
}

// 跨厅pk乱斗相关信息
message CrossPkInfo {
  int32 roomType  = 1; // 房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
  int32 gameType  = 5; // 玩法类型
  int64 matchSid  = 6; // 匹配频道
  int64 matchSsid = 7; // 匹配子频道
  int64 matchUid  = 8; // 匹配用户uid
  int64 matchGameType = 9; // 匹配用户玩法类型
  int64 status    = 10; // 状态: 0 -- 未定义  1 -- 游戏进行中  2 -- 惩罚中  3 -- 结束
}

// 批量获取跨厅pk乱斗相关信息
message BatchGetCrossPkInfoReq {
  int32 roomType  = 1; // 业务房间类型
  repeated ChannelId channelList = 2; // 要查询的平道列表
}

message BatchGetCrossPkInfoRsp {
  Result result = 1;
  repeated CrossPkInfo pkInfoList = 2;
}

// 批量获取跨厅pk频道付费魅力值
message BatchGetCrossPkChannelPayedCharmReq {
  int32 roomType  = 1; // 业务房间类型
  repeated ChannelId channelList = 2; // 要查询的频道列表
}

// 频道魅力值
message ChannelCharm {
  ChannelId channel = 1;
  int64 charm = 2;
}

message BatchGetCrossPkChannelPayedCharmRsp {
  Result result = 1;
  repeated ChannelCharm chCharmList = 2;
}

// 检查跨厅pk跨频道音频uid状态
message CheckCrossPkVoiceUidReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
}

message CheckCrossPkVoiceUidRsp {
  Result result = 1;
  int32 status = 2; // 状态 1-是频道音频uid 0-不是频道音频uid
}

// 批量获取跨厅pk各个频道下嘉宾的魅力值信息
message BatchGetCrossPkGuestPayedCharmReq {
  int32 roomType  = 1; // 业务房间类型
  int64 gameType  = 2; // 玩法类型
  repeated ChannelId channelList = 3; // 要查询的频道列表
}

message GuestCharm {
  int64 uid = 1;
  int64 charm = 2;
}

message ChannelGuestCharm {
  ChannelId channel = 1;
  repeated GuestCharm guestCharmList = 2;
}

message BatchGetCrossPkGuestPayedCharmRsp {
  Result result = 1;
  repeated ChannelGuestCharm chGuestCharmList = 2;
}

// 请求再来一场跨厅pk 返回 状态：0-邀请成功 1-已经邀请过了 2-上一场活动不存在
message ApplyStartNewCrossPkReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
}

message ApplyStartNewCrossPkRsp {
  Result result = 1;
}

// 同意|拒绝 再来一场跨厅pk
message ConfirmStartNewCrossPkReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
  bool accept     = 5; // true-同意 false-不同意
}

message ConfirmStartNewCrossPkRsp {
  Result result = 1; // result.code -> -- 0-ok  1-timeout   2- matchinfo not exist 3－活动信息不存在 4-活动进行中
  PkTeamInfo pkTeamInfo = 2; // 对手信息
}

// 发起投降
message GiveUpCrossPkReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
}

message GiveUpCrossPkRsp {
  Result result = 1; // result.code --> 0 success  1 less than 5 minute    2 activity not exist 3 activity not start 4 other err
  PkTeamInfo pkTeamInfo = 2; // 对手信息
}

// 申请加时
message ApplyAddDurationReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
  int64 duration  = 5; // 加多长时间，单位：秒
}

message ApplyAddDurationRsp {
  Result result = 1; // result.code 0 success  1 less than 5 minute    2 activity not exist 3 activity not start 4 other err
}

// 确认加时
message ConfirmAddDurationReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
  bool accept     = 5; // true-同意 false-不同意
}

message ConfirmAddDurationRsp {
  Result result = 1;  // result.code 0 ok  1--加时已经超时  2--活动已经结束 3 活动不存在 4 已经达到最大加时次数
}

// 停止乱斗,当对方主持下麦超过1分钟后可以调用
message StopCrossPkReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
}

message StopCrossPkRsp {
  Result result = 1; // result.code 0 success  1 less than 1 minute    2 activity not exist 3 activity not start 4 other err
}

// 主持断开连麦确认及通知
message DisconnectReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
}

message DisconnectRsp {
  Result result = 1; // result.code 0 ok  1--exist(当前已经有发起的申请了) 2--活动不存在  3--(超过当天发起次数)  4 -- 每场只能发起一起
}

// 同意|拒绝 主持断开连麦
message ConfirmDisconnectReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
  bool accept     = 5; // true-同意 false-不同意
}

message ConfirmDisconnectRsp {
  Result result = 1; // result.code 0 ok  1--已经超时  2--活动不存在
}

// 获取跨频道音频uids
message GetCrossPkVoiceUidReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid 操作者uid
}

message GetCrossPkVoiceUidRsp {
  Result result = 1;
  repeated int64 uidList = 2;
}

// 添加跨频道音频uid
message AddCrossPkVoiceUidReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
}

message AddCrossPkVoiceUidRsp {
  Result result = 1;
}

// 删除跨频道音频uid
message DelCrossPkVoiceUidReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
}

message DelCrossPkVoiceUidRsp {
  Result result = 1;
}

// 设置惩罚结果
message SetPunishmentResultReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
  int64 result    = 5; // 结果
}

message SetPunishmentResultRsp {
  Result result = 1; // result.code 0 success  1--activity not exist 2--activity is starting  3--activity is end 4--guest info not exist
}

// 获取惩罚配置信息
message GetPunishmentCfgReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
}

message GetPunishmentCfgRsp {
  Result result = 1;
  repeated com.yy.hd.api.pb.game.common.PunishmentCfg punishmentCfgList = 2; // 惩罚配置列表
}

// 获取多人PK本场嘉宾|主持贡献榜
message GetContributorRankListReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 主持|嘉宾 uid
  int32 rankType  = 5; // 榜单类型，0--正榜（按魅力值降序） 1--负榜（按魅力值升序）
  ClientInfo client = 6; // 客户端信息
}

message ContributorItem {
  int32 rank     = 1; // 排名
  int64 uid      = 2; // 贡献者uid
  int64 amount   = 3; // 贡献礼物价值
  string avatar  = 4;
  string nick    = 5;
}

message GetContributorRankListRsp {
  Result result = 1;
  repeated ContributorItem contributorList = 2;
}

// 上报嘉宾是否支持 AR特效合并到视频流
message ReportArMixStatusReq {
  int64 sid   = 1;
  int64 ssid  = 2;
  int64 uid   = 3;
  int32 ar_mix_status = 4; // 0--未定义  1--支持  2--不支持AR特效合到视频流
}
message ReportArMixStatusRsp {
  Result result = 1;
}

// 获取玩法关键信息
message GetKeyInfoReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
  ClientInfo client = 5; // 客户端信息
}
message GetKeyInfoRsp {
  Result result = 1;
  com.yy.hd.api.pb.game.common.KeyInfo keyInfo = 2; // 玩法关键信息
}

// 获取跨厅pk匹配战况
message GetCrossPkBattleTeamRankReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid
  int32 size      = 5; // 要返回多少条记录
  repeated int32 gameTypeList = 6; // 要返回哪些匹配玩法类型
  ClientInfo client = 7; // 客户端信息
}

message GetCrossPkBattleTeamRankRsp {
  Result result = 1;
  repeated com.yy.hd.api.pb.game.common.BattleGameInfo gameInfoList = 2; // 匹配战况列表
}

// 获取给定uid可以上座的空闲座位列表
message GetAllowOnSeatPosReq {
  int32 roomType  = 1; // 业务房间类型
  int64 sid       = 2; // 频道
  int64 ssid      = 3; // 子频道
  int64 uid       = 4; // 用户uid（可以为0）
  Role role       = 5; // 希望上座的角色
}

message GetAllowOnSeatPosRsp {
  Result result       = 1;
  bool alreadyOnSeat  = 2; // 当前是否已经在座位上了
  int32 curSeat       = 3; // 当前所在座位
  repeated int32 seatList = 4; // 空闲的座位号列表
}

// 嘉宾位置信息
message GuestPosInfo {
  int64 uid = 1; // 嘉宾uid
  int32 pos = 2; // 位置信息，从0开始
}

message ChannelGuestInfo {
  ChannelId channel = 1;
  repeated GuestPosInfo guest = 2;
}

// 批量获取频道的嘉宾列表
message BatchGetChannelGuestReq {
  int32 roomType = 1; // 房间类型
  int64 gameType = 2; // 玩法类型
  repeated ChannelId channel = 3;
}

message BatchGetChannelGuestRsp {
  Result result       = 1;
  repeated ChannelGuestInfo info = 2;
}

// 频道游戏会话信息
message ChannelSessionInfo {
  int64 sid = 1;
  int64 ssid = 2;
  int32 roomType = 3; // 房间类型
  int64 gameType = 4; // 玩法类型
  int32 status   = 5; // 游戏状态 0-游戏不存在|未开启游戏 1-进行中 2-惩罚中 3-游戏结束
  int64 startTime = 6; // 本场游戏开始时间：秒
  int64 exceptedEndTime = 7; // 本场游戏预期结束时间：秒
  int64 pkSid = 8; // 乱斗对手sid
  int64 pkSsid = 9; // 乱斗对手ssid
}

// 批量获取频道游戏会话基本信息
message BatchGetActiveChannelSessionInfoReq {
  int32 roomType = 1;
  repeated ChannelId channel = 2;
}
message BatchGetActiveChannelSessionInfoRsp {
  Result result       = 1;
  repeated ChannelSessionInfo info = 2;
}


// s2sname: hd_game_server_yrpc
service HdGameServerProvider {
  // 批量获取房间玩法推荐状态信息
  rpc batchGetRecommendStatusInfo(BatchGetRecommendStatusInfoReq) returns (BatchGetRecommendStatusInfoRsp);

  // 匹配完成开始游戏(乱斗玩法)
  rpc startCrossPk(StartCrossPkReq) returns (Result);

  // 批量获取跨厅pk乱斗相关信息
  rpc batchGetCrossPkInfo(BatchGetCrossPkInfoReq) returns (BatchGetCrossPkInfoRsp);

  // 批量获取跨厅pk频道付费魅力值
  rpc batchGetCrossPkChannelPayedCharm(BatchGetCrossPkChannelPayedCharmReq) returns (BatchGetCrossPkChannelPayedCharmRsp);

  // 检查跨厅pk跨频道音频uid状态
  rpc checkCrossPkVoiceUid(CheckCrossPkVoiceUidReq) returns (CheckCrossPkVoiceUidRsp);

  // 批量获取跨厅pk各个频道下嘉宾的魅力值信息
  rpc batchGetCrossPkGuestPayedCharm(BatchGetCrossPkGuestPayedCharmReq) returns (BatchGetCrossPkGuestPayedCharmRsp);

  // 请求再来一场跨厅pk 返回 result.code 状态：0-邀请成功 1-已经邀请过了 2-上一场活动不存在
  // 会给对方用户发送邀请单播通知（注意，如果对方是旧玩法，那么要调用 fts-video-fight 发送邀请单播协议）
  rpc applyStartNewCrossPk(ApplyStartNewCrossPkReq) returns (ApplyStartNewCrossPkRsp);

  // 同意|拒绝 再来一场跨厅pk
  rpc confirmStartNewCrossPk(ConfirmStartNewCrossPkReq) returns (ConfirmStartNewCrossPkRsp);

  // 发起投降
  rpc giveUpCrossPk(GiveUpCrossPkReq) returns (GiveUpCrossPkRsp);

  // 申请加时
  rpc applyAddDuration(ApplyAddDurationReq) returns (ApplyAddDurationRsp);

  // 确认加时
  rpc confirmAddDuration(ConfirmAddDurationReq) returns (ConfirmAddDurationRsp);

  // 停止乱斗,当对方主持下麦超过1分钟后可以调用
  rpc stopCrossPk(StopCrossPkReq) returns (StopCrossPkRsp);

  // 主持断开连麦确认及通知
  rpc disconnect(DisconnectReq) returns (DisconnectRsp);

  // 同意|拒绝 主持断开连麦
  rpc confirmDisconnect(ConfirmDisconnectReq) returns (ConfirmDisconnectRsp);

  // 获取跨频道音频uids
  rpc getCrossPkVoiceUid(GetCrossPkVoiceUidReq) returns (GetCrossPkVoiceUidRsp);

  // 添加跨频道音频uid
  rpc addCrossPkVoiceUid(AddCrossPkVoiceUidReq) returns (AddCrossPkVoiceUidRsp);

  // 删除跨频道音频uid
  rpc delCrossPkVoiceUid(DelCrossPkVoiceUidReq) returns (DelCrossPkVoiceUidRsp);

  // 设置惩罚结果
  rpc setPunishmentResult(SetPunishmentResultReq) returns (SetPunishmentResultRsp);

  // 获取惩罚配置列表
  rpc getPunishmentCfg(GetPunishmentCfgReq) returns (GetPunishmentCfgRsp);

  // 获取多人PK本场嘉宾|主持贡献榜
  rpc getContributorRankList(GetContributorRankListReq) returns (GetContributorRankListRsp);

  // 上报嘉宾是否支持 AR特效合并到视频流
  rpc reportArMixStatus(ReportArMixStatusReq) returns (ReportArMixStatusRsp);

  // 获取玩法关键信息
  rpc getKeyInfo(GetKeyInfoReq) returns (GetKeyInfoRsp);

  // 获取跨厅pk匹配战况
  rpc getCrossPkBattleTeamRank(GetCrossPkBattleTeamRankReq) returns (GetCrossPkBattleTeamRankRsp);

  // 获取给定uid可以上座的空闲座位列表
  rpc getAllowOnSeatPos(GetAllowOnSeatPosReq) returns (GetAllowOnSeatPosRsp);

  // 获取频道嘉宾位置列表
  rpc batchGetChannelGuest(BatchGetChannelGuestReq) returns (BatchGetChannelGuestRsp);

  // 批量获取活跃频道游戏会话基本信息
  rpc batchGetActiveChannelSessionInfo(BatchGetActiveChannelSessionInfoReq) returns (BatchGetActiveChannelSessionInfoRsp);
}