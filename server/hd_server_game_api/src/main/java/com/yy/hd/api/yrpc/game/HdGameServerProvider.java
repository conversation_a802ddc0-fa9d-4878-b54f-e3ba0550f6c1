package com.yy.hd.api.yrpc.game;

import static com.yy.hd.api.pb.Common.*;
import static com.yy.hd.api.pb.game.GamePb.*;

import org.apache.dubbo.common.annotation.Yrpc;

/**
 * s2sname: hd_game_server_yrpc

 * <AUTHOR>
 * @since 2024/12/17 15:07
 */
public interface HdGameServerProvider {

    /**
     * 批量获取房间玩法推荐状态信息
     */
    @Yrpc(functionName = "batchGetRecommendStatusInfo")
    BatchGetRecommendStatusInfoRsp batchGetRecommendStatusInfo(BatchGetRecommendStatusInfoReq req);

    /**
     * 匹配完成开始游戏(乱斗玩法)
     */
    @Yrpc(functionName = "startCrossPk")
    Result startCrossPk(StartCrossPkReq req);

    /**
     * 批量获取跨厅pk乱斗相关信息
     */
    @Yrpc(functionName = "batchGetCrossPkInfo")
    BatchGetCrossPkInfoRsp batchGetCrossPkInfo(BatchGetCrossPkInfoReq req);

    /**
     * 批量获取跨厅pk频道付费魅力值
     */
    @Yrpc(functionName = "batchGetCrossPkChannelPayedCharm")
    BatchGetCrossPkChannelPayedCharmRsp batchGetCrossPkChannelPayedCharm(BatchGetCrossPkChannelPayedCharmReq req);

    /**
     * 检查跨厅pk跨频道音频uid状态
     */
    @Yrpc(functionName = "checkCrossPkVoiceUid")
    CheckCrossPkVoiceUidRsp checkCrossPkVoiceUid(CheckCrossPkVoiceUidReq req);

    /**
     * 批量获取跨厅pk各个频道下嘉宾的魅力值信息
     */
    @Yrpc(functionName = "batchGetCrossPkGuestPayedCharm")
    BatchGetCrossPkGuestPayedCharmRsp batchGetCrossPkGuestPayedCharm(BatchGetCrossPkGuestPayedCharmReq req);

    /**
     * 请求再来一场跨厅pk 返回 result.code 状态：0-邀请成功 1-已经邀请过了 2-上一场活动不存在
     * 会给对方用户发送邀请单播通知（注意，如果对方是旧玩法，那么要调用 fts-video-fight 发送邀请单播协议）
     */
    @Yrpc(functionName = "applyStartNewCrossPk")
    ApplyStartNewCrossPkRsp applyStartNewCrossPk(ApplyStartNewCrossPkReq req);

    /**
     * 同意|拒绝 再来一场跨厅pk
     */
    @Yrpc(functionName = "confirmStartNewCrossPk")
    ConfirmStartNewCrossPkRsp confirmStartNewCrossPk(ConfirmStartNewCrossPkReq req);

    /**
     * 发起投降
     */
    @Yrpc(functionName = "giveUpCrossPk")
    GiveUpCrossPkRsp giveUpCrossPk(GiveUpCrossPkReq req);

    /**
     * 申请加时
     */
    @Yrpc(functionName = "applyAddDuration")
    ApplyAddDurationRsp applyAddDuration(ApplyAddDurationReq req);

    /**
     * 确认加时
     */
    @Yrpc(functionName = "confirmAddDuration")
    ConfirmAddDurationRsp confirmAddDuration(ConfirmAddDurationReq req);

    /**
     * 停止乱斗,当对方主持下麦超过1分钟后可以调用
     */
    @Yrpc(functionName = "stopCrossPk")
    StopCrossPkRsp stopCrossPk(StopCrossPkReq req);

    /**
     * 主持断开连麦确认及通知
     */
    @Yrpc(functionName = "disconnect")
    DisconnectRsp disconnect(DisconnectReq req);

    /**
     * 同意|拒绝 主持断开连麦
     */
    @Yrpc(functionName = "confirmDisconnect")
    ConfirmDisconnectRsp confirmDisconnect(ConfirmDisconnectReq req);

    /**
     * 获取跨频道音频uids
     */
    @Yrpc(functionName = "getCrossPkVoiceUid")
    GetCrossPkVoiceUidRsp getCrossPkVoiceUid(GetCrossPkVoiceUidReq req);

    /**
     * 添加跨频道音频uid
     */
    @Yrpc(functionName = "addCrossPkVoiceUid")
    AddCrossPkVoiceUidRsp addCrossPkVoiceUid(AddCrossPkVoiceUidReq req);

    /**
     * 删除跨频道音频uid
     */
    @Yrpc(functionName = "delCrossPkVoiceUid")
    DelCrossPkVoiceUidRsp delCrossPkVoiceUid(DelCrossPkVoiceUidReq req);

    /**
     * 设置惩罚结果
     */
    @Yrpc(functionName = "setPunishmentResult")
    SetPunishmentResultRsp setPunishmentResult(SetPunishmentResultReq req);

    /**
     * 获取惩罚配置列表
     */
    @Yrpc(functionName = "getPunishmentCfg")
    GetPunishmentCfgRsp getPunishmentCfg(GetPunishmentCfgReq req);

    /**
     * 获取多人PK本场嘉宾|主持贡献榜
     */
    @Yrpc(functionName = "getContributorRankList")
    GetContributorRankListRsp getContributorRankList(GetContributorRankListReq req);

    /**
     * 上报嘉宾是否支持 AR特效合并到视频流
     */
    @Yrpc(functionName = "reportArMixStatus")
    ReportArMixStatusRsp reportArMixStatus(ReportArMixStatusReq req);

    /**
     * 获取玩法关键信息
     */
    @Yrpc(functionName = "getKeyInfo")
    GetKeyInfoRsp getKeyInfo(GetKeyInfoReq req);

    /**
     * 获取跨厅pk匹配战况
     */
    @Yrpc(functionName = "getCrossPkBattleTeamRank")
    GetCrossPkBattleTeamRankRsp getCrossPkBattleTeamRank(GetCrossPkBattleTeamRankReq req);

    /**
     * 获取给定uid可以上座的空闲座位列表
     */
    @Yrpc(functionName = "getAllowOnSeatPos")
    GetAllowOnSeatPosRsp getAllowOnSeatPos(GetAllowOnSeatPosReq req);

    /**
     * 获取频道嘉宾位置列表
     */
    @Yrpc(functionName = "batchGetChannelGuest")
    BatchGetChannelGuestRsp batchGetChannelGuest(BatchGetChannelGuestReq req);

    /**
     * 批量获取活跃频道游戏会话基本信息
     */
    @Yrpc(functionName = "batchGetActiveChannelSessionInfo")
    BatchGetActiveChannelSessionInfoRsp batchGetActiveChannelSessionInfo(BatchGetActiveChannelSessionInfoReq req);
}