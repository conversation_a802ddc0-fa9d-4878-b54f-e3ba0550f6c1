package com.yy.hd.event.kafka.game;

import com.yy.hd.event.kafka.KafkaEvent;
import com.yy.hd.event.kafka.KafkaTopic;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/11/26 21:17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GameChangeEvent implements KafkaEvent {
    @Override
    public String topic() {
        return KafkaTopic.HD_GAME_CHANGE_TOPIC;
    }

    @Override
    public String key() {
        return key == null ? null : String.valueOf(key);
    }

    /** for kafka */
    private Object key;

    /** 变更类型 */
    public enum Type {
        /** 开启游戏 */
        START,
        /** 停止游戏(主动停止) */
        STOP,
        /** 惩罚 */
        PUNISHMENT,
        /** 自然终止 */
        FINISHED
    }

    /** 事件类型 */
    private Type type;

    /** type = START 的时候该属性有效 */
    private GameStartEvent gameStartEvent;
}
