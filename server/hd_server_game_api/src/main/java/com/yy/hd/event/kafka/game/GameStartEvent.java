package com.yy.hd.event.kafka.game;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/26 14:16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GameStartEvent {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Team {
        /**
         * 顶级频道sid
         */
        private long sid;

        /**
         * 子频道ssid
         */
        private long ssid;

        /**
         * 所属队伍，多人视频 不区分队伍， 团战和乱斗区分：0-橙队 1-蓝队
         */
        private int team;

        /**
         * 房间类型 0-交友 1-语音房
         */
        private int roomType;

        /**
         * 玩法类型
         */
        private int gameType;

        /**
         * 房间当前主持uid
         */
        private long compereUid;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TeamMember {
        /**
         * 所属队伍，多人视频 不区分队伍， 团战和乱斗区分：0-橙队 1-蓝队
         */
        private int team;

        /**
         * 成员角色，参考 hd_api.common.proto 中 role 定义 0-普通用户 1-主持 2-嘉宾 3-候选
         */
        private int role;

        /**
         * 位置序号, session_id + team + role + position 唯一
         */
        private int position;

        /**
         * 用户uid
         */
        private long uid;
    }

    /** 操作人uid */
    private long opUid;

    /** 游戏会话id */
    private long sessionId;

    /** 房间类型 0-交友 1-语音房 */
    private int roomType;

    /** 玩法类型 */
    private int gameType;

    /**
     * 本场游戏开始时间
     */
    private Date startTime;

    /**
     * 本场游戏市场，单位：秒
     */
    private long duration;

    /**
     * 游戏加时总时长
     */
    private long addDuration;

    /**
     * 本场游戏预期结束时间 对应 vf_auto_set_win_status， pk开始时间 + pk时长 + 结束后预留的出动画时长 (startTime + duration + add_duration) 就是一个时间戳
     */
    private Date exceptEndTime;

    /**
     * 扩展信息
     */
    private String expand;

    /** 队伍列表 */
    private List<Team> teamList;

    /** 队伍成员列表 */
    private List<TeamMember> teamMemberList;
}
