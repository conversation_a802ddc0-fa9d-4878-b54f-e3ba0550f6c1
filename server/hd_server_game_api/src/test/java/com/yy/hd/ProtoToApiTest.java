package com.yy.hd;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;

import java.io.*;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 将 proto 协议文件转成 API 接口定义
 *
 * <AUTHOR>
 * @since 2024/12/5 11:46
 */
public class ProtoToApiTest {

    /**
     * 项目根路径
     */
    protected static String projectRoot;
    protected static String moduleRoot;

    @Before
    public void ready() {
        String current = Objects.requireNonNull(ProtoToApiTest.class.getResource("")).getPath();
        projectRoot = current.substring(0, current.indexOf("hd_server_api"));
        moduleRoot = projectRoot + "hd_server_api";
        System.out.println("projectRoot: " + projectRoot);
        System.out.println("moduleRoot: " + moduleRoot);
    }

    class MethodInfo {
        public String funcName;
        public String reqType;
        public String rspType;
        public List<String> commentList;
    }

    class ServiceInfo {
        public String serviceName;
        public List<String> commentList;
        public boolean hasCommon;
        public List<MethodInfo> methodInfoList = new ArrayList<>();
    }

    @Test
    public void testGenHdGameYrpc() throws Exception {
        genYrpcApi("game/hd_game_yrpc.proto");
    }

    @Test
    public void testGenHdAuthYrpc() throws Exception {
        genYrpcApi("auth/hd_auth_yrpc.proto");
    }

    public void genYrpcApi(String protoRelationPath) throws Exception {
        String inputFilePath = String.format("%s/src/main/proto/%s",
                moduleRoot,
                protoRelationPath);

        List<String> lines = IOUtils.readLines(new FileReader(inputFilePath));

        // package 行
        String packageLine = null;
        String apiPackage = null;
        String modelPackage = null;
        String packageRegex = "^\\s*package\\s+(.*)\\.pb\\.(.*)\\s*;\\s*$";

        for (String line : lines) {
            if (line.matches(packageRegex)) {
                packageLine = line;
                String sunPkg = packageLine.replaceAll(packageRegex, "$2");
                apiPackage = "com.yy.hd.api.yrpc." + sunPkg;
                modelPackage = packageLine.replaceAll(packageRegex, "$1.pb.$2");
                break;
            }
        }
        String pbClassName = null;
        String classNameRegex = "^option\\s+java_outer_classname\\s*=\\s*\"(.*)\"\\s*;?\\s*$";
        for (String line : lines) {
            if (line.matches(classNameRegex)) {
                pbClassName = line.replaceAll(classNameRegex, "$1");
                break;
            }
        }

        List<ServiceInfo> serviceInfoList = new ArrayList<>();
        ServiceInfo curServiceInfo = null;
        List<String> commentLines = new ArrayList<>();
        String serviceRegex = "^\\s*service\\s+([a-zA-Z0-9]+)\\s+.*$";
        String methodRegex = "^\\s*rpc\\s+([a-zA-Z0-9]+)\\(\\s*([a-zA-Z0-9]+)\\s*\\)\\s*returns\\s*\\(([a-zA-Z0-9]+)\\).*$";
        String commentRegex = "^\\s*//\\s*(.*)\\s*$";
        for (String line : lines) {
            if (line.matches(commentRegex)) {
                commentLines.add(line.replaceAll(commentRegex, "$1"));
                continue;
            }

            if (line.isBlank() && !commentLines.isEmpty()) {
                commentLines = new ArrayList<>();
                continue;
            }

            if (line.matches(serviceRegex)) {
                curServiceInfo = new ServiceInfo();
                curServiceInfo.serviceName = line.replaceAll(serviceRegex, "$1");
                curServiceInfo.commentList = commentLines;
                commentLines = new ArrayList<>();
                serviceInfoList.add(curServiceInfo);
                continue;
            }

            if (curServiceInfo != null && line.startsWith("}")) {
                curServiceInfo = null;
                commentLines = new ArrayList<>();
                // service 定义结束
                continue;
            }

            // 检查是不是方法
            if (line.matches(methodRegex) && curServiceInfo != null) {
                MethodInfo methodInfo = new MethodInfo();
                methodInfo.commentList = commentLines;
                methodInfo.funcName = line.replaceAll(methodRegex, "$1");
                methodInfo.reqType = line.replaceAll(methodRegex, "$2");
                methodInfo.rspType = line.replaceAll(methodRegex, "$3");
                curServiceInfo.methodInfoList.add(methodInfo);
                if (methodInfo.rspType.equals("Result")) {
                    curServiceInfo.hasCommon = true;
                }
                commentLines = new ArrayList<>();
                continue;
            }
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm");
        Date now = new Date();
        // 生成代码
        for (var service : serviceInfoList) {
            StringBuilder builder = new StringBuilder();

            // 添加package
            builder.append("package ").append(apiPackage).append(";\n\n");

            // 添加import
            if (service.hasCommon) {
                builder.append("import static com.yy.hd.api.pb.Common.*;\n");
            }
            builder.append("import static ").append(modelPackage).append(".").append(pbClassName).append(".*;\n\n");
            builder.append("import org.apache.dubbo.common.annotation.Yrpc;\n\n");

            // 添加注释
            builder.append("/**\n");

            for (var comment : service.commentList) {
                builder.append(" * ").append(comment).append("\n");
            }

            builder.append("\n");
            builder.append(" * <AUTHOR>
            builder.append(" * @since ").append(format.format(now)).append("\n");
            builder.append(" */\n");

            builder.append("public interface ").append(service.serviceName).append(" {\n");
            for (var method : service.methodInfoList) {
                StringBuilder mb = new StringBuilder();
                mb.append("\n");
                if (!method.commentList.isEmpty()) {
                    mb.append("    /**\n");
                    for (var comment : method.commentList) {
                        mb.append("     * ").append(comment).append("\n");
                    }
                    mb.append("     */\n");
                }
                mb.append("    @Yrpc(functionName = \"").append(method.funcName).append("\")\n");
                mb.append("    ").append(method.rspType).append(" ").append(method.funcName).append("(").append(method.reqType).append(" req);").append("\n");
                builder.append(mb);
            }
            builder.append("}");

            String sourceCode = builder.toString();
            System.out.println(sourceCode);

            // 输出文件夹
            String folder = String.format("%s/src/main/java/%s", moduleRoot, apiPackage.replace(".", "/"));
            File f = new File(folder);
            if (!f.exists()) {
                f.mkdirs();
            }
            // 输出到文件
            String path = String.format("%s/src/main/java/%s/%s.java", moduleRoot, apiPackage.replace(".", "/"), service.serviceName);
            IOUtils.write(sourceCode, new FileOutputStream(path), Charset.defaultCharset());
            System.out.println("文件写入成功： " + path);
        }
    }
}
