package com.yy.hd.api.yrpc.game.match;

import com.yy.hd.api.pb.Common;
import com.yy.hd.api.pb.game.match.GameMatchPb;
import org.apache.dubbo.common.annotation.Yrpc;

/**
 * <AUTHOR>
 * @since 2024/11/29 18:47
 */
public interface HdCrossBizPkServerProvider {

    /**
     * 跨业务邀请匹配成功
     */
    @Yrpc(functionName = "crossBizPkInviteMatched")
    Common.Result crossBizPkInviteMatched(GameMatchPb.CrossBizPkInviteMatchedReq req);

    /**
     * 检查是否正在进行跨业务PK
     */
    @Yrpc(functionName = "checkCrossBizPk")
    GameMatchPb.CheckCrossBizPkRsp checkCrossBizPk(GameMatchPb.CheckCrossBizPkReq req);
}
