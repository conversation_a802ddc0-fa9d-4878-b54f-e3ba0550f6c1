package com.yy.hd.api.yrpc.game.match;

import com.yy.hd.api.pb.Common;
import org.apache.dubbo.common.annotation.Yrpc;

import static com.yy.hd.api.pb.game.match.GameMatchPb.InviteMatchConfirmReq;
import static com.yy.hd.api.pb.game.match.GameMatchPb.InviteMatchReq;

/**
 * <AUTHOR>
 * @since 2024/11/29 18:47
 */
public interface HdGameMatchServerProvider {

    /**
     * 邀请匹配
     */
    @Yrpc(functionName = "inviteMatch")
    Common.Result inviteMatch(InviteMatchReq req);

    /**
     * 邀请匹配
     */
    @Yrpc(functionName = "inviteMatchConfirm")
    Common.Result inviteMatchConfirm(InviteMatchConfirmReq req);
}
