syntax = "proto3";
package com.yy.hd.api.pb.game.match;
option java_outer_classname = "GameMatchPb";

import "common.proto";
import "game_match/game_match_common.proto";
import "game_match/game_match_event.proto";

// 邀请匹配请求
message InviteMatchReq {
  com.yy.hd.api.pb.game.match.common.InviteInfo    invite_info = 1;
}

// 邀请匹配确认请求
message InviteMatchConfirmReq {
  com.yy.hd.api.pb.game.match.common.InviteInfo    invite_info = 1;
  repeated int64 uid_list = 2; // 接受时的嘉宾列表  多人视频乱斗 最多3人
  int32    confirm        = 3; // 0-拒绝 1-同意
}

// 通知跨业务pk匹配成功
message CrossBizPkInviteMatchedReq {
  int64 gameDuration                = 1; // 游戏时长，单位：秒
  PkPlayer inviter                  = 2; // 邀请者
  PkPlayer invitee                  = 3; // 被邀请者
  string expand                     = 4; // 扩展信息
}

// 检查给定频道是否正在跨业务pk
message CheckCrossBizPkReq {
  int64 sid   = 1;
  int64 ssid  = 2;
}

message CheckCrossBizPkRsp {
  int32 ret       = 1; // 0--成功  1--失败
  string msg      = 2; // 错误信息
  bool crossBizPk = 3; // 是否正在跨业务pk中
}

// hd-game-match 服务, s2sname: hd_game_match_server_yrpc
service HdGameMatchServerProvider {
  // 邀请匹配
  rpc inviteMatch(InviteMatchReq) returns (Result);

  // 邀请结果确认
  rpc inviteMatchConfirm(InviteMatchConfirmReq) returns (Result);
}

// hd-game-match 跨业务pk, s2sname: hd_game_match_server_yrpc
service HdCrossBizPkServerProvider {
  // 跨业务pk邀请匹配成功
  rpc crossBizPkInviteMatched(CrossBizPkInviteMatchedReq) returns (Result);

  // 检查是否正在进行跨业务PK
  rpc checkCrossBizPk(CheckCrossBizPkReq) returns(CheckCrossBizPkRsp);
}