syntax = "proto3";
package com.yy.hd.api.pb.game.match;

import "event.proto";

// 匹配成功的模式
enum MatchedMode {
  INVITE = 0; // 邀请模式
  MATCH  = 1; // 匹配模式
}

// 玩家状态
enum PlayerStatus {
  TRY_READY       = 0; // 等待ready
  READY_OK        = 1; // ready 成功, 等待开始pk
  START_OK        = 2; // 开始pk成功
  READY_FAILED    = 3; // ready 失败
  READY_TIMEOUT   = 4; // ready 超时
  START_FAILED    = 5; // 开始pk失败
  START_TIMEOUT   = 6; // 开始pk超时
  PK_TERMINATE    = 7; // PK终止了
  PK_GIVE_UP      = 8; // PK投降了
}

// 跨业务PK终止类型
enum TerminateType {
  FAIL_START    = 0; // 开始pk失败
  GIVE_UP       = 1; // 有一方投降
  NORMAL_END    = 2; // 正常结束(倒计时结束)
}

// 玩家信息
message PkPlayer {
  int64 uid           = 1; // uid
  int64 imid          = 2; // yy号
  int64 asid          = 3; // 所在短位asid
  int64 sid           = 4; // 所在sid
  int64 ssid          = 5; // 所在ssid
  string nick         = 6; // 昵称
  string avatar       = 7; // 头像
  int32 bizType       = 8; // 房间业务类型，1-交友 2-聊天室 3-基础房
  string channelName  = 9; // 频道名称 当 biz_type = 2 的时候表示聊天室房间名称
  string expand       = 10; // 扩展信息
  PlayerStatus status = 11; // 玩家状态
}

// 跨业务PK匹配到的事件 （各个业务方去消费这个事件，然后占用资源进行ready pk）
message CrossBizPkMatchedEvent {
  option (event.topic)              = 'hd_cross_biz_pk_matched_event';
  string seqId                      = 1; // 事件流水号
  int64 timestamp                   = 2; // 事件时间戳(毫秒)
  string matchId                    = 3; // 匹配ID，唯一序列号
  int64 matchedTime                 = 4; // 匹配成功时间戳（毫秒）
  MatchedMode matchedMode           = 5; // 匹配成功的模式
  int64 gameDuration                = 6; // 游戏时长，单位：秒
  PkPlayer from                     = 7; // 如果 matchedMode = INVITE, 那么 from 就是邀请者，to就是被邀请者
  PkPlayer to                       = 8; // 另一个玩家信息
  string expand                     = 9; // 扩展信息
}

// 跨业务PK-业务ready pk 结果事件 （业务不消费，hd-game-match 消费，然后等待都有ready结果后发布 BothReadyResult 结果）
message CrossBizPkReadyResultEvent {
  option (event.topic)              = 'hd_cross_biz_pk_ready_result_event';
  string seqId                      = 1; // 事件流水号
  string matchId                    = 2; // 匹配ID，唯一序列号
  int64 timestamp                   = 3; // 事件时间戳(毫秒)
  PkPlayer player                   = 4; // 本次 ready pk 的业务 player
  string expand                     = 5; // 扩展信息
}

// 跨业务PK-所有业务都ready pk 结果事件 （hd-game-match 发布， 业务消费，如果成功，业务发布 真正 start pk，并发布start pk 结果事件）
message CrossBizPkBothReadyResultEvent {
  option (event.topic)              = 'hd_cross_biz_pk_both_ready_result_event';
  string seqId                      = 1; // 事件流水号
  int64 timestamp                   = 2; // 事件时间戳(毫秒)
  string matchId                    = 3; // 匹配ID，唯一序列号
  int64 matchedTime                 = 4; // 匹配成功时间戳（毫秒）
  MatchedMode matchedMode           = 5; // 匹配成功的模式
  int64 gameDuration                = 6; // 游戏时长，单位：秒
  PkPlayer from                     = 7; // 如果 matchedMode = INVITE, 那么 from 就是邀请者，to就是被邀请者
  PkPlayer to                       = 8; // 另一个玩家信息
  string expand                     = 9; // 扩展信息
  int32 allowStartPk                = 10; // 是否真正可以开始pk了，0-不能（from|to有ready失败的） 1-可以
}

// 跨业务PK-业务真正start pk 结果事件 （业务不消费，hd-game-match 消费）
message CrossBizPkStartResultEvent {
  option (event.topic)              = 'hd_cross_biz_pk_start_result_event';
  string seqId                      = 1; // 事件流水号
  int64 timestamp                   = 2; // 事件时间戳(毫秒)
  string matchId                    = 3; // 匹配ID，唯一序列号
  PkPlayer player                   = 4; // 本次 start pk 的业务 player
  string expand                     = 5; // 扩展信息
}

// 跨业务PK-一些异常终止pk的情况(比如检测到一方失效了，又比如只有一方开始成功了)
message CrossBizPkTerminateEvent {
  option (event.topic)              = 'hd_cross_biz_pk_terminate_event';
  string seqId                      = 1; // 事件流水号
  int64 timestamp                   = 2; // 事件时间戳(毫秒)
  string matchId                    = 3; // 匹配ID，唯一序列号
  int64 matchedTime                 = 4; // 匹配成功时间戳（毫秒）
  MatchedMode matchedMode           = 5; // 匹配成功的模式
  int64 gameDuration                = 6; // 游戏时长，单位：秒
  PkPlayer from                     = 7; // 如果 matchedMode = INVITE, 那么 from 就是邀请者，to就是被邀请者 查看status是否为 PK_TERMINATE
  PkPlayer to                       = 8; // 另一个玩家信息 查看status是否为 PK_TERMINATE
  string expand                     = 9; // 扩展信息
  TerminateType terminateType       = 10; // 终止类型
  string remark                     = 11; // 一些备注信息
}

// 跨业务PK，结束事件(参与跨业务pk的业务方结束（可能是自然结束、可能是投降），其他的会收到 CrossBizPkTerminateEvent)
message CrossBizPkEndEvent {
  option (event.topic)              = 'hd_cross_biz_pk_end_event';
  string seqId                      = 1; // 事件流水号
  int64 timestamp                   = 2; // 事件时间戳(毫秒)
  string matchId                    = 3; // 匹配ID，唯一序列号
  PkPlayer player                   = 4; // 本次 发起投降的业务 player（至少包含 sid, ssid, uid, bizType）
  TerminateType endType             = 5; // 结束类型
  string expand                     = 6; // 扩展信息
  string remark                     = 7; // 一些备注信息
}