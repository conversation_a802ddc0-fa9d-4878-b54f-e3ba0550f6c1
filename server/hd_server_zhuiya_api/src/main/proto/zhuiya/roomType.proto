syntax = "proto3";

package cn.yy.ent.zhuiya.gen.pb.roomtype;
option java_outer_classname = "RoomType";



message SetRoomTypeReq {
  int64 sid = 1;
  int64 ssid = 2;
  int32 roomTypeId = 3; //房间类型
  int64 uid = 4;
}

message BaseRsp {
  int32 result = 1;
  string msg = 2;
}

// 获取房间类型id zy_room_type.room_type_id
message GetRoomTypeIdReq {
  int64 sid = 1;
  int64 ssid = 2;
}

message GetRoomTypeIdRsp {
  int32 result = 1;
  string msg = 2;
  int32 roomTypeId = 3; // <= 0 表示无效
}

message GetRoomTypeCategoryReq {
  int64 sid = 1;
  int64 ssid = 2;
}

message GetRoomTypeCategoryRsp {
  int32 result = 1;
  string msg = 2;
  int32 roomTypeCategory = 3; // <= 0 表示无效
  int32 subRoomTypeCategory = 4; // <= 0 表示无效
}

// 清理房间类型
message ClearRoomTypeReq {
  int64 sid = 1;
  int64 ssid = 2;
}