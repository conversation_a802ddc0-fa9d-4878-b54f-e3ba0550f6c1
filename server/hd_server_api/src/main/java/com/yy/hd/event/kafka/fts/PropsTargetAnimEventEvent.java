package com.yy.hd.event.kafka.fts;

import com.yy.hd.event.kafka.KafkaEvent;
import com.yy.hd.event.kafka.KafkaTopic;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date: 2024/12/18 15:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PropsTargetAnimEventEvent implements KafkaEvent {
    @Override
    public String topic() {
        return KafkaTopic.HD_GAME_PROPS_TARGET_ANIM_TOPIC;
    }

    @Override
    public String key() {
        return String.valueOf(sessionId);
    }

    /**
     * 游戏会话id
     */
    private Long sessionId;
    /**
     * 房间类型 0-交友 1-语音房
     */
    private int roomType;
    /**
     * 玩法类型
     */
    private int gameType;
    /**
     * 被踢嘉宾uid
     */
    private Long uid;
    /**
     * 频道sid
     */
    private Long sid;
    /**
     * 频道ssid
     */
    private Long ssid;
    /**
     * 营收ID
     */
    private Long propsId;
    /**
     * 数量
     */
    private Long count;
    /**
     * 时间戳 秒
     */
    private Long timestamp;
}
