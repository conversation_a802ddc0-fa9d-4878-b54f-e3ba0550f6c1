namespace java com.yy.hd.api.thrift.broadcast

struct MobsrvBcReq {
    1:i32 max;
    2:i32 min;
    3:list<i64> uids;
    4:i64 sid;
    5:i64 ssid;
    6:string body;
    7:string seqId;
    8:string context;
}

struct MobsrvBcRsp {
    // 返回码，0-成功
    1:i32    code;
    2:string message;
    3:string seqId;
}

struct ServiceApiGatewayBcReq {
    1:i64 groupType;
    2:string s2sname;
    3:string s2stoken;
    4:string s2skey;
    5:i64 groupId;
    6:i32 max;
    7:i32 min;
    8:string body;
    9:string seqId;
    10:string context;
}

struct ServiceApiGatewayBcRsp {
    // 返回码，0-成功
    1:i32    code;
    2:string message;
    3:string seqId;
}

service TMobsrvBcService {
    /**
    * 测试链接
    */
    void pingMobsrv();

    /**
    * 广播
    **/
    MobsrvBcRsp broadcastMobsrv(1:MobsrvBcReq req);

}

service TServiceApiGatewayBcService {

    /**
    * 测试链接
    */
    void pingSvc();

    /**
    * 组播
    **/
    ServiceApiGatewayBcRsp broadcastSvc(1: ServiceApiGatewayBcReq req);

    /**
    * 组播
    **/
    ServiceApiGatewayBcRsp broadcastSvcBySa(1: ServiceApiGatewayBcReq req);
}

service RateLimitService {

    /**
    * 获取令牌，true表示通行
    * @param context 服务名
    * @param key 令牌桶的key
    * @param timeoutMS 获取令牌时允许等待的毫秒数
    **/
    bool canPass(1:string context, 2:string resource, 3:i32 timeoutMS);
}