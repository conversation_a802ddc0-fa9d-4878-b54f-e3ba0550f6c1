syntax = "proto3";
package com.yy.hd.api.pb.seat;

import "common.proto";

message OnSeatReq {
  int64 sid = 1;  // 频道sid
  int64 ssid = 2; // 频道ssid
  int64 uid = 3;  // uid
  int32 pos = 4;  // 座位号 主持默认0
  int32 role = 5; // 角色 必传 大于0
}
message OnSeatResp {
  Result result = 1;
  int64 sitTime = 2; // 上座时间 毫秒时间戳
}

message OffSeatReq {
  int64 sid = 1;  // 频道sid
  int64 ssid = 2; // 频道ssid
  int64 uid = 3;  // 用户uid
  int32 pos = 4;  // 座位号 默认0号是主持
  int32 role = 5; // 角色 必传 大于0
}
message OffSeatResp {
  Result result = 1;
  int64 sitTime = 2; // 上座时间 毫秒时间戳
}

message CleanRoomSeatReq {
  int64 sid = 1;  // 频道sid
  int64 ssid = 2; // 频道ssid
  int32 role = 3; // 角色 可选 大于0时删除指定role
}
message CleanRoomSeatResp {
  Result result = 1;
  repeated SeatInfo seats = 2;  // 下座成功列表
}

message GetSeatListReq {
  int64 sid = 1;   // 频道sid
  int64 ssid = 2; // 频道ssid
  int32 role = 3; // 角色 可选 大于0时查询指定role
}
message GetSeatListResp {
  Result result = 1;
  repeated SeatInfo seats = 2;  // 座位列表
}
message SeatInfo {
  int64 uid = 1 ;                   // 用户uid
  int32 pos = 2;                    // 用户座位号
  int32 role = 3;                   // 角色
  int64 sitTime = 4;                // 上座时间
}

message GetSeatUserReq {
  int64 sid = 1;  // 频道sid
  int64 ssid = 2; // 频道ssid
  int32 pos = 3;  // 座位号
  int32 role = 4; // 角色
}
message GetSeatUserResp {
  Result result = 1;
  int64 uid = 2; // 座位信息
  int32 role = 3; // 角色
}

message GetUserSeatReq {
  int64 sid = 1;  // 频道sid
  int64 ssid = 2; // 频道ssid
  int64 uid = 3;  // 用户uid
}

message GetUserSeatResp {
  Result result = 1;
  int32 pos = 2;  // 座位号
  int32 role = 3; // 角色
}

message GetAllSeatsReq {
  int64 id = 1;    // 遍历的id 默认从0开始
  int64 limit = 2; // 查询数量
  int32 role = 3;  // 角色 可选 大于0时查询指定role
}
message GetAllSeatsResp {
  Result result = 1;
  repeated ChInfo chs = 2;  // 座位列表
}
message ChInfo {
  int64 sid = 1 ;  // 用户uid
  int64 ssid = 2;  // 用户座位号
}

// 批量获取频道对应座位用户信息
message BatchGetSeatReq {
  repeated ChInfo chList = 1;
  int32 role = 2;
}
message ChSeatInfo {
  ChInfo ch = 1;
  repeated SeatInfo seats = 2;
}

message BatchGetSeatResp {
  Result result = 1;
  repeated ChSeatInfo chSeats = 2;
}

service SeatServiceYrpc {
  // 上座
  rpc onSeat(OnSeatReq) returns (OnSeatResp);

  // 下座
  rpc offSeat(OffSeatReq) returns (OffSeatResp);

  // 清空房间座位列表
  rpc cleanRoomSeat(CleanRoomSeatReq) returns (CleanRoomSeatResp);

  // 获取座位列表
  rpc getSeatList(GetSeatListReq) returns (GetSeatListResp);

  // 获取座位的用户信息
  rpc getSeatUser(GetSeatUserReq) returns (GetSeatUserResp);

  // 获取用户的座位信息
  rpc getUserSeat(GetUserSeatReq) returns (GetUserSeatResp);

  // 获取所有房间
  rpc getAllSeats(GetAllSeatsReq) returns (GetAllSeatsResp);

  // 批量获取频道对应座位用户信息
  rpc batchGetSeat(BatchGetSeatReq) returns (BatchGetSeatResp);
}