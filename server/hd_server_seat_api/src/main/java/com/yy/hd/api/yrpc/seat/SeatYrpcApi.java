package com.yy.hd.api.yrpc.seat;

import org.apache.dubbo.common.annotation.Yrpc;
import com.yy.hd.api.pb.seat.SeatYrpc;

/**
 * s2s: hd_seat_yrpc
 */
public interface SeatYrpcApi {

	@Yrpc(functionName="onSeat")
	SeatYrpc.OnSeatResp onSeat (SeatYrpc.OnSeatReq req);

	@Yrpc(functionName="offSeat")
	SeatYrpc.OffSeatResp offSeat (SeatYrpc.OffSeatReq req);

	@Yrpc(functionName="cleanRoomSeat")
	SeatYrpc.CleanRoomSeatResp cleanRoomSeat (SeatYrpc.CleanRoomSeatReq req);

	@Yrpc(functionName="getSeatList")
	SeatYrpc.GetSeatListResp getSeatList (SeatYrpc.GetSeatListReq req);

	@Yrpc(functionName="getSeatUser")
	SeatYrpc.GetSeatUserResp getSeatUser (SeatYrpc.GetSeatUserReq req);

	@Yrpc(functionName="getUserSeat")
	SeatYrpc.GetUserSeatResp getUserSeat (SeatYrpc.GetUserSeatReq req);

	@Yrpc(functionName="getAllSeats")
	SeatYrpc.GetAllSeatsResp getAllSeats (SeatYrpc.GetAllSeatsReq req);

	@Yrpc(functionName="batchGetSeat")
	SeatYrpc.BatchGetSeatResp batchGetSeat (SeatYrpc.BatchGetSeatReq req);
}