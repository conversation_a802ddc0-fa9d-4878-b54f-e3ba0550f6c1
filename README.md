### 开发约定
1. 最小化原则，将协议拆分成最小化、按业务不可再分的最小单元，作为一个单独的模块
2. 新增协议文件，不要和原来的放到一起，如果需要以来其他模块，那么 pom.xml 指定即可

### 项目结构
1. common: 公共协议 一般给其他模块依赖使用，不涉及具体业务服务，定义一些基础的协议
2. client: 给前端/客户端的协议
3. server: 互动业务发布服务端协议，比如服务端发布的yrpc/thrift/kafka等
4. external: 非互动业务的服务协议 yrpc/thrift/yyp 等
5. fts：交友业务相关的协议，一般都是 thrift，少量可能是 yrpc

###
1. hd_client_api: 前端/客户端上下行协议(http/service通道)
2. hd_server_api: 互动后端服务协议(yrpc/thrift/kafka)
3. hd_external_api: 非互动服务协议(yrpc/thrift)

###
1. 请求以Req结尾
2. 响应以Resp结尾
3. 广播以Cast结尾

### 升级版本号

mvn versions:set -DnewVersion=1.0.0-SNAPSHOT

该命令可以将父子模块全部改成 1.0.0-SNAPSHOT

删除所有的版本备份文件
find . -name "*.versionsBackup" -delete

### 构建发布

mvn clean install deploy -am -pl hd_external_api -DskipTests=true

